using MediatR;
using Microsoft.AspNetCore.Mvc;
using VirtuManagerBackend.Application.Api.Response.Jwt;
using VirtuManagerBackend.Application.Features.Authorization.Commands.Authorize;
using VirtuManagerBackend.Presentation.Controllers.Base;

namespace VirtuManagerBackend.Presentation.Controllers;

[ApiController]
[Route("/Api/[controller]")]
public class AuthorizationController : MediatorControllerBase
{
    public AuthorizationController(IMediator mediator) : base(mediator) { }

    [HttpGet("Authorize")]
    public async Task<IActionResult> Authorize(string login, string password)
        => await ExecuteCommand<AuthorizeCommand, JwtTokenResponse>(new AuthorizeCommand(login, password));
}