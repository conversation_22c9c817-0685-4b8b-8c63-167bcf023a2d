using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using VirtuManagerBackend.Application.Api.Dto.User;
using VirtuManagerBackend.Application.Api.Requests;
using VirtuManagerBackend.Application.Api.Response.PaginationResponse;
using VirtuManagerBackend.Application.Api.Response.User;
using VirtuManagerBackend.Application.Features.Users.Commands.AddUser;
using VirtuManagerBackend.Application.Features.Users.Commands.DeleteUserById;
using VirtuManagerBackend.Domain.Db.Tables.UserTable;
using VirtuManagerBackend.Features.Users.Commands.GetUserById;
using VirtuManagerBackend.Features.Users.Commands.GetUsersPage;
using VirtuManagerBackend.Presentation.Controllers.Base;

[ApiController]
[Route("/Api/[controller]")]
public class UsersController : MediatorControllerBase
{
	public UsersController(IMediator mediator) : base(mediator) { }

	[Authorize(Roles = nameof(UserRole.Admin))]
	[HttpGet("GetUserById/{id}")]
	public async Task<IActionResult> GetUserById(int id)
		=> await ExecuteCommand<GetUserByIdCommand, User>(new GetUserByIdCommand(id));

	[Authorize(Roles = nameof(UserRole.Admin))]
	[HttpDelete("DeleteUserById")]
	public async Task<IActionResult> DeleteUserById(IdRequestBase request)
		=> await ExecuteCommand<DeleteUserByIdCommand, UserDeletedResponse>(new DeleteUserByIdCommand(request.Id, HttpContext.User));

	[Authorize(Roles = nameof(UserRole.Admin))]
	[HttpPost("AddUser")]
	public async Task<IActionResult> AddUser(CreateUserDto userDto)
		=> await ExecuteCommand<AddUserCommand, UserAddedResponse>(new AddUserCommand(userDto));

	[Authorize(Roles = nameof(UserRole.Admin))]
	[HttpGet("GetUsersPage")]
	public async Task<IActionResult> GetUsersPage(
		[FromQuery] int pageNumber = 1,
		[FromQuery] int pageSize = 10)
		=> await ExecuteCommand<GetUsersPageCommand, PaginationResponse<User>>(
			new GetUsersPageCommand(pageNumber, pageSize));
}