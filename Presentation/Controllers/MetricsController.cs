using Microsoft.AspNetCore.Mvc;
using VirtuManagerBackend.Application.Services;

namespace VirtuManagerBackend.Presentation.Controllers;

[ApiController]
[Route("/Api/[controller]")]
public class MetricsController : ControllerBase
{
    private readonly InfluxDbService _influxDbService;
    
    public MetricsController(InfluxDbService influxDbService)
    {
        _influxDbService = influxDbService;
    }

    [HttpGet("VirtualMachine/{vmName}/cpu")]
    public async Task<IActionResult> GetCpuMetrics(string vmName, [FromQuery] string range = "-12h")
    {
        var metrics = await _influxDbService.QueryVirtualMachineMetrics("vms", "CpuLoad", vmName, range);
        return Ok(metrics);
    }
}