using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using VirtuManagerBackend.Application.Api.Dto.VirtualMachineConfiguration;
using VirtuManagerBackend.Application.Api.Requests;
using VirtuManagerBackend.Application.Api.Response.VirtualMachineConfiguration;
using VirtuManagerBackend.Domain.Db.Tables.UserTable;
using VirtuManagerBackend.Domain.Db.Tables.VirtualMachineConfigurationTable;
using VirtuManagerBackend.Features.VirtualMachineConfigurations.Commands.AddConfiguration;
using VirtuManagerBackend.Features.VirtualMachineConfigurations.Commands.DeleteConfiguration;
using VirtuManagerBackend.Features.VirtualMachineConfigurations.Commands.GetConfigurationById;
using VirtuManagerBackend.Presentation.Controllers.Base;

namespace VirtuManagerBackend.Presentation.Controllers;

[ApiController]
[Route("/Api/[controller]")]
public class VirtualMachineConfigurationsController : MediatorControllerBase
{
    public VirtualMachineConfigurationsController(IMediator mediator) : base(mediator) { }

    [Authorize(Roles = nameof(UserRole.Admin))]
    [HttpPost("AddConfiguration")]
    public async Task<IActionResult> AddConfiguration(AddVirtualMachineConfigurationDto virtualMachineConfigurationDto)
        => await ExecuteCommand<AddConfigurationCommand, VirtualMachineConfigurationCreatedResponse>(
            new AddConfigurationCommand(virtualMachineConfigurationDto));

    [Authorize(Roles = nameof(UserRole.Admin))]
    [HttpGet("DeleteConfiguration")]
    public async Task<IActionResult> DeleteConfiguration([FromQuery] IdRequestBase request)
        => await ExecuteCommand<DeleteConfigurationCommand, VirtualMachineConfigurationDeletedResponse>(
            new DeleteConfigurationCommand(request.Id));

    [Authorize(Roles = nameof(UserRole.Admin))]
    [HttpGet("GetConfigurationById")]
    public async Task<IActionResult> GetConfigurationById([FromQuery] IdRequestBase idRequestBase)
        => await ExecuteCommand<GetConfigurationByIdCommand, VirtualMachineConfiguration>(
            new GetConfigurationByIdCommand(idRequestBase.Id));
}