using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using VirtuManagerBackend.Application.Api.Dto.VirtualDisk;
using VirtuManagerBackend.Application.Api.Requests;
using VirtuManagerBackend.Application.Api.Response.PaginationResponse;
using VirtuManagerBackend.Application.Api.Response.VirtualDisks;
using VirtuManagerBackend.Domain.Db.Tables.UserTable;
using VirtuManagerBackend.Domain.Db.Tables.VirtualDiskTable;
using VirtuManagerBackend.Features.VirtualDisks.Commands.AddVirtualDisk;
using VirtuManagerBackend.Features.VirtualDisks.Commands.DeleteVirtualDisk;
using VirtuManagerBackend.Features.VirtualDisks.Commands.GetVirtualDiskById;
using VirtuManagerBackend.Features.VirtualDisks.Commands.GetVirtualDisksPage;
using VirtuManagerBackend.Features.VirtualDisks.Commands.UpdateVirtualDisk;
using VirtuManagerBackend.Presentation.Controllers.Base;

namespace VirtuManagerBackend.Presentation.Controllers;

[ApiController]
[Route("/Api/[controller]")]
public class VirtualDisksController : MediatorControllerBase
{
    public VirtualDisksController(IMediator mediator) : base(mediator) { }

    [Authorize(Roles = nameof(UserRole.Admin))]
    [HttpGet("GetVirtualDiskById/{id}")]
    public async Task<IActionResult> GetVirtualDiskById(int id)
        => await ExecuteCommand<GetVirtualDiskByIdCommand, VirtualDisk>(new GetVirtualDiskByIdCommand(id));

    [Authorize(Roles = nameof(UserRole.Admin))]
    [HttpPost("AddVirtualDisk")]
    public async Task<IActionResult> AddVirtualDisk(AddVirtualDiskDto virtualDiskDto)
        => await ExecuteCommand<AddVirtualDiskCommand, AddVirtualDiskResponse>(
            new AddVirtualDiskCommand(virtualDiskDto));

    [Authorize(Roles = nameof(UserRole.Admin))]
    [HttpDelete("DeleteVirtualDisk")]
    public async Task<IActionResult> DeleteVirtualDisk(IdRequestBase request)
        => await ExecuteCommand<DeleteVirtualDiskCommand, VirtualDiskDeletedResponse>(
            new DeleteVirtualDiskCommand(request.Id));

    [Authorize(Roles = nameof(UserRole.Admin))]
    [HttpPatch("UpdateVirtualDisk")]
    public async Task<IActionResult> UpdateVirtualDisk(UpdateVirtualDiskDto virtualDiskDto)
        => await ExecuteCommand<UpdateVirtualDiskCommand, UpdateVirtualDiskResponse>(
            new UpdateVirtualDiskCommand(virtualDiskDto));

    [Authorize(Roles = nameof(UserRole.Admin))]
    [HttpGet("GetVirtualDisksPage")]
    public async Task<IActionResult> GetVirtualDisksPage(
        [FromQuery] int pageNumber = 1,
        [FromQuery] int pageSize = 10)
        => await ExecuteCommand<GetVirtualDisksPageCommand, PaginationResponse<VirtualDisk>>(
            new GetVirtualDisksPageCommand(pageNumber, pageSize));
}