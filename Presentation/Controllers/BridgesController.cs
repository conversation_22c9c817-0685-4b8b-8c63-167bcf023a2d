using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using VirtuManagerBackend.Application.Api.Dto.Bridge;
using VirtuManagerBackend.Application.Api.Response.Bridges;
using VirtuManagerBackend.Application.Api.Response.PaginationResponse;
using VirtuManagerBackend.Domain.Db.Tables.BridgesTable;
using VirtuManagerBackend.Domain.Db.Tables.UserTable;
using VirtuManagerBackend.Features.Bridges.Commands.AddBridge;
using VirtuManagerBackend.Features.Bridges.Commands.GetBridgeById;
using VirtuManagerBackend.Features.Bridges.Commands.GetBridgesPage;
using VirtuManagerBackend.Presentation.Controllers.Base;

namespace VirtuManagerBackend.Presentation.Controllers;

[ApiController]
[Route("/Api/[controller]")]
public class BridgesController : MediatorControllerBase
{
    public BridgesController(IMediator mediator) : base(mediator) { }

    [Authorize(Roles = nameof(UserRole.Admin))]
    [HttpGet("GetBridgeById/{id}")]
    public async Task<IActionResult> GetBridgeById(int id)
        => await ExecuteCommand<GetBridgeByIdCommand, Bridge>(new GetBridgeByIdCommand(id));

    [Authorize(Roles = nameof(UserRole.Admin))]
    [HttpPost("AddBridge")]
    public async Task<IActionResult> AddBridge(AddBridgeDto addBridgeDto)
        => await ExecuteCommand<AddBridgeCommand, AddBridgeResponse>(new AddBridgeCommand(addBridgeDto));

    [Authorize(Roles = nameof(UserRole.Admin))]
    [HttpGet("GetBridgesPage")]
    public async Task<IActionResult> GetBridgesPage([FromQuery] int pageNumber = 1, [FromQuery] int pageSize = 10)
        => await ExecuteCommand<GetBridgesPageCommand, PaginationResponse<Bridge>>(new GetBridgesPageCommand(pageNumber, pageSize));
}