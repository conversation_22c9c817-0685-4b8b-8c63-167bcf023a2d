using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using VirtuManagerBackend.Application.Api.Dto.Repository;
using VirtuManagerBackend.Application.Api.Requests;
using VirtuManagerBackend.Application.Api.Response.PaginationResponse;
using VirtuManagerBackend.Application.Api.Response.Repositories;
using VirtuManagerBackend.Domain.Db.Tables.ImageRepositoryTable;
using VirtuManagerBackend.Domain.Db.Tables.UserTable;
using VirtuManagerBackend.Features.ImageRepository.Commands.AddRepository;
using VirtuManagerBackend.Features.ImageRepository.Commands.GetRepositoryById;
using VirtuManagerBackend.Features.ImageRepository.Commands.GetRepositoryPage;
using VirtuManagerBackend.Presentation.Controllers.Base;

namespace VirtuManagerBackend.Presentation.Controllers;

[ApiController]
[Route("/Api/[controller]")]
public class ImageRepositoriesController : MediatorControllerBase
{
    //TODO Привести все геты к одному формату параметров
    public ImageRepositoriesController(IMediator mediator) : base(mediator) { }

    [Authorize(Roles = nameof(UserRole.Admin))]
    [HttpGet("GetRepositoryById")]
    public async Task<IActionResult> GetRepositoryById([FromQuery] IdRequestBase request)
        => await ExecuteCommand<GetRepositoryByIdCommand, RemoteImageRepository>(new GetRepositoryByIdCommand(request.Id));

    [HttpPost("AddRepository")]
    [Authorize(Roles = nameof(UserRole.Admin))]
    public async Task<IActionResult> AddRepository(AddRepositoryDto repositoryDto)
        => await ExecuteCommand<AddRepositoryCommand, RepositoryCreatedResponse>(new AddRepositoryCommand(repositoryDto));

    [Authorize(Roles = nameof(UserRole.Admin))]
    [HttpGet("GetRepositoryPage")]
    public async Task<IActionResult> GetRepositoryPage(
        [FromQuery] int pageNumber = 1,
        [FromQuery] int pageSize = 10)
        => await ExecuteCommand<GetRepositoryPageCommand, PaginationResponse<RemoteImageRepository>>(
            new GetRepositoryPageCommand(pageNumber, pageSize));
}