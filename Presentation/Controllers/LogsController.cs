using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using VirtuManagerBackend.Application.Api.Response.PaginationResponse;
using VirtuManagerBackend.Domain.Db.Tables.LogEntryTable;
using VirtuManagerBackend.Domain.Db.Tables.UserTable;
using VirtuManagerBackend.Features.Logs.Commands.GetLogsPage;
using VirtuManagerBackend.Presentation.Controllers.Base;
using LogLevel = VirtuManagerBackend.Domain.Db.Tables.LogEntryTable.LogLevel;

namespace VirtuManagerBackend.Presentation.Controllers;

[ApiController]
[Route("/Api/[controller]")]
public class LogsController : MediatorControllerBase
{
    public LogsController(IMediator mediator) : base(mediator) { }

    [Authorize(Roles = nameof(UserRole.Admin))]
    [HttpGet("GetLogsPage")]
    public async Task<IActionResult> GetLogsPage(
        [FromQuery] int pageNumber = 1,
        [FromQuery] int pageSize = 10,
        [FromQuery] LogType? type = null,
        [FromQuery] LogLevel? level = null)
        => await ExecuteCommand<GetLogsPageCommand, LogsPaginationResponse>(new GetLogsPageCommand(pageNumber, pageSize, type, level));
}
