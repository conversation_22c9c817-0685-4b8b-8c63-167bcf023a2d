using TeamFlow.Identity.Application.Contracts.Dto.ViewModels;
using TeamFlow.Identity.Core.Entities;

namespace TeamFlow.Identity.Application.Contracts.Mapping;

public static class UserMappingExtensions
{
    public static UserSkillViewModel ToViewModel(this UserSkill userSkill)
    {
        return new UserSkillViewModel
        {
            SkillId = userSkill.SkillId,
            Level = userSkill.Level,
            YearsExperience = userSkill.YearsExperience,
            IsPrimary = userSkill.IsPrimary
        };
    }
}