using FluentResults;
using MediatR;
using TeamFlow.Identity.Application.Contracts.Dto.ViewModels;
using TeamFlow.Identity.Application.Contracts.Filtering.Positions;
using TeamFlow.Shared.Contracts.Pagination;

namespace TeamFlow.Identity.Application.Contracts.Features.Queries.Position;

public record GetPositionsQuery(Pageable Pageable, PositionFilterCriteria? Filter, PositionSortingCriteria? Sorting)
    : IRequest<Result<Page<PositionViewModel>>>;