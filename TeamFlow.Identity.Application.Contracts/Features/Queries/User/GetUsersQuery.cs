using FluentResults;
using MediatR;
using TeamFlow.Identity.Application.Contracts.Dto.ViewModels;
using TeamFlow.Identity.Application.Contracts.Filtering.Users;
using TeamFlow.Shared.Contracts.Pagination;

namespace TeamFlow.Identity.Application.Contracts.Features.Queries.User;

public record GetUsersQuery(Pageable Pageable, UserFilterCriteria? Filter, UserSortingCriteria? Sorting) : IRequest<Result<Page<UserViewModel>>>;