using System.Net;

namespace TeamFlow.Identity.Application.Contracts.Dto.Client;

public record AuthContextInfo
{
    public required IPAddress IpAddress { get; init; }
    public string UserAgent             { get; init; } = string.Empty;
    public ClientDeviceInfo DeviceInfo  { get; init; } = new();
    public string? Country              { get; init; }
    public string? City                 { get; init; }
    public DateTime RequestTime         { get; init; } = DateTime.UtcNow;
}