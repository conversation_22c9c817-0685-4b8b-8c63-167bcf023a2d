using System.Linq.Expressions;
using TeamFlow.Identity.Core.Entities;
using TeamFlow.Shared.Contracts.Enums;
using TeamFlow.Shared.Contracts.Sorting;

namespace TeamFlow.Identity.Application.Contracts.Filtering.Users;

public record UserSortingCriteria(UsersSortField SortField, SortDirection SortDirection) : ISortingCriteria<User>
{
    public OrderByExpression<User> ToOrderByExpression()
    {
        Expression<Func<User, object>> keySelector = SortField switch
        {
            UsersSortField.CreatedAt => x => x.CreatedAt,
            UsersSortField.UpdatedAt => x => x.UpdatedAt,
            UsersSortField.Login => x => x.Login,
            UsersSortField.Email => x => x.Email,
            UsersSortField.OnlineStatus => x => x.OnlineStatus,
            UsersSortField.Role => x => x.Role,
            UsersSortField.LastLoginAt => x => x.LastLoginAt!,
            UsersSortField.PositionId => x => x.PositionId,
            _ => x => x.Id,
        };
        
        return new OrderByExpression<User>(keySelector, SortDirection);
    }
}