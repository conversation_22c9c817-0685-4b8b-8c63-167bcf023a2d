using System.Linq.Expressions;
using TeamFlow.Identity.Core.Entities;
using TeamFlow.Shared.Contracts.Enums;
using TeamFlow.Shared.Contracts.Sorting;

namespace TeamFlow.Identity.Application.Contracts.Filtering.Positions;

public record PositionSortingCriteria(PositionsSortField SortField, SortDirection SortDirection) : ISortingCriteria<Position>
{
    public OrderByExpression<Position> ToOrderByExpression()
    {
        Expression<Func<Position, object>> keySelector = SortField switch
        {
            PositionsSortField.CreatedAt => x => x.CreatedAt,
            PositionsSortField.UpdatedAt => x => x.UpdatedAt,
            PositionsSortField.Name => x => x.Name,
            _ => x => x.Id,
        };
        
        return new OrderByExpression<Position>(keySelector, SortDirection);
    }
}