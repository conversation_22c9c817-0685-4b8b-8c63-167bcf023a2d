version: '3.8'

services:
  zookeeper:
    image: confluentinc/cp-zookeeper:7.5.0
    container_name: zookeeper
    ports:
      - "2181:2181"
    environment:
      ZOOKEEPER_CLIENT_PORT: 2181
      ZOOKEEPER_TICK_TIME: 2000

  kafka:
    image: confluentinc/cp-kafka:7.5.0
    container_name: kafka
    ports:
      - "9092:9092"
    environment:
      KAFKA_BROKER_ID: 1
      KAFKA_ZOOKEEPER_CONNECT: zookeeper:2181
      KAFKA_ADVERTISED_LISTENERS: PLAINTEXT://192.168.2.6:9092
      KAFKA_LISTENERS: PLAINTEXT://0.0.0.0:9092
      KAFKA_OFFSETS_TOPIC_REPLICATION_FACTOR: 1

  akhq:
    image: tchiotludo/akhq
    container_name: akhq
    volumes:
      - ./akhq.application.yml:/app/application.yml:ro
    ports:
      - "8080:8080"
    environment:
      - AKHQ_CONNECTIONS_KAFKA_DEFAULT_BOOTSTRAPSERVERS=kafka:9092
    depends_on:
      - kafka