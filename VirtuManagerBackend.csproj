<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Confluent.Kafka" Version="2.10.0" />
    <PackageReference Include="FluentResults" Version="3.16.0" />
    <PackageReference Include="Grpc.AspNetCore" Version="2.57.0" />
    <PackageReference Include="Grpc.Net.Client" Version="2.66.0" />
    <PackageReference Include="Grpc.Tools" Version="2.67.0-pre1">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
    </PackageReference>
    <PackageReference Include="InfluxDB.Client" Version="4.19.0-dev.14906" />
    <PackageReference Include="MediatR" Version="12.5.0" />
    <PackageReference Include="Microsoft.AspNetCore.Authentication.JwtBearer" Version="8.0.0" />
    <PackageReference Include="MongoDB.Driver" Version="3.0.0" />
    <PackageReference Include="Pomelo.EntityFrameworkCore.MySql" Version="8.0.0" />
    <PackageReference Include="Swashbuckle.AspNetCore" Version="6.4.0" />
    <PackageReference Include="VirtuManagerKafkaClient" Version="1.0.4">
      <RestoreSources>https://gitlab.mkpnet.ru/api/v4/projects/112/packages/nuget/index.json</RestoreSources>
    </PackageReference>
    <ProtoBuf Include="Protos/virtualMachineLifecycle.proto" GrpcServices="Client" />
    <ProtoBuf Include="Protos/virtualMachineUtils.proto" GrpcServices="Client" />
    <ProtoBuf Include="Protos/virtualMachineStats.proto" GrpcServices="Client" />
    <ProtoBuf Include="Protos/networkManagerUtils.proto" GrpcServices="Client" />
    <ProtoBuf Include="Protos/virtualDiskLifecycle.proto" GrpcServices="Client" />
    <ProtoBuf Include="Protos/vncUtils.proto" GrpcServices="Client" />
  </ItemGroup>

  <ItemGroup>
    <Folder Include="Application\" />
    <Folder Include="Domain\" />
    <Folder Include="Presentation\" />
  </ItemGroup>

</Project>
