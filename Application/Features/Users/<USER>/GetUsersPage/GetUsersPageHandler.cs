using FluentResults;
using MediatR;
using VirtuManagerBackend.Application.Api.Response.PaginationResponse;
using VirtuManagerBackend.Domain.Db.Tables.UserTable;
using VirtuManagerBackend.Infrastructure.Extensions;
using VirtuManagerBackend.Infrastructure.Repositories;

namespace VirtuManagerBackend.Features.Users.Commands.GetUsersPage;

public class GetUsersPageHandler : IRequestHandler<GetUsersPageCommand, Result<PaginationResponse<User>>>
{
    private readonly UsersRepository _usersRepository;
    
    public GetUsersPageHandler(UsersRepository usersRepository)
    {
        _usersRepository = usersRepository;
    }
    
    public async Task<Result<PaginationResponse<User>>> Handle(GetUsersPageCommand request, CancellationToken cancellationToken)
    {
        var (users, totalCount) = await _usersRepository.GetUsersPage(request.PageNumber, request.PageSize);

        return Result.Ok(new PaginationResponse<User>
        {
            Items = users,
            TotalCount = totalCount,
            PageNumber = request.PageNumber,
            PageSize = request.PageSize,
            TotalPages = totalCount.CalculateTotalPages(request.PageSize)
        });
    }
}