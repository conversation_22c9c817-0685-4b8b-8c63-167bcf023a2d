using FluentResults;
using MediatR;
using VirtuManagerBackend.Application.Api.Response.User;
using VirtuManagerBackend.Domain.Db.Tables.UserTable;
using VirtuManagerBackend.Infrastructure.Repositories;

namespace VirtuManagerBackend.Application.Features.Users.Commands.AddUser;

public class AddUserHandler : IRequestHandler<AddUserCommand, Result<UserAddedResponse>>
{
    private readonly UsersRepository _usersRepository;
    
    public AddUserHandler(UsersRepository usersRepository)
    {
        _usersRepository = usersRepository;
    }
    
    public async Task<Result<UserAddedResponse>> Handle(AddUserCommand request, CancellationToken cancellationToken)
    {
        var userByLoginExists = await _usersRepository.GetUserByLogin(request.UserDto.Login);
        if (userByLoginExists != null)
        {
            return Result.Fail(new Error("User already exists")
                .WithMetadata("Response", new UserExistsErrorResponse())
                .WithMetadata("StatusCode", StatusCodes.Status409Conflict));
        }

        var user = new User
        {
            Login = request.UserDto.Login,
            Password = request.UserDto.Password,
            Email = request.UserDto.Email,
            Role = request.UserDto.Role,
            CreatedAt = DateTime.Now
        };
		
        await _usersRepository.Add(user);
        return Result.Ok(new UserAddedResponse());
    }
}