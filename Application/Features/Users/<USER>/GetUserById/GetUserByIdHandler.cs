using FluentResults;
using MediatR;
using VirtuManagerBackend.Application.Api.Response.User;
using VirtuManagerBackend.Domain.Db.Tables.UserTable;
using VirtuManagerBackend.Features.Users.Commands.GetUserById;
using VirtuManagerBackend.Infrastructure.Repositories;

namespace VirtuManagerBackend.Application.Features.Users.Commands.GetUserById;

public class GetUserByIdHandler : IRequestHandler<GetUserByIdCommand, Result<User>>
{
    private readonly UsersRepository _usersRepository;
    
    public GetUserByIdHandler(UsersRepository usersRepository)
    {
        _usersRepository = usersRepository;
    }
    
    public async Task<Result<User>> Handle(GetUserByIdCommand request, CancellationToken cancellationToken)
    {
        var user = await _usersRepository.GetUserById(request.Id);
        if (user == null)
        {
            return Result.Fail(new Error("User not found")
                .WithMetadata("Response", new UserNotFoundResponse())
                .WithMetadata("StatusCode", StatusCodes.Status404NotFound));
        }

        return Result.Ok(user);
    }
}