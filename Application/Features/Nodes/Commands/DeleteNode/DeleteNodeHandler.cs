using FluentResults;
using MediatR;
using VirtuManagerBackend.Application.Api.Response.Node;
using VirtuManagerBackend.Infrastructure.Repositories;

namespace VirtuManagerBackend.Application.Features.Nodes.Commands.DeleteNode;

public class DeleteNodeHandler : IRequestHandler<DeleteNodeCommand, Result<DeleteNodeResponse>>
{
    private readonly NodesRepository _nodesRepository;
    
    public DeleteNodeHandler(NodesRepository nodesRepository)
    {
        _nodesRepository = nodesRepository;
    }
    
    public async Task<Result<DeleteNodeResponse>> Handle(DeleteNodeCommand request, CancellationToken cancellationToken)
    {
        var node = await _nodesRepository.GetNodeById(request.Id);

        if (node == null)
        {
            return Result.Fail(new Error("Node not found")
                .WithMetadata("Response", new NodeNotFoundResponse())
                .WithMetadata("StatusCode", StatusCodes.Status404NotFound));
        }

        await _nodesRepository.Remove(node);
        return Result.Ok(new DeleteNodeResponse());
    }
}