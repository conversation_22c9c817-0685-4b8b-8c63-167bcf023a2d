using FluentResults;
using MediatR;
using VirtuManagerBackend.Application.Api.Response.VirtualDisks;
using VirtuManagerBackend.Infrastructure.Repositories;

namespace VirtuManagerBackend.Features.VirtualDisks.Commands.UpdateVirtualDisk;

public class UpdateVirtualDiskHandler : IRequestHandler<UpdateVirtualDiskCommand, Result<UpdateVirtualDiskResponse>>
{
    private readonly VirtualDisksRepository _virtualDisksRepository;
    
    public UpdateVirtualDiskHandler(VirtualDisksRepository virtualDisksRepository)
    {
        _virtualDisksRepository = virtualDisksRepository;
    }
    
    public async Task<Result<UpdateVirtualDiskResponse>> Handle(UpdateVirtualDiskCommand request, CancellationToken cancellationToken)
    {
        var virtualDisk = await _virtualDisksRepository.GetVirtualDiskById(request.UpdateVirtualDiskDto.Id);

        if (virtualDisk == null)
        {
            return Result.Fail(new Error("Virtual disk not found")
                .WithMetadata("Response", new VirtualDiskNotFoundResponse())
                .WithMetadata("StatusCode", StatusCodes.Status404NotFound));
        }
        
        virtualDisk.Name = request.UpdateVirtualDiskDto.Name;
        virtualDisk.Size = request.UpdateVirtualDiskDto.Size;

        await _virtualDisksRepository.Update(virtualDisk);
        return Result.Ok(new UpdateVirtualDiskResponse());
    }
}