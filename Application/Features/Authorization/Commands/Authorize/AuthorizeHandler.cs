using System.IdentityModel.Tokens.Jwt;
using FluentResults;
using MediatR;
using VirtuManagerBackend.Application.Api.Response.Jwt;
using VirtuManagerBackend.Application.Api.Response.User;
using VirtuManagerBackend.Authorization;
using VirtuManagerBackend.Infrastructure.Repositories;

namespace VirtuManagerBackend.Application.Features.Authorization.Commands.Authorize;

public class AuthorizeHandler : IRequestHandler<AuthorizeCommand, Result<JwtTokenResponse>>
{
    private readonly UsersRepository _usersRepository;
    
    private readonly JwtUtils _jwtUtils;
    
    public AuthorizeHandler(UsersRepository usersRepository, JwtUtils jwtUtils)
    {
        _usersRepository = usersRepository;
        _jwtUtils = jwtUtils;
    }
    
    public async Task<Result<JwtTokenResponse>> Handle(AuthorizeCommand request, CancellationToken cancellationToken)
    {
        var user = await _usersRepository.GetUserByLoginAndPassword(request.Login, request.Password);
        if (user == null)
        {
            return Result.Fail(new Error("Invalid login or password")
                    .WithMetadata("Response", new UserNotFoundResponse())
                    .WithMetadata("StatusCode", StatusCodes.Status404NotFound));
        }
        
        user.LastAuthorization = DateTime.Now;
        await _usersRepository.Update(user);
        
        return Result.Ok(new JwtTokenResponse(new JwtSecurityTokenHandler().WriteToken(_jwtUtils.GetSecurityTokenByUser(user))));
    }
}