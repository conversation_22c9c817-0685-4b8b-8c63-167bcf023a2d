using FluentResults;
using MediatR;
using VirtuManagerBackend.Application.Api.Response.Repositories;
using VirtuManagerBackend.Domain.Db.Tables.ImageRepositoryTable;

namespace VirtuManagerBackend.Features.ImageRepository.Commands.AddRepository;

public class AddRepositoryHandler : IRequestHandler<AddRepositoryCommand, Result<RepositoryCreatedResponse>>
{
    private readonly Infrastructure.Repositories.ImageRepository _imageRepository;
    
    public AddRepositoryHandler(Infrastructure.Repositories.ImageRepository imageRepository)
    {
        _imageRepository = imageRepository;
    }
    
    public async Task<Result<RepositoryCreatedResponse>> Handle(AddRepositoryCommand request, CancellationToken cancellationToken)
    {
        if (!Uri.TryCreate(request.AddRepositoryDto.Domain, UriKind.Absolute, out var uri) || uri.AbsolutePath != "/" || !uri.ToString().EndsWith($"/"))
        {
            return Result.Fail(new Error("Failed parse URL")
                .WithMetadata("Response", new RepositoryUriParseFailedResponse())
                .WithMetadata("StatusCode", StatusCodes.Status400BadRequest));
        }
        
        var httpClient = new HttpClient();
        var response = await httpClient.SendAsync(new HttpRequestMessage(HttpMethod.Get, request.AddRepositoryDto.Domain + "Api/ImageService/IsImageService"), cancellationToken);

        var content = await response.Content.ReadAsStringAsync(cancellationToken);
        if (!bool.TryParse(content, out _))
        {
            return Result.Fail(new Error("Is not an image service")
                .WithMetadata("Response", new IsNotImageServiceResponse())
                .WithMetadata("StatusCode", StatusCodes.Status400BadRequest));
        }
        
        var repositoryExists = await _imageRepository.GetRepositoryByDomain(request.AddRepositoryDto.Domain);
        if (repositoryExists != null)
        {
            return Result.Fail(new Error("Repository already exists")
                .WithMetadata("Response", new RepositoryExistsResponse())
                .WithMetadata("StatusCode", StatusCodes.Status409Conflict));
        }

        await _imageRepository.Add(new RemoteImageRepository
        {
            Domain = request.AddRepositoryDto.Domain,
            Title = request.AddRepositoryDto.Title
        });

        return Result.Ok(new RepositoryCreatedResponse());
    }
}