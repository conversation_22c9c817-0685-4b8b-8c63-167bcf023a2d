using FluentResults;
using MediatR;
using Microsoft.AspNetCore.Http.HttpResults;
using VirtuManagerBackend.Application.Api.Response.Repositories;
using VirtuManagerBackend.Domain.Db.Tables.ImageRepositoryTable;

namespace VirtuManagerBackend.Features.ImageRepository.Commands.GetRepositoryById;

public class GetRepositoryByIdHandler : IRequestHandler<GetRepositoryByIdCommand, Result<RemoteImageRepository>>
{
    private readonly Infrastructure.Repositories.ImageRepository _imageRepository;
    
    public GetRepositoryByIdHandler(Infrastructure.Repositories.ImageRepository imageRepository)
    {
        _imageRepository = imageRepository;
    }
    
    public async Task<Result<RemoteImageRepository>> Handle(GetRepositoryByIdCommand request, CancellationToken cancellationToken)
    {
        var repository = await _imageRepository.GetRepositoryById(request.Id);

        if (repository == null)
        {
            return Result.Fail(new Error("Repository not found")
                    .WithMetadata("Response", new RepositoryNotFoundResponse())
                    .WithMetadata("StatusCode", StatusCodes.Status404NotFound));
        }

        return Result.Ok(repository);
    }
}