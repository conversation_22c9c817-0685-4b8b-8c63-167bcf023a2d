using FluentResults;
using MediatR;
using VirtuManagerBackend.Application.Api.Response.PaginationResponse;
using VirtuManagerBackend.Domain.Db.Tables.ImageRepositoryTable;
using VirtuManagerBackend.Infrastructure.Extensions;

namespace VirtuManagerBackend.Features.ImageRepository.Commands.GetRepositoryPage;

public class GetRepositoryPageHandler : IRequestHandler<GetRepositoryPageCommand, Result<PaginationResponse<RemoteImageRepository>>>
{
    private readonly Infrastructure.Repositories.ImageRepository _imageRepository;
    
    public GetRepositoryPageHandler(Infrastructure.Repositories.ImageRepository imageRepository)
    {
        _imageRepository = imageRepository;
    }
    
    public async Task<Result<PaginationResponse<RemoteImageRepository>>> Handle(GetRepositoryPageCommand request, CancellationToken cancellationToken)
    {
        var (repositories, totalCount) = await _imageRepository.GetImageRepositoriesPage(request.PageNumber, request.PageSize);

        return Result.Ok(new PaginationResponse<RemoteImageRepository>
        {
            Items = repositories,
            TotalCount = totalCount,
            PageNumber = request.PageNumber,
            PageSize = request.PageSize,
            TotalPages = totalCount.CalculateTotalPages(request.PageSize)
        });
    }
}