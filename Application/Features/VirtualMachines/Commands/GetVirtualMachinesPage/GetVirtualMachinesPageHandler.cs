using FluentResults;
using MediatR;
using VirtuManagerBackend.Application.Api.Response.PaginationResponse;
using VirtuManagerBackend.Domain.Db.Tables.VirtualMachineTable;
using VirtuManagerBackend.Infrastructure.Extensions;
using VirtuManagerBackend.Infrastructure.Repositories;

namespace VirtuManagerBackend.Application.Features.VirtualMachines.Commands.GetVirtualMachinesPage;

public class GetVirtualMachinesPageHandler : IRequestHandler<GetVirtualMachinesPageCommand, Result<PaginationResponse<VirtualMachine>>>
{
    private readonly VirtualMachinesRepository _virtualMachinesRepository;
    
    public GetVirtualMachinesPageHandler(VirtualMachinesRepository virtualMachinesRepository)
    {
        _virtualMachinesRepository = virtualMachinesRepository;
    }
    
    public async Task<Result<PaginationResponse<VirtualMachine>>> Handle(GetVirtualMachinesPageCommand request, CancellationToken cancellationToken)
    {
        var (virtualMachines, totalCount) = await _virtualMachinesRepository.GetVirtualMachinesPage(request.PageNumber, request.PageSize);
        return Result.Ok(new PaginationResponse<VirtualMachine>
        {
            Items = virtualMachines,
            TotalCount = totalCount,
            PageNumber = request.PageNumber,
            PageSize = request.PageSize,
            TotalPages = totalCount.CalculateTotalPages(request.PageSize)
        });
    }
}