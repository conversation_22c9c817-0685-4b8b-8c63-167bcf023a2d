using System.Text.Json;
using FluentResults;
using MediatR;
using VirtuManagerBackend.Application.Api.Response.VirtualMachine;
using VirtuManagerBackend.Domain.Db.Tables;
using VirtuManagerBackend.Domain.Db.Tables.VirtualMachineTask;
using VirtuManagerBackend.Infrastructure.Repositories;
using VirtuManagerKafkaClient.Messaging.Interfaces;

namespace VirtuManagerBackend.Application.Features.VirtualMachines.Commands.StartVirtualMachine;

public class StartVirtualMachineHandler : IRequestHandler<StartVirtualMachineCommand, Result<StartVirtualMachineResponse>>
{
    private readonly VirtualMachineTaskRepository _virtualMachineTaskRepository;
    
    private readonly IKafkaProducer<VirtualMachineTask> _kafkaProducer;
    
    public StartVirtualMachineHandler(VirtualMachineTaskRepository virtualMachineTaskRepository, IKafkaProducer<VirtualMachineTask> kafkaProducer)
    {
        _kafkaProducer = kafkaProducer;
        _virtualMachineTaskRepository = virtualMachineTaskRepository;
    }
    
    public async Task<Result<StartVirtualMachineResponse>> Handle(StartVirtualMachineCommand request, CancellationToken cancellationToken)
    {
        var task = new VirtualMachineTask
        {
            Payload = JsonSerializer.Serialize(request.Request),
            Type = TaskType.StartVirtualMachine,
            CreatedAt = DateTime.Now,
            Status = TaskState.Pending,
            EndedAt = null
        };
        
        await _kafkaProducer.ProduceAsync(task, "virtual-machine-tasks", cancellationToken);
        await _virtualMachineTaskRepository.Add(task);
        
        return Result.Ok(new StartVirtualMachineResponse());
    }
}