using System.Text.Json;
using FluentResults;
using MediatR;
using VirtuManagerBackend.Application.Api.Response.VirtualMachine;
using VirtuManagerBackend.Domain.Db.Tables;
using VirtuManagerBackend.Domain.Db.Tables.VirtualMachineTask;
using VirtuManagerBackend.Infrastructure.Repositories;
using VirtuManagerKafkaClient.Messaging.Interfaces;

namespace VirtuManagerBackend.Application.Features.VirtualMachines.Commands.DeleteVirtualMachine;

public class DeleteVirtualMachineHandler : IRequestHandler<DeleteVirtualMachineCommand, Result<DeleteVirtualMachineResponse>>
{
    private readonly VirtualMachineTaskRepository _virtualMachineTaskRepository;
    
    private readonly IKafkaProducer<VirtualMachineTask> _kafkaProducer;
    
    public DeleteVirtualMachineHandler(VirtualMachineTaskRepository virtualMachineTaskRepository, IKafkaProducer<VirtualMachineTask> kafkaProducer)
    {
        _virtualMachineTaskRepository = virtualMachineTaskRepository;
        _kafkaProducer = kafkaProducer;
    }
    
    public async Task<Result<DeleteVirtualMachineResponse>> Handle(DeleteVirtualMachineCommand request, CancellationToken cancellationToken)
    {
        var task = new VirtualMachineTask
        {
            Payload = JsonSerializer.Serialize(request.IdRequestBase),
            Type = TaskType.RemoveVirtualMachine,
            CreatedAt = DateTime.Now,
            Status = TaskState.Pending,
            EndedAt = null
        };

        await _kafkaProducer.ProduceAsync(task, "virtual-machine-tasks", cancellationToken);
        await _virtualMachineTaskRepository.Add(task);
        
        return Result.Ok(new DeleteVirtualMachineResponse());
    }
}