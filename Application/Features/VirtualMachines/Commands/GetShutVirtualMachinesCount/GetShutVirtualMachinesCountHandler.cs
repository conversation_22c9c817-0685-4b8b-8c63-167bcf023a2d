using FluentResults;
using MediatR;
using VirtuManagerBackend.Application.Api.Response.VirtualMachine;
using VirtuManagerBackend.Infrastructure.Repositories;

namespace VirtuManagerBackend.Application.Features.VirtualMachines.Commands.GetShutVirtualMachinesCount;

public class GetShutVirtualMachinesCountHandler : IRequestHandler<GetShutVirtualMachinesCountCommand, Result<GetVirtualMachinesCountResponse>>
{
    private readonly VirtualMachinesRepository _virtualMachinesRepository;
    
    public GetShutVirtualMachinesCountHandler(VirtualMachinesRepository virtualMachinesRepository)
    {
        _virtualMachinesRepository = virtualMachinesRepository;
    }
    
    public async Task<Result<GetVirtualMachinesCountResponse>> Handle(GetShutVirtualMachinesCountCommand request, CancellationToken cancellationToken)
    {
        var vmsCount = await _virtualMachinesRepository.GetShutVirtualMachinesCount();
        return Result.Ok(new GetVirtualMachinesCountResponse(vmsCount));
    }
}