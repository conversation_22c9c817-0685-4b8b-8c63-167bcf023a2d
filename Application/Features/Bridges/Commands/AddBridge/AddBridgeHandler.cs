using System.Text.Json;
using FluentResults;
using MediatR;
using VirtuManagerBackend.Application.Api.Response.Bridges;
using VirtuManagerBackend.Domain.Db.Tables;
using VirtuManagerBackend.Domain.Db.Tables.BridgeTaskTable;
using VirtuManagerBackend.Infrastructure.Repositories;
using VirtuManagerKafkaClient.Messaging.Interfaces;

namespace VirtuManagerBackend.Features.Bridges.Commands.AddBridge;

public class AddBridgeHandler : IRequestHandler<AddBridgeCommand, Result<AddBridgeResponse>>
{
    private readonly BridgesRepository _bridgesRepository;
    
    private readonly BridgeTaskRepository _bridgeTaskRepository;

    private readonly IKafkaProducer<Domain.Db.Tables.BridgeTaskTable.BridgeTask> _kafkaProducer;
    
    public AddBridgeHandler(BridgesRepository bridgesRepository, BridgeTaskRepository bridgeTaskRepository, IKafkaProducer<Domain.Db.Tables.BridgeTaskTable.BridgeTask> kafkaProducer)
    {
        _bridgesRepository = bridgesRepository;
        _bridgeTaskRepository = bridgeTaskRepository;
        _kafkaProducer = kafkaProducer;
    }
    
    public async Task<Result<AddBridgeResponse>> Handle(AddBridgeCommand request, CancellationToken cancellationToken)
    {
        var bridgeExists = await _bridgesRepository.GetBridgeByName(request.AddBridgeDto.Name);

        if (bridgeExists != null)
        {
            
            return Result.Fail(new Error("Bridge with that name already exists")
                .WithMetadata("Response", new BridgeExistResponse())
                .WithMetadata("StatusCode", StatusCodes.Status409Conflict));
        }

        var task = new Domain.Db.Tables.BridgeTaskTable.BridgeTask
        {
            CreatedAt = DateTime.Now,
            Payload = JsonSerializer.Serialize(request.AddBridgeDto),
            Status = TaskState.Pending,
            Type = TaskType.CreateBridge
        };

        await _kafkaProducer.ProduceAsync(task, "bridge-tasks", cancellationToken);
        
        await _bridgeTaskRepository.Add(task);
        return Result.Ok(new AddBridgeResponse());
    }
}