using FluentResults;
using MediatR;
using VirtuManagerBackend.Application.Api.Response.Bridges;
using VirtuManagerBackend.Domain.Db.Tables.BridgesTable;
using VirtuManagerBackend.Infrastructure.Repositories;

namespace VirtuManagerBackend.Features.Bridges.Commands.GetBridgeById;

public class GetBridgeByIdHandler : IRequestHandler<GetBridgeByIdCommand, Result<Bridge>>
{
    private readonly BridgesRepository _bridgesRepository;
    
    public GetBridgeByIdHandler(BridgesRepository bridgesRepository)
    {
        _bridgesRepository = bridgesRepository;
    }
    
    public async Task<Result<Bridge>> Handle(GetBridgeByIdCommand request, CancellationToken cancellationToken)
    {
        var bridgeExist = await _bridgesRepository.GetBridgeById(request.Id);

        if (bridgeExist == null)
        {
            return Result.Fail(new Error("Bridge not found")
                .WithMetadata("Response", new BridgeNotFoundResponse())
                .WithMetadata("StatusCode", StatusCodes.Status404NotFound));
        }
        
        return Result.Ok(bridgeExist);
    }
}