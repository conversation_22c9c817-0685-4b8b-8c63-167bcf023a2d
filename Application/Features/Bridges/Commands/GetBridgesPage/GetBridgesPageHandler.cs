using FluentResults;
using MediatR;
using VirtuManagerBackend.Application.Api.Response.PaginationResponse;
using VirtuManagerBackend.Domain.Db.Tables.BridgesTable;
using VirtuManagerBackend.Infrastructure.Extensions;
using VirtuManagerBackend.Infrastructure.Repositories;

namespace VirtuManagerBackend.Features.Bridges.Commands.GetBridgesPage;

public class GetBridgesPageHandler : IRequestHandler<GetBridgesPageCommand, Result<PaginationResponse<Bridge>>>
{
    private readonly BridgesRepository _bridgesRepository;
    
    public GetBridgesPageHandler(BridgesRepository bridgesRepository)
    {
        _bridgesRepository = bridgesRepository;
    }
    
    public async Task<Result<PaginationResponse<Bridge>>> Handle(GetBridgesPageCommand request, CancellationToken cancellationToken)
    {
        var (bridges, totalCount) = await _bridgesRepository.GetBridgesPage(request.PageNumber, request.PageSize);

        return Result.Ok(new PaginationResponse<Bridge>()
        {
            Items = bridges,
            TotalCount = totalCount,
            PageNumber = request.PageNumber,
            PageSize = request.PageSize,
            TotalPages = totalCount.CalculateTotalPages(request.PageSize)
        });
    }
}