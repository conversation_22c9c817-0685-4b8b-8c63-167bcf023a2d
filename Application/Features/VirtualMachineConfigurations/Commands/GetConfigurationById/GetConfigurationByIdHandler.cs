using FluentResults;
using MediatR;
using VirtuManagerBackend.Application.Api.Response.VirtualMachineConfiguration;
using VirtuManagerBackend.Domain.Db.Tables.VirtualMachineConfigurationTable;
using VirtuManagerBackend.Infrastructure.Repositories;

namespace VirtuManagerBackend.Features.VirtualMachineConfigurations.Commands.GetConfigurationById;

public class GetConfigurationByIdHandler : IRequestHandler<GetConfigurationByIdCommand, Result<VirtualMachineConfiguration>>
{
    private readonly VirtualMachineConfigurationRepository _virtualMachineConfigurationRepository;
    
    public GetConfigurationByIdHandler(VirtualMachineConfigurationRepository virtualMachineConfigurationRepository)
    {
        _virtualMachineConfigurationRepository = virtualMachineConfigurationRepository;
    }
    
    public async Task<Result<VirtualMachineConfiguration>> Handle(GetConfigurationByIdCommand request, CancellationToken cancellationToken)
    {
        var configurationExists = await _virtualMachineConfigurationRepository.GetConfigurationById(request.Id);
        if (configurationExists == null)
        {
            return Result.Fail(new Error("Configuration not found")
                .WithMetadata("Response", new VirtualMachineConfigurationNotFoundResponse())
                .WithMetadata("StatusCode", StatusCodes.Status404NotFound));
        }

        return Result.Ok(configurationExists);
    }
}