using FluentResults;
using MediatR;
using VirtuManagerBackend.Application.Api.Response.VirtualMachineConfiguration;
using VirtuManagerBackend.Domain.Db.Tables.VirtualMachineConfigurationTable;
using VirtuManagerBackend.Infrastructure.Repositories;

namespace VirtuManagerBackend.Features.VirtualMachineConfigurations.Commands.AddConfiguration;

public class AddConfigurationHandler : IRequestHandler<AddConfigurationCommand, Result<VirtualMachineConfigurationCreatedResponse>>
{
    private readonly VirtualMachineConfigurationRepository _virtualMachineConfigurationRepository;
    
    public AddConfigurationHandler(VirtualMachineConfigurationRepository virtualMachineConfigurationRepository)
    {
        _virtualMachineConfigurationRepository = virtualMachineConfigurationRepository;
    }
    
    public async Task<Result<VirtualMachineConfigurationCreatedResponse>> Handle(AddConfigurationCommand request, CancellationToken cancellationToken)
    {
        var configurationExists = await _virtualMachineConfigurationRepository.GetConfigurationByTitle(request.AddVirtualMachineConfigurationDto.Title);
        if (configurationExists != null)
        {
            return Result.Fail(new Error("Configuration is already exists")
                .WithMetadata("Response", new VirtualMachineConfigurationExistsResponse())
                .WithMetadata("StatusCode", StatusCodes.Status409Conflict));
        }

        await _virtualMachineConfigurationRepository.Add(new VirtualMachineConfiguration
        {
            Title = request.AddVirtualMachineConfigurationDto.Title,
            Description = request.AddVirtualMachineConfigurationDto.Description,
            CpuCores = request.AddVirtualMachineConfigurationDto.CpuCores,
            RamSize = request.AddVirtualMachineConfigurationDto.RamSize,
            StorageSize = request.AddVirtualMachineConfigurationDto.StorageSize
        });

        return Result.Ok(new VirtualMachineConfigurationCreatedResponse());
    }
}