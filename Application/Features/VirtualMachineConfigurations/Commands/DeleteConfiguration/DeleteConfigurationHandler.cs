using FluentResults;
using MediatR;
using VirtuManagerBackend.Application.Api.Response.VirtualMachineConfiguration;
using VirtuManagerBackend.Infrastructure.Repositories;

namespace VirtuManagerBackend.Features.VirtualMachineConfigurations.Commands.DeleteConfiguration;

public class DeleteConfigurationHandler : IRequestHandler<DeleteConfigurationCommand, Result<VirtualMachineConfigurationDeletedResponse>>
{
    private readonly VirtualMachineConfigurationRepository _virtualMachineConfigurationRepository;
    
    public DeleteConfigurationHandler(VirtualMachineConfigurationRepository virtualMachineConfigurationRepository)
    {
        _virtualMachineConfigurationRepository = virtualMachineConfigurationRepository;
    }
    
    public async Task<Result<VirtualMachineConfigurationDeletedResponse>> Handle(DeleteConfigurationCommand request, CancellationToken cancellationToken)
    {
        var configurationExists = await _virtualMachineConfigurationRepository.GetConfigurationById(request.Id);
        if (configurationExists == null)
        {
            return Result.Fail(new Error("Configuration not found")
                .WithMetadata("Response", new VirtualMachineConfigurationNotFoundResponse())
                .WithMetadata("StatusCode", StatusCodes.Status404NotFound));
        }

        await _virtualMachineConfigurationRepository.Remove(configurationExists);

        return Result.Ok(new VirtualMachineConfigurationDeletedResponse());
    }
}