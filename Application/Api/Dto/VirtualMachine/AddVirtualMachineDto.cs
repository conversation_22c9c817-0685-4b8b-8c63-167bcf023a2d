using VirtuManagerBackend.Application.Api.Dto.VirtualDisk;

namespace VirtuManagerBackend.Application.Api.Dto.VirtualMachine;

public record AddVirtualMachineDto
{
    public string Title { get; init; }
    
    public int NodeId { get; init; }
    
    public string UserName { get; init; }
    
    public string Password { get; init; }
    
    public ulong Ram { get; init; }
    
    public int CpuCores { get; init; }

    public bool LockPassword { get; init; }
    
    public string HostName { get; init; }

    public string Description { get; init; }
    
    public int BridgeId { get; init; }
    
    public string IpAddress { get; init; }

    public int ImageRepositoryId { get; init; }

    public string InstalledOs { get; set; }

    public string OsVariant { get; set; }

    public bool AutoAssignIp { get; init; }
    
    public IEnumerable<AddVirtualDiskDto> Disks { get; init; }
}