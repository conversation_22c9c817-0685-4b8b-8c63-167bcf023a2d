namespace VirtuManagerBackend.Infrastructure.Utils;

public static class MacAddress
{
    public static string GenerateMacAddress()
    {
        var random = new Random();
        var macAddr = new byte[6];
        random.NextBytes(macAddr);
    
        // Set the first byte to be even (unicast)
        macAddr[0] = (byte)(macAddr[0] & (byte)254);
    
        return string.Join(":", macAddr.Select(b => b.ToString("X2")));
    }
}