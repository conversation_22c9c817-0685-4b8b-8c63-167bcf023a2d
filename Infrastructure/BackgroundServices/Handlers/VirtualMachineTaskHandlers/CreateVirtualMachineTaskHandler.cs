using System.Text.Json;
using Grpc.Net.Client;
using virtu_manager_node_service;
using VirtuManagerBackend.Application.Api.Dto.VirtualMachine;
using VirtuManagerBackend.Domain.Db.Tables;
using VirtuManagerBackend.Domain.Db.Tables.BridgeIpPoolTable;
using VirtuManagerBackend.Domain.Db.Tables.VirtualDiskTable;
using VirtuManagerBackend.Domain.Db.Tables.VirtualMachineSettingsTable;
using VirtuManagerBackend.Domain.Db.Tables.VirtualMachineTable;
using VirtuManagerBackend.Domain.Db.Tables.VirtualMachineTask;
using VirtuManagerBackend.Infrastructure.BackgroundServices.Handlers.Interfaces;
using VirtuManagerBackend.Infrastructure.Repositories;
using VirtuManagerBackend.Infrastructure.Utils;

namespace VirtuManagerBackend.Infrastructure.BackgroundServices.Handlers.VirtualMachineTaskHandlers;

public class CreateVirtualMachineTaskHandler : ITaskHandler<VirtualMachineTask>
{
    private VirtualMachineTask? _virtualMachineTask;

    private readonly VirtualMachinesRepository _virtualMachineRepository;

    private readonly VirtualMachineTaskRepository _virtualMachineTaskRepository;

    private readonly NodesRepository _nodesRepository;
    
    private readonly BridgesRepository _bridgesRepository;

    private readonly ImageRepository _imageRepository;
    
    public CreateVirtualMachineTaskHandler(VirtualMachinesRepository virtualMachineRepository, VirtualMachineTaskRepository virtualMachineTaskRepository, NodesRepository nodesRepository, BridgesRepository bridgesRepository, ImageRepository imageRepository)
    {
        _virtualMachineRepository = virtualMachineRepository;
        _virtualMachineTaskRepository = virtualMachineTaskRepository;
        _nodesRepository = nodesRepository;
        _bridgesRepository = bridgesRepository;
        _imageRepository = imageRepository;
    }
    
    public async Task HandleAsync()
    {
        await using var transaction = await _virtualMachineRepository.BeginTransactionAsync();
        try
        {
            var payloadDto = JsonSerializer.Deserialize<AddVirtualMachineDto>(_virtualMachineTask.Payload);
            var virtualMachine = await ParsePayload(_virtualMachineTask.Payload);
            
            var virtualMachineExists = await _virtualMachineRepository.GetVirtualMachineByTitle(virtualMachine.Title);
            if (virtualMachineExists != null)
            {
                throw new Exception("Virtual machine exists");
            }

            var node = await _nodesRepository.GetNodeById(virtualMachine.NodeId);
            if (node == null)
            {
                throw new Exception("Node not found");
            }

            var bridge = await _bridgesRepository.GetBridgeById(payloadDto.BridgeId);
            if (bridge == null)
            {
                throw new Exception("Bridge not found");
            }

            var imageRepository = await _imageRepository.GetRepositoryById(payloadDto.ImageRepositoryId);
            if (imageRepository == null)
            {
                throw new Exception("Image repository not found");
            }
            
            using var channel = GrpcChannel.ForAddress($"http://{node.Ip}:5212");
            var client = new CreateVirtualMachine.CreateVirtualMachineClient(channel);

            BridgeIp bridgeIp;
            
            if (payloadDto.AutoAssignIp)
            {
                bridgeIp = await _bridgesRepository.GetFirstFreeIpFromBridge(bridge.Id);
            }
            else
            {
                bridgeIp = await _bridgesRepository.GetIpByBridgeId(bridge.Id, payloadDto.IpAddress);
            }
            
            if (bridgeIp == null)
            {
                throw new InvalidOperationException("Not found IP");
            }

            var mac = MacAddress.GenerateMacAddress();
            var request = ConfigureVirualMachineRequest(virtualMachine, bridge.Name, bridgeIp.Ip, mac, payloadDto.OsVariant, imageRepository.Domain);
            
            foreach (var disk in virtualMachine.VirtualMachineSettings.VirtualDisks)
            {
                disk.NodeId = virtualMachine.NodeId;
                request.Disk.Add(new DiskInfo()
                {
                    Name = disk.Name,
                    Size = disk.Size
                });
            }
            
            _virtualMachineTask.EndedAt = DateTime.Now;
            _virtualMachineTask.Status = TaskState.Ended;
            await client.CreateAsync(request);
            await _virtualMachineRepository.Add(virtualMachine);
            bridgeIp.VirtualMachineId = virtualMachine.Id;
            bridgeIp.MacAddress = mac;
            await _bridgesRepository.UseIp(bridgeIp);
            
            await transaction.CommitAsync();
        }
        catch (Exception exception)
        {
            _virtualMachineTask!.Status = TaskState.EndedWithError;
            _virtualMachineTask.Error = exception.Message;
            await transaction.RollbackAsync();
        }
        
        await _virtualMachineTaskRepository.Update(_virtualMachineTask);
    }

    public void SetTask(VirtualMachineTask task)
    {
        ArgumentNullException.ThrowIfNull(task);

        _virtualMachineTask = task;
    }

    private CreateVirtualMachineRequest ConfigureVirualMachineRequest(VirtualMachine virtualMachine, string bridgeName, string ipAddress, string macAddress, string osVariant, string imageRepositoryUrl)
    {
        return new CreateVirtualMachineRequest
        {
            Name = virtualMachine.Title,
            Ram = virtualMachine.VirtualMachineSettings.Ram,
            Vcpu = virtualMachine.VirtualMachineSettings.CpuCores,
            OsVariant = osVariant,
            MetaData = new MetaData()
            {
                InstanceId = virtualMachine.VirtualMachineSettings.HostName,
                LocalHostName = virtualMachine.VirtualMachineSettings.UserName
            },
            UserData = new UserData()
            {
                Hostname = virtualMachine.VirtualMachineSettings.HostName,
                Users =
                {
                    new virtu_manager_node_service.User()
                    {
                        LockPasswd = virtualMachine.VirtualMachineSettings.LockPassword,
                        Name = virtualMachine.VirtualMachineSettings.UserName,
                        Passwd = virtualMachine.VirtualMachineSettings.Password,
                        Shell = virtualMachine.VirtualMachineSettings.Shell,
                        Sudo = virtualMachine.VirtualMachineSettings.Sudo
                    }
                },
                Packages =
                {
                    "qemu-guest-agent"
                },
                Runcmd =
                {
                    "sudo service qemu-guest-agent start"
                }
            },
            Network = new NetworkConfiguration()
            {
                Bridge = bridgeName,
                Ip = ipAddress,
                Mac = macAddress,
            },
            RepositoryDomain = imageRepositoryUrl
        };
    }
    
    private async Task<VirtualMachine> ParsePayload(string payload)
    {
        var payloadDto = JsonSerializer.Deserialize<AddVirtualMachineDto>(_virtualMachineTask.Payload);
        var virtualMachineSettings = new VirtualMachineSettings
        {
            UserName = payloadDto.UserName,
            Password = payloadDto.Password,
            Ram = payloadDto.Ram,
            CpuCores = payloadDto.CpuCores,
            Sudo = "ALL=(ALL) NOPASSWD:ALL",
            Shell = "/bin/bash",
            LockPassword = payloadDto.LockPassword,
            HostName = payloadDto.HostName,
            VirtualDisks = payloadDto.Disks.Select(d => new VirtualDisk
            {
                Name = d.Name,
                Size = d.Size
            }).ToList(),
            InstalledOs = payloadDto.InstalledOs
        };

        var bridgeExists = await _bridgesRepository.GetBridgeById(payloadDto.BridgeId);

        if (bridgeExists == null)
        {
            throw new Exception("Bridge not found");
        }
        
        var virtualMachine = new VirtualMachine
        {
            Title = payloadDto.Title,
            NodeId = payloadDto.NodeId,
            Description = payloadDto.Description,
            VirtualMachineSettings = virtualMachineSettings,
            StartDate = DateTime.Now,
        };

        return virtualMachine;
    }
}