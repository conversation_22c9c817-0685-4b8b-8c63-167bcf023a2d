using System.Text.Json;
using Grpc.Net.Client;
using virtu_manager_node_service;
using VirtuManagerBackend.Application.Api.Dto.VirtualDisk;
using VirtuManagerBackend.Domain.Db.Tables;
using VirtuManagerBackend.Domain.Db.Tables.VirtualDiskTable;
using VirtuManagerBackend.Domain.Db.Tables.VirtualDiskTaskTable;
using VirtuManagerBackend.Infrastructure.BackgroundServices.Handlers.Interfaces;
using VirtuManagerBackend.Infrastructure.Repositories;

namespace VirtuManagerBackend.Infrastructure.BackgroundServices.Handlers.VirtualMachineTaskHandlers;

public class CreateAndAttachVirtualDiskToVirtualMachineTaskHandler : ITaskHandler<VirtualDiskTask>
{
    private VirtualDiskTask? _virtualDiskTask;
    
    private readonly VirtualDisksRepository _virtualDisksRepository;
    
    private readonly VirtualDisksTaskRepository _virtualDisksTaskRepository;
    
    private readonly NodesRepository _nodesRepository;
    
    private readonly VirtualMachinesRepository _virtualMachinesRepository;
    
    public CreateAndAttachVirtualDiskToVirtualMachineTaskHandler(VirtualDisksRepository virtualDisksRepository, VirtualDisksTaskRepository virtualDisksTaskRepository, NodesRepository nodesRepository, VirtualMachinesRepository virtualMachinesRepository)
    {
        _virtualDisksRepository = virtualDisksRepository;
        _virtualDisksTaskRepository = virtualDisksTaskRepository;
        _nodesRepository = nodesRepository;
        _virtualMachinesRepository = virtualMachinesRepository;
    }

    public async Task HandleAsync()
    {
        await using var transaction = await _virtualDisksRepository.BeginTransactionAsync();
        try
        {
            var virtualDisk = await ParsePayload(_virtualDiskTask!.Payload);

            var node = await _nodesRepository.GetNodeById(virtualDisk.NodeId);

            if (node == null)
            {
                throw new Exception("Node not found");
            }
            
            using var channel = GrpcChannel.ForAddress($"http://{node.Ip}:5212");
            var client = new VirtualMachineLifecycle.VirtualMachineLifecycleClient(channel);
            var request = new VirtualMachineAttachDiskRequest
            {
                DiskName = virtualDisk.Name,
                VmName = virtualDisk.VirtualMachineSettings!.VirtualMachine.Title,
                Size = virtualDisk.Size
            };
            
            client.AttachDiskAsync(request);
            await _virtualDisksRepository.Update(virtualDisk);
            await transaction.CommitAsync();
        }
        catch (Exception exception)
        {
            _virtualDiskTask!.Status = TaskState.EndedWithError;
            _virtualDiskTask.Error = exception.Message;
            
            await transaction.RollbackAsync();
        }

        await _virtualDisksTaskRepository.Update(_virtualDiskTask);
    }

    public void SetTask(VirtualDiskTask task)
    {
        ArgumentNullException.ThrowIfNull(task);
        _virtualDiskTask = task;
    }

    private async Task<VirtualDisk> ParsePayload(string payload)
    {
        var payloadDto = JsonSerializer.Deserialize<AttachVirtualDiskToVirtualMachineDto>(payload);

        var nodeExists = await _nodesRepository.GetNodeById(payloadDto!.NodeId);

        if (nodeExists == null)
        {
            throw new Exception("Node not found");
        }

        var virtualMachineExists =
            await _virtualMachinesRepository.GetVirtualMachineByTitle(payloadDto.VirtualMachineName);

        if (virtualMachineExists == null)
        {
            throw new Exception("Virtual machine not found");
        }

        var virtualDiskExists = await _virtualDisksRepository.GetVirtualDiskByName(payloadDto.DiskName);

        if (virtualDiskExists != null)
        {
            throw new Exception("Disk already exists");
        }

        var virtualDisk = new VirtualDisk
        {
            Name = payloadDto.DiskName,
            Size = payloadDto.Size,
            NodeId = nodeExists.Id,
            VirtualMachineSettingsId = virtualMachineExists.VirtualMachineSettings.Id
        };
        
        return virtualDisk;
    }
}