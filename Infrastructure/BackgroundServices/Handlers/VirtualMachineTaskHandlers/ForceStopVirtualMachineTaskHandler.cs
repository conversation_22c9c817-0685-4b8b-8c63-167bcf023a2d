using System.Text.Json;
using Grpc.Net.Client;
using virtu_manager_node_service;
using VirtuManagerBackend.Application.Api.Requests;
using VirtuManagerBackend.Domain.Db.Tables;
using VirtuManagerBackend.Domain.Db.Tables.VirtualMachineTask;
using VirtuManagerBackend.Infrastructure.BackgroundServices.Handlers.Interfaces;
using VirtuManagerBackend.Infrastructure.Repositories;

namespace VirtuManagerBackend.Infrastructure.BackgroundServices.Handlers.VirtualMachineTaskHandlers;

public class ForceStopVirtualMachineTaskHandler : ITaskHandler<VirtualMachineTask>
{
    private readonly VirtualMachinesRepository _virtualMachinesRepository;

    private readonly NodesRepository _nodesRepository;

    private readonly VirtualMachineTaskRepository _virtualMachineTaskRepository;

    private VirtualMachineTask? _task;

    public ForceStopVirtualMachineTaskHandler(VirtualMachinesRepository virtualMachinesRepository, NodesRepository nodesRepository, VirtualMachineTaskRepository virtualMachineTaskRepository)
    {
        _virtualMachinesRepository = virtualMachinesRepository;
        _nodesRepository = nodesRepository;
        _virtualMachineTaskRepository = virtualMachineTaskRepository;
    }
    
    public async Task HandleAsync()
    {
        await using var transaction = await _virtualMachinesRepository.BeginTransactionAsync();
        _task!.EndedAt = DateTime.Now;
        try
        {
            var request = ParsePayload(_task.Payload);
            var virtualMachine = await _virtualMachinesRepository.GetVirtualMachineById(request.Id);
            
            if (virtualMachine == null)
            {
                throw new Exception("Virtual machine not found");
            }
            
            var node = await _nodesRepository.GetNodeById(virtualMachine.NodeId);

            if (node == null)
            {
                throw new Exception("Node not found");
            }
            
            using var channel = GrpcChannel.ForAddress($"http://{node.Ip}:5212");
            
            var client = new VirtualMachineLifecycle.VirtualMachineLifecycleClient(channel);
            var grpcRequest = new VirtualMachineRequest
            {
                Name = virtualMachine.Title
            };

            await client.ForceStopAsync(grpcRequest);
            _task.Status = TaskState.Ended;
            await transaction.CommitAsync();
        }
        catch (Exception exception)
        {
            _task.Status = TaskState.EndedWithError;
            _task.Error = exception.Message;
            await transaction.RollbackAsync();
        }
        
        await _virtualMachineTaskRepository.Update(_task);
    }

    public void SetTask(VirtualMachineTask task)
    {
        ArgumentNullException.ThrowIfNull(task);
        
        _task = task;
    }

    private IdRequestBase? ParsePayload(string payload)
        => JsonSerializer.Deserialize<IdRequestBase>(payload);
}