using VirtuManagerBackend.Domain.Db.Tables.VirtualMachineTable;
using VirtuManagerBackend.Infrastructure.BackgroundServices.Handlers.Interfaces;
using VirtuManagerBackend.Infrastructure.Repositories;

namespace VirtuManagerBackend.Infrastructure.BackgroundServices.Handlers.UpdateStatsHandlers;

public class UpdateUptimeHandler : IUpdateStatsHandler<VirtualMachine>
{
    private readonly VirtualMachinesRepository _virtualMachinesRepository;
    
    private VirtualMachine? _virtualMachine;
    
    public UpdateUptimeHandler(VirtualMachinesRepository virtualMachinesRepository)
    {
        _virtualMachinesRepository = virtualMachinesRepository;
    }
    
    public async Task HandleAsync()
    {
        await using var transaction = await _virtualMachinesRepository.BeginTransactionAsync();

        try
        {
            if (_virtualMachine.State == "running")
            {
                var dateTime = (DateTimeOffset)DateTime.Now - _virtualMachine.StartDate!;
                _virtualMachine.Uptime = (ulong?)dateTime.GetValueOrDefault().TotalSeconds;
            }
            else if(_virtualMachine.State != "running")
            {
                _virtualMachine.Uptime = 0;
            }

            await _virtualMachinesRepository.Update(_virtualMachine);
        }
        catch (Exception ex)
        {
            await transaction.RollbackAsync();
            return;
        }

        await transaction.CommitAsync();
    }

    public void SetEntity(VirtualMachine entity)
    {
        ArgumentNullException.ThrowIfNull(entity);
        _virtualMachine = entity;
    }
}