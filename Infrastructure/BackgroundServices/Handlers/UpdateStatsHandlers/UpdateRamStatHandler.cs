using Grpc.Net.Client;
using InfluxDB.Client.Api.Domain;
using InfluxDB.Client.Writes;
using virtu_manager_node_service;
using VirtuManagerBackend.Application.Services;
using VirtuManagerBackend.Domain.Db.Tables.VirtualMachineTable;
using VirtuManagerBackend.Infrastructure.BackgroundServices.Handlers.Interfaces;
using VirtuManagerBackend.Infrastructure.Repositories;

namespace VirtuManagerBackend.Infrastructure.BackgroundServices.Handlers.UpdateStatsHandlers;

public class UpdateRamStatHandler : IUpdateStatsHandler<VirtualMachine>
{
    private readonly VirtualMachinesRepository _virtualMachinesRepository;

    private VirtualMachine? _virtualMachine;
    
    private readonly InfluxDbService _influxDbService;
    
    public UpdateRamStatHandler(VirtualMachinesRepository virtualMachinesRepository, InfluxDbService influxDbService)
    {
        _influxDbService = influxDbService;
        _virtualMachinesRepository = virtualMachinesRepository;
    }

    public async Task HandleAsync()
    {
        await using var transaction = await _virtualMachinesRepository.BeginTransactionAsync();

        try
        {
            var node = _virtualMachine.Node;

            using var grpcChannel = GrpcChannel.ForAddress($"http://{node.Ip}:5212");
            var client = new VirtualMachineStats.VirtualMachineStatsClient(grpcChannel);

            var response = await client.GetRamLoadAsync(new VirtualMachineGetStatsRequest()
            {
                VmName = _virtualMachine.Title
            });

            _virtualMachine.AvailableMemory = response.Available;
            _virtualMachine.TotalMemory = response.Max;
            _virtualMachine.UsedMemory = response.Used;
            
            _influxDbService.Write(write =>
            {
                write.WritePoints(new List<PointData>()
                {
                    PointData.Measurement("vms")
                        .Tag("vm", _virtualMachine.Title)
                        .Field("AvailableRam", response.Available)
                        .Timestamp(DateTime.Now, WritePrecision.S),
                    PointData.Measurement("vms")
                        .Tag("vm", _virtualMachine.Title)
                        .Field("TotalRam", response.Max)
                        .Timestamp(DateTime.Now, WritePrecision.S),
                    PointData.Measurement("vms")
                        .Tag("vm", _virtualMachine.Title)
                        .Field("UsedRam", response.Used)
                        .Timestamp(DateTime.Now, WritePrecision.S)
                });
            });
            await _virtualMachinesRepository.Update(_virtualMachine);
        }
        catch (Exception exception)
        {
            await transaction.RollbackAsync();
            return;
        }

        await transaction.CommitAsync();
    }

    public void SetEntity(VirtualMachine entity)
    {
        ArgumentNullException.ThrowIfNull(entity);

        _virtualMachine = entity;
    }
}