using Grpc.Net.Client;
using InfluxDB.Client.Api.Domain;
using InfluxDB.Client.Writes;
using virtu_manager_node_service;
using VirtuManagerBackend.Application.Services;
using VirtuManagerBackend.Domain.Db.Tables.VirtualMachineTable;
using VirtuManagerBackend.Infrastructure.BackgroundServices.Handlers.Interfaces;
using VirtuManagerBackend.Infrastructure.Repositories;

namespace VirtuManagerBackend.Infrastructure.BackgroundServices.Handlers.UpdateStatsHandlers;

public class UpdateCpuStatHandler : IUpdateStatsHandler<VirtualMachine>
{
    private readonly VirtualMachinesRepository _virtualMachinesRepository;

    private VirtualMachine? _virtualMachine;

    private readonly InfluxDbService _influxDbService;

    public UpdateCpuStatHandler(VirtualMachinesRepository virtualMachinesRepository, InfluxDbService influxDbService)
    {
        _influxDbService = influxDbService;
        _virtualMachinesRepository = virtualMachinesRepository;
    }

    public async Task HandleAsync()
    {
        await using var transaction = await _virtualMachinesRepository.BeginTransactionAsync();
        try
        {
            var node = _virtualMachine.Node;

            using var grpcChannel = GrpcChannel.ForAddress($"http://{node.Ip}:5212");
            var client = new VirtualMachineStats.VirtualMachineStatsClient(grpcChannel);

            var response = await client.GetCpuLoadAsync(new VirtualMachineGetStatsRequest
            {
                VmName = _virtualMachine.Title
            });

            _virtualMachine.CpuLoad = response.CpuLoad;

            _influxDbService.Write(write =>
            {
                var point = PointData.Measurement("vms")
                    .Tag("vm", _virtualMachine.Title)
                    .Field("CpuLoad", response.CpuLoad)
                    .Timestamp(DateTime.Now, WritePrecision.S);

                write.WritePoint(point);
            });
            await _virtualMachinesRepository.Update(_virtualMachine);
        }
        catch (Exception exception)
        {
            return;
        }

        await transaction.CommitAsync();
    }

    public void SetEntity(VirtualMachine entity)
    {
        ArgumentNullException.ThrowIfNull(entity);

        _virtualMachine = entity;
    }
}