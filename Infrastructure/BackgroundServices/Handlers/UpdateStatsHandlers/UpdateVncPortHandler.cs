using Grpc.Net.Client;
using virtu_manager_node_service;
using VirtuManagerBackend.Domain.Db.Tables.VirtualMachineTable;
using VirtuManagerBackend.Infrastructure.BackgroundServices.Handlers.Interfaces;
using VirtuManagerBackend.Infrastructure.Repositories;

namespace VirtuManagerBackend.Infrastructure.BackgroundServices.Handlers.UpdateStatsHandlers;

public class UpdateVncPortHandler : IUpdateStatsHandler<VirtualMachine>
{
    private readonly VirtualMachinesRepository _virtualMachinesRepository;
    
    private VirtualMachine? _virtualMachine;

    public UpdateVncPortHandler(VirtualMachinesRepository virtualMachinesRepository)
    {
        _virtualMachinesRepository = virtualMachinesRepository;
    }
    
    public async Task HandleAsync()
    {
        await using var transaction = await _virtualMachinesRepository.BeginTransactionAsync();
        
        try
        {
            var node = _virtualMachine.Node;
            
            using var channel = GrpcChannel.ForAddress($"http://{node.Ip}:5212");
            var client = new VncUtils.VncUtilsClient(channel);
            var grpcRequest = new VirtualMachineVncRequest
            {
                VmName = _virtualMachine.Title
            };
            
            var response = await client.GetVirtualMachineWebsockifyInfoAsync(grpcRequest);
            
            _virtualMachine.VncPort = response.WebsockifyPort;

            await _virtualMachinesRepository.Update(_virtualMachine);
        }
        catch (Exception exception)
        {
            await transaction.RollbackAsync();
            return;
        }

        await transaction.CommitAsync();
    }

    public void SetEntity(VirtualMachine entity)
    {
        ArgumentNullException.ThrowIfNull(entity);
        _virtualMachine = entity;
    }
}