using VirtuManagerBackend.Domain.Db.Tables.BridgeTaskTable;
using VirtuManagerBackend.Infrastructure.BackgroundServices.Handlers.BridgeTaskHandlers;
using VirtuManagerBackend.Infrastructure.BackgroundServices.Handlers.Interfaces;

namespace VirtuManagerBackend.Infrastructure.BackgroundServices.Handlers.Factories;

public class BridgesTaskFactory : ITaskFactory<BridgeTask, TaskType>
{
    private readonly IServiceProvider _serviceProvider;
    
    public BridgesTaskFactory(IServiceScopeFactory serviceScopeFactory)
    {
        _serviceProvider = serviceScopeFactory.CreateScope().ServiceProvider;
    }
    
    public ITaskHandler<BridgeTask> GetHandler(TaskType type, BridgeTask task)
    {
        switch (type)
        {
            case TaskType.CreateBridge:
                var createVirtualMachineHandler = _serviceProvider.GetRequiredService<CreateBridgeHandler>();
                createVirtualMachineHandler.SetTask(task);
                return createVirtualMachineHandler;
            case TaskType.DeleteBridge:
                break;
        }
        
        throw new ArgumentOutOfRangeException();
    }
}