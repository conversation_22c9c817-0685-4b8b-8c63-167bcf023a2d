using Microsoft.EntityFrameworkCore;
using VirtuManagerBackend.Domain.Db;
using VirtuManagerBackend.Domain.Db.Tables.BridgeIpPoolTable;
using VirtuManagerBackend.Domain.Db.Tables.BridgesTable;

namespace VirtuManagerBackend.Infrastructure.Repositories;

public class BridgesRepository : AbstractRepository<Bridge>
{
    public BridgesRepository(ApplicationDbContext applicationDbContext) : base(applicationDbContext)
    {
    }

    public override async Task Add(Bridge entity)
    {
        await ApplicationDbContext.Bridges.AddAsync(entity);
        await ApplicationDbContext.SaveChangesAsync();
    }

    public override async Task<List<Bridge>> GetAll()
        => await ApplicationDbContext.Bridges
            .Include(b => b.BridgeIps)
            .Include(b => b.Node)
            .ToListAsync();

    public override async Task Remove(Bridge entity)
    {
        ApplicationDbContext.Bridges.Remove(entity);
        await ApplicationDbContext.SaveChangesAsync();
    }

    public override async Task Update(Bridge entity)
    {
        ApplicationDbContext.Bridges.Update(entity);
        await ApplicationDbContext.SaveChangesAsync();
    }

    public async Task<Bridge?> GetBridgeById(int id) =>
        await ApplicationDbContext.Bridges.FirstOrDefaultAsync(b => b.Id == id);

    public async Task<Bridge?> GetBridgeByName(string name) =>
        await ApplicationDbContext.Bridges.FirstOrDefaultAsync(b => b.Name == name);

    public async Task AddBridgeIp(BridgeIp ip)
    {
        await ApplicationDbContext.IpPool.AddAsync(ip);
        await ApplicationDbContext.SaveChangesAsync();
    }

    public async Task<BridgeIp?> GetFirstFreeIpFromBridge(int bridgeId)
        => await ApplicationDbContext.IpPool.FirstOrDefaultAsync(ip =>
            ip.VirtualMachine == null && ip.BridgeId == bridgeId);

    public async Task<BridgeIp?> GetIpByBridgeId(int bridgeId, string ipAddress)
        => await ApplicationDbContext.IpPool.FirstOrDefaultAsync(ip => ip.Ip == ipAddress && ip.BridgeId == bridgeId);

    public async Task UseIp(BridgeIp bridgeIp)
    {
        ApplicationDbContext.IpPool.Update(bridgeIp);
        await ApplicationDbContext.SaveChangesAsync();
    }

    public async Task<(IEnumerable<Bridge>? bridges, long totalCount)> GetBridgesPage(int pageNumber, int pageSize) =>
        await GetPaged(pageNumber, pageSize, ApplicationDbContext.Bridges.AsQueryable());
}