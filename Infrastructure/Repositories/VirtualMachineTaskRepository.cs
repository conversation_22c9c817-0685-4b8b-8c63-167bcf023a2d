using Microsoft.EntityFrameworkCore;
using VirtuManagerBackend.Domain.Db;
using VirtuManagerBackend.Domain.Db.Tables;
using VirtuManagerBackend.Domain.Db.Tables.VirtualMachineTask;

namespace VirtuManagerBackend.Infrastructure.Repositories;

public class VirtualMachineTaskRepository : AbstractRepository<VirtualMachineTask>
{
    public VirtualMachineTaskRepository(ApplicationDbContext applicationDbContext) : base(applicationDbContext)
    {
    }

    public override async Task Add(VirtualMachineTask entity)
    {
        await ApplicationDbContext.VirtualMachineTasks.AddAsync(entity);
        await ApplicationDbContext.SaveChangesAsync();
    }

    public override async Task<List<VirtualMachineTask>> GetAll()
        => await ApplicationDbContext.VirtualMachineTasks.ToListAsync();

    public override async Task Remove(VirtualMachineTask entity)
    {
        ApplicationDbContext.VirtualMachineTasks.Remove(entity);
        await ApplicationDbContext.SaveChangesAsync();
    }

    public override async Task Update(VirtualMachineTask entity)
    {
        ApplicationDbContext.VirtualMachineTasks.Update(entity);
        await ApplicationDbContext.SaveChangesAsync();
    }

    public async Task<VirtualMachineTask?> GetTaskById(int id)
        => await ApplicationDbContext.VirtualMachineTasks.FirstOrDefaultAsync(t => t.Id == id);

    public async Task<IEnumerable<VirtualMachineTask>> GetPendingTasks()
        => await ApplicationDbContext.VirtualMachineTasks
            .Where(t => t.Status == TaskState.Pending)
            .ToListAsync();

    public async Task<(IEnumerable<VirtualMachineTask>? virtualMachineTasks, long totalCount)>
        GetVirtualMachineTasksPage(int pageNumber, int pageSize)
        => await GetPaged(pageNumber, pageSize, ApplicationDbContext.VirtualMachineTasks.AsQueryable());
}