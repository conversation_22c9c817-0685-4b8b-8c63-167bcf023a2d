using Microsoft.EntityFrameworkCore;
using VirtuManagerBackend.Domain.Db;
using VirtuManagerBackend.Domain.Db.Tables;
using VirtuManagerBackend.Domain.Db.Tables.BridgeTaskTable;

namespace VirtuManagerBackend.Infrastructure.Repositories;

public class BridgeTaskRepository : AbstractRepository<BridgeTask>
{
    public BridgeTaskRepository(ApplicationDbContext applicationDbContext) : base(applicationDbContext)
    {
    }

    public override async Task Add(BridgeTask entity)
    {
        await ApplicationDbContext.BridgeTasks.AddAsync(entity);
        await ApplicationDbContext.SaveChangesAsync();
    }

    public override async Task<List<BridgeTask>> GetAll()
        => await ApplicationDbContext.BridgeTasks.ToListAsync();

    public override async Task Remove(BridgeTask entity)
    {
        ApplicationDbContext.BridgeTasks.Remove(entity);
        await ApplicationDbContext.SaveChangesAsync();
    }

    public override async Task Update(BridgeTask entity)
    {
        ApplicationDbContext.BridgeTasks.Update(entity);
        await ApplicationDbContext.SaveChangesAsync();
    }

    public async Task<List<BridgeTask>> GetPendingTasks()
        => await ApplicationDbContext.BridgeTasks.Where(t => t.Status == TaskState.Pending).ToListAsync();

    public async Task<(IEnumerable<BridgeTask>? bridgeTasks, long totalCount)>
        GetBridgeTasksPage(int pageNumber, int pageSize) =>
        await GetPaged(pageNumber, pageSize, ApplicationDbContext.BridgeTasks.AsQueryable());
}