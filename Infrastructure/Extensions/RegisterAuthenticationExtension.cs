using Microsoft.AspNetCore.Authentication;
using Microsoft.IdentityModel.Tokens;
using VirtuManagerBackend.Authorization;

namespace VirtuManagerBackend.Infrastructure.Extensions;

public static class RegisterAuthenticationExtension
{
    public static AuthenticationBuilder RegisterJwtBearer(this AuthenticationBuilder builder)
    {
        var config = builder.Services.BuildServiceProvider().GetService<AuthConfiguration>();
        ArgumentNullException.ThrowIfNull(config);

        builder.AddJwtBearer(opts =>
        {
            opts.TokenValidationParameters = new TokenValidationParameters()
            {
                ValidateIssuer = true,
                ValidIssuer = config.Issuer,

                ValidateAudience = true,
                ValidAudience = config.Audience,

                ValidateLifetime = true,

                IssuerSigningKey = config.GetSymmetricSecurityKey(),
                ValidateIssuerSigningKey = true
            };
        });

        return builder;
    }
}