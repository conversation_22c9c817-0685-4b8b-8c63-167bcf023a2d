using FluentResults;
using Microsoft.AspNetCore.Mvc;

namespace VirtuManagerBackend.Infrastructure.Extensions;

public static class MediatorResultExtension
{
    public static IActionResult ToActionResult<T>(this Result<T> result, ControllerBase controller)
    {
        if (result.IsSuccess)
            return controller.Ok(result.Value);

        var firstError = result.Errors.First();
        if (firstError.Metadata.TryGetValue("StatusCode", out var statusCodeObj) 
            && statusCodeObj is int statusCode 
            && firstError.Metadata.TryGetValue("Response", out var response))
        {
            return controller.StatusCode(statusCode, response);
        }

        return controller.BadRequest(result.Errors);
    }
}