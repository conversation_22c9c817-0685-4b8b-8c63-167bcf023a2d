using System.Reflection;
using VirtuManagerBackend.Infrastructure.BackgroundServices.Interfaces;

namespace VirtuManagerBackend.Infrastructure.Extensions;

public static class RegisterBackgroundServicesExtension
{
    public static IServiceCollection AddMarkedBackgroundServices(this IServiceCollection services)
    {
        var assembly = Assembly.GetExecutingAssembly();
        var interfaceType = typeof(IAutoRegisterHostedService);
        var method = typeof(ServiceCollectionHostedServiceExtensions)
            .GetMethods(BindingFlags.Static | BindingFlags.Public)
            .First(m =>
                m.Name == "AddHostedService" &&
                m.IsGenericMethod &&
                m.GetParameters().Length == 1);

        var hostedServiceTypes = assembly.GetTypes()
            .Where(t => interfaceType.IsAssignableFrom(t) && t is { IsClass: true, IsAbstract: false });

        foreach (var type in hostedServiceTypes)
        {
            var genericMethod = method.MakeGenericMethod(type);
            genericMethod.Invoke(null, [services]);
        }

        return services;
    }
}