using VirtuManagerBackend.Domain.Db.Tables.BridgeTaskTable;
using VirtuManagerBackend.Infrastructure.BackgroundServices.Handlers.Interfaces;
using VirtuManagerKafkaClient.Messaging.Interfaces;

namespace VirtuManagerBackend.Infrastructure.Kafka.Consumers;

public class BridgeTaskHandler : IMessageHandler<BridgeTask>
{
    private readonly ITaskFactory<BridgeTask, TaskType> _taskFactory;
    
    public BridgeTaskHandler(ITaskFactory<BridgeTask, TaskType> taskFactory)
    {
        _taskFactory = taskFactory;
    }
    
    public async Task HandleAsync(BridgeTask message, CancellationToken cancellationToken)
    {
        await _taskFactory.GetHandler(message.Type, message).HandleAsync();
    }
}