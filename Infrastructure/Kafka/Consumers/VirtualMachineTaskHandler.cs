using VirtuManagerBackend.Domain.Db.Tables.VirtualMachineTask;
using VirtuManagerBackend.Infrastructure.BackgroundServices.Handlers.Interfaces;
using VirtuManagerKafkaClient.Messaging.Interfaces;

namespace VirtuManagerBackend.Infrastructure.Kafka.Consumers;

public class VirtualMachineTaskHandler : IMessageHandler<VirtualMachineTask>
{
    private readonly ITaskFactory<VirtualMachineTask, TaskType> _taskFactory;
    
    public VirtualMachineTaskHandler(ITaskFactory<VirtualMachineTask, TaskType> taskFactory)
    {
        _taskFactory = taskFactory;
    }
    
    public async Task HandleAsync(VirtualMachineTask message, CancellationToken cancellationToken)
    {
        await _taskFactory.GetHandler(message.Type, message).HandleAsync();
    }
}