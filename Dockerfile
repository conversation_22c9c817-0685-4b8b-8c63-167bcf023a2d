FROM mcr.microsoft.com/dotnet/sdk:8.0 AS build
WORKDIR /src

COPY ["VirtuManagerBackend.csproj", "./"]
RUN dotnet restore "VirtuManagerBackend.csproj"

COPY . .
RUN dotnet build "VirtuManagerBackend.csproj" -c Release -o /app/build
RUN dotnet publish "VirtuManagerBackend.csproj" -c Release -o /app/publish

FROM mcr.microsoft.com/dotnet/aspnet:8.0-bookworm-slim
WORKDIR /app
COPY --from=build /app/publish .

ENV ASPNETCORE_URLS=http://+:5173
EXPOSE 5173

ENTRYPOINT ["dotnet", "VirtuManagerBackend.dll"]
