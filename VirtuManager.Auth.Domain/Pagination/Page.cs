namespace TeamFlow.Shared.Contracts.Pagination;

/// <summary>
/// Представляет собой модель пагинированного списка сущностей типа <typeparamref name="TEntity"/>.
/// Содержит коллекцию элементов на текущей странице и метаданные пагинации.
/// </summary>
/// <typeparam name="TEntity">Тип сущности, содержащейся на странице.</typeparam>
public class Page<TEntity> where TEntity : class
{
    /// <summary>
    /// Получает или задает коллекцию сущностей типа <typeparamref name="TEntity"/> на текущей странице.
    /// Может быть null, если страница пуста.
    /// </summary>
    public IReadOnlyList<TEntity>? Elements { get; set; }

    /// <summary>
    /// Получает или задает общее количество элементов, соответствующих запросу без учета пагинации.
    /// </summary>
    public long TotalCount { get; set; }

    /// <summary>
    /// Получает или задает номер текущей страницы (начиная с 1).
    /// </summary>
    public int PageNumber { get; set; }

    /// <summary>
    /// Получает или задает размер страницы, то есть максимальное количество элементов на одной странице.
    /// </summary>
    public int PageSize { get; set; }

    /// <summary>
    /// Получает общее количество страниц на основе <see cref="TotalCount"/> и <see cref="PageSize"/>.
    /// Если <see cref="PageSize"/> равно 0, возвращает 0.
    /// </summary>
    public ulong TotalPages => (ulong)(PageSize > 0 ? Math.Ceiling((double)TotalCount / PageSize) : 0);

    /// <summary>
    /// Получает или задает значение, указывающее, является ли текущая страница первой страницей.
    /// </summary>
    public bool HasNextPage => PageNumber * PageSize < TotalCount;

    /// <summary>
    /// Получает или задает значение, указывающее, является ли текущая страница последней страницей.
    /// </summary>
    public bool HasPreviousPage => PageSize > 1;

    /// <summary>
    /// Инициализирует новый экземпляр класса <see cref="Page{TEntity}"/>.
    /// </summary>
    /// <param name="elements">Коллекция сущностей на текущей странице.</param>
    /// <param name="totalCount">Общее количество элементов.</param>
    /// <param name="pageable">Объект <see cref="Pageable"/>, содержащий информацию о пагинации.</param>
    public Page(List<TEntity>? elements, long totalCount, Pageable pageable)
    {
        Elements = elements;
        TotalCount = totalCount;
        PageSize = pageable.PageSize;
        PageNumber = pageable.PageNumber;
    }

    public Page()
    {
    }
}