namespace TeamFlow.Shared.Contracts.Pagination;

/// <summary>
/// Представляет собой модель для параметров пагинации при запросе списка сущностей.
/// Содержит информацию о номере страницы, размере страницы и параметрах сортировки.
/// </summary>
public record struct Pageable
{
    private const int MaxPageSize = 100;

    private readonly int _pageSize = 10;

    /// <summary>
    /// Получает или задает номер запрашиваемой страницы (по умолчанию 1).
    /// Нумерация страниц обычно начинается с 1.
    /// </summary>
    public int PageNumber { get; init; } = 1;


    /// <summary>
    /// Получает или задает желаемый размер страницы, то есть максимальное количество элементов, возвращаемых на одной странице (по умолчанию 10).
    /// Максимальный размер страницы 100.
    /// </summary>
    public int PageSize
    {
        get => _pageSize;
        private init => _pageSize = value > MaxPageSize ? MaxPageSize : value;
    }

    /// <summary>
    /// Инициализирует новый экземпляр класса <see cref="Pageable"/> с указанными параметрами пагинации.
    /// </summary>
    /// <param name="pageNumber">Номер страницы (начиная с 1).</param>
    /// <param name="pageSize">Размер страницы (максимальное количество элементов на странице).</param>
    public Pageable(int pageNumber, int pageSize)
    {
        PageNumber = pageNumber;
        PageSize = pageSize > MaxPageSize ? MaxPageSize : pageSize;
        // SortBy = sortBy;
    }

    /// <summary>
    /// Инициализирует новый экземпляр класса <see cref="Pageable"/> с параметрами по умолчанию.
    /// </summary>
    public Pageable()
    {
    }
}