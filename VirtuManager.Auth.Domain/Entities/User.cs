using System.ComponentModel.DataAnnotations;
using VirtuManager.Auth.Domain.Entities.Base;
using VirtuManager.Auth.Domain.Enums;

namespace VirtuManager.Auth.Domain.Entities;

public class User : Entity
{
    public required string Login { get; set; }
    public required string Email { get; set; }
    public required string PasswordHash { get; set; }
    public string? RefreshTokenHash { get; set; }
    
    [EnumDataType(typeof(UserRole))]
    public required UserRole Role { get; set; }
    
    public DateTime? LastLoginAt { get; set; }
    public DateTime? LastPasswordChangeAt { get; set; }
}