using VirtuManager.Auth.Domain.Entities.Base;

namespace VirtuManager.Auth.Domain.Entities;

public class Token : Entity
{
    public required string HashedToken { get; set; }
    public DateTime ExpiresAt { get; set; }
    public DateTime? TerminatedAt { get; set; }
    public bool IsActive { get; set; }
    public DateTime? LastActivity { get; set; } = DateTime.UtcNow;
    
    
    public Guid UserId { get; set; }
    
    public User User { get; set; } = null!;
}