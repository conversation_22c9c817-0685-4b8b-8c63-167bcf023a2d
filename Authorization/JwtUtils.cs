using System.IdentityModel.Tokens.Jwt;
using System.Security.Claims;
using Microsoft.IdentityModel.Tokens;
using VirtuManagerBackend.Domain.Db.Tables.UserTable;

namespace VirtuManagerBackend.Authorization;

public class JwtUtils
{
    private readonly AuthConfiguration _configuration;
    
    public JwtUtils(AuthConfiguration configuration)
    {
        _configuration = configuration;
    }
    
    public JwtSecurityToken GetSecurityTokenByUser(User user)
    {
        var jwt = new JwtSecurityToken(
            issuer: _configuration.Issuer,
            audience: _configuration.Audience,
            claims: new List<Claim>()
            {
                new Claim(ClaimTypes.Name, user.Login),
                new Claim(ClaimTypes.Role, user.Role.ToString())
            },
            expires: DateTime.UtcNow.Add(TimeSpan.FromDays(5)),
            signingCredentials: new SigningCredentials(_configuration.GetSymmetricSecurityKey(), SecurityAlgorithms.HmacSha256)
        );

        return jwt;
    }
}