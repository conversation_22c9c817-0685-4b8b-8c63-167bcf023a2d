using FluentResults;
using MediatR;
using System.ComponentModel.DataAnnotations;

namespace VirtuManager.Auth.Application.Features.Auth.Commands;

public record LoginCommand : IRequest<Result<LoginResult>>
{
    [Required]
    public string Login { get; init; } = string.Empty;
    
    [Required]
    public string Password { get; init; } = string.Empty;
}

public record LoginResult
{
    public string AccessToken { get; init; } = string.Empty;
    public string RefreshToken { get; init; } = string.Empty;
}
