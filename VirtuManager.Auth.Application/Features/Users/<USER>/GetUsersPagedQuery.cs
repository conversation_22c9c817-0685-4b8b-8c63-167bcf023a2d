using FluentResults;
using MediatR;
using System.ComponentModel.DataAnnotations;

namespace VirtuManager.Auth.Application.Features.Users.Queries;

public record GetUsersPagedQuery : IRequest<Result<GetUsersPagedResult>>
{
    [Range(1, int.MaxValue)]
    public int PageNumber { get; init; } = 1;
    
    [Range(1, 100)]
    public int PageSize { get; init; } = 10;
}

public record GetUsersPagedResult
{
    public IEnumerable<UserDto> Users { get; init; } = Enumerable.Empty<UserDto>();
    public int TotalCount { get; init; }
    public int PageNumber { get; init; }
    public int PageSize { get; init; }
    public int TotalPages { get; init; }
}
