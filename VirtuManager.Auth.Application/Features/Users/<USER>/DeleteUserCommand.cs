using FluentResults;
using MediatR;
using System.ComponentModel.DataAnnotations;

namespace VirtuManager.Auth.Application.Features.Users.Commands;

public record DeleteUserCommand : IRequest<Result<DeleteUserResult>>
{
    [Required]
    public Guid UserId { get; init; }
    
    [Required]
    public string CurrentUserLogin { get; init; } = string.Empty;
}

public record DeleteUserResult
{
    public Guid DeletedUserId { get; init; }
    public string DeletedUserLogin { get; init; } = string.Empty;
}
