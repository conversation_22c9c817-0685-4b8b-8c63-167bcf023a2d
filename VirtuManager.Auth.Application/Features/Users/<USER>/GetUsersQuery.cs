using FluentResults;
using MediatR;
using VirtuManager.Auth.Domain.Entities;

namespace VirtuManager.Auth.Application.Features.Users.Queries;

public record GetUsersQuery : IRequest<Result<GetUsersResult>>
{
}

public record GetUsersResult
{
    public IEnumerable<UserDto> Users { get; init; } = Enumerable.Empty<UserDto>();
}

public record UserDto
{
    public Guid Id { get; init; }
    public string Login { get; init; } = string.Empty;
    public string Email { get; init; } = string.Empty;
    public string Role { get; init; } = string.Empty;
    public DateTime CreatedAt { get; init; }
    public DateTime? UpdatedAt { get; init; }
    public DateTime? LastLoginAt { get; init; }
}
