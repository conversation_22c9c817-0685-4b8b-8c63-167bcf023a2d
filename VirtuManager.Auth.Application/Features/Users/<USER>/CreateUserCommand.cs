using FluentResults;
using MediatR;
using System.ComponentModel.DataAnnotations;
using VirtuManager.Auth.Domain.Enums;

namespace VirtuManager.Auth.Application.Features.Users.Commands;

public record CreateUserCommand : IRequest<Result<CreateUserResult>>
{
    [Required]
    public string Login { get; init; } = string.Empty;
    
    [Required]
    public string Email { get; init; } = string.Empty;
    
    [Required]
    [MinLength(8)]
    public string Password { get; init; } = string.Empty;
    
    public UserRole? Role { get; init; }
}

public record CreateUserResult
{
    public Guid UserId { get; init; }
    public string Login { get; init; } = string.Empty;
    public string Email { get; init; } = string.Empty;
    public UserRole Role { get; init; }
    public DateTime CreatedAt { get; init; }
}
