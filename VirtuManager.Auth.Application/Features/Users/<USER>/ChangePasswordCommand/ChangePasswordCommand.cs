using System.ComponentModel.DataAnnotations;
using FluentResults;
using MediatR;

namespace VirtuManager.Auth.Application.Features.Users.Commands.ChangePasswordCommand;

public record ChangePasswordCommand : IRequest<Result<Result>>
{
    [Required]
    public Guid UserId { get; init; }
    
    [Required]
    public string CurrentPassword { get; init; } = string.Empty;
    
    [Required]
    [MinLength(8)]
    public string NewPassword { get; init; } = string.Empty;
    
    [Required]
    [MinLength(8)]
    public string ConfirmNewPassword { get; init; } = string.Empty;
}