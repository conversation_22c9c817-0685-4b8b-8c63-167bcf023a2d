using System.ComponentModel.DataAnnotations;
using FluentResults;
using MediatR;
using VirtuManager.Auth.Domain.Enums;

namespace VirtuManager.Auth.Application.Features.Users.Commands.CreateUserCommand;

public record CreateUserCommand : IRequest<Result<Result<Guid>>>
{
    [Required]
    public string Login { get; init; } = string.Empty;
    
    [Required]
    public string Email { get; init; } = string.Empty;
    
    [Required]
    [MinLength(8)]
    public string Password { get; init; } = string.Empty;
    
    public UserRole? Role { get; init; }
}