using FluentResults;
using MediatR;
using System.ComponentModel.DataAnnotations;

namespace VirtuManager.Auth.Application.Features.Users.Commands;

public record ChangePasswordCommand : IRequest<Result<ChangePasswordResult>>
{
    [Required]
    public Guid UserId { get; init; }
    
    [Required]
    public string CurrentPassword { get; init; } = string.Empty;
    
    [Required]
    [MinLength(8)]
    public string NewPassword { get; init; } = string.Empty;
    
    [Required]
    [MinLength(8)]
    public string ConfirmNewPassword { get; init; } = string.Empty;
}

public record ChangePasswordResult
{
    public Guid UserId { get; init; }
    public string Login { get; init; } = string.Empty;
    public DateTime UpdatedAt { get; init; }
}
