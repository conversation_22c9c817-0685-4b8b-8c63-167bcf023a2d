<Project Sdk="Microsoft.NET.Sdk">

    <PropertyGroup>
        <TargetFramework>net8.0</TargetFramework>
        <ImplicitUsings>enable</ImplicitUsings>
        <Nullable>enable</Nullable>
    </PropertyGroup>

    <ItemGroup>
      <PackageReference Include="FluentValidation" Version="12.0.0" />
      <PackageReference Include="MediatR" Version="12.5.0" />
    </ItemGroup>

    <ItemGroup>
      <ProjectReference Include="..\VirtuManager.Auth.Domain\VirtuManager.Auth.Domain.csproj" />
    </ItemGroup>

    <ItemGroup>
      <Folder Include="Features\Auth\Queries\" />
      <Folder Include="Features\Users\Commands\DeleteUserCommand\" />
      <Folder Include="Services\" />
    </ItemGroup>

</Project>
