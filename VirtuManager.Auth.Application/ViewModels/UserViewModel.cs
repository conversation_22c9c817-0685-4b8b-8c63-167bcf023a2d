using System.ComponentModel.DataAnnotations;
using VirtuManager.Auth.Domain.Enums;

namespace VirtuManager.Auth.Application.ViewModels;

public record UserViewModel
{
    public Guid Id { get; init; }
    public string Login { get; init; } = string.Empty;
    public string Email { get; init; } = string.Empty;

    [EnumDataType(typeof(UserRole))] public UserRole Role { get; init; }

    public DateTime CreatedAt { get; init; }
    public DateTime? UpdatedAt { get; init; }
    public DateTime? LastLoginAt { get; init; }
    public DateTime? LastPasswordChangeAt { get; init; }
}