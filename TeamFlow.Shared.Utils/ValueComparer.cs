namespace TeamFlow.Shared.Utils;

public static class ValueComparer
{
    public static bool IsUpdated<T>(T oldValue, T? newValue)
    {
        if (newValue is null)
        {
            return false;
        }

        return !EqualityComparer<T>.Default.Equals(oldValue, newValue);
    }
    
    public static T? GetNewValueIfUpdated<T>(T oldValue, T? newValue) => IsUpdated(oldValue, newValue) ? newValue : default;
    
    public static bool IsUpdated(string oldValue, string? newValue)
    {
        return !string.IsNullOrWhiteSpace(newValue)
               && !string.Equals(oldValue, newValue, StringComparison.Ordinal);
    }
}