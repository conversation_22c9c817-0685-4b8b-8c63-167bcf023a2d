using System.Linq.Expressions;

namespace TeamFlow.Shared.Utils;

/// <summary>
/// Утилитарный класс для построения лямбда-выражений, используемых в операциях фильтрации данных.
/// Предоставляет методы для создания предикатов различных типов сравнения и логических операций.
/// </summary>
/// <remarks>
/// Данный класс облегчает создание динамических запросов для ORM-систем, таких как Entity Framework,
/// позволяя строить сложные предикаты во время выполнения программы на основе пользовательского ввода
/// или других условий.
/// </remarks>
/// <example>
/// Пример использования для создания динамического фильтра:
/// <code>
/// var parameter = Expression.Parameter(typeof(User), "user");
/// var containsExpression = ExpressionBuilder.BuildStringContains&lt;User&gt;(parameter, "Name", "John", true);
/// var ageExpression = ExpressionBuilder.BuildGreaterThan&lt;User, int&gt;(parameter, "Age", 18);
/// var combinedExpression = ExpressionBuilder.CombineWithAnd(containsExpression, ageExpression);
/// var lambda = Expression.Lambda&lt;Func&lt;User, bool&gt;&gt;(combinedExpression, parameter);
/// var filteredUsers = users.Where(lambda.Compile()).ToList();
/// </code>
/// </example>
public static class ExpressionBuilder
{
    /// <summary>
    /// Создает предикат для проверки, содержит ли строка указанную подстроку.
    /// </summary>
    /// <typeparam name="TEntity">Тип сущности</typeparam>
    /// <param name="parameter">Параметр выражения</param>
    /// <param name="propertyName">Имя свойства</param>
    /// <param name="value">Значение для проверки</param>
    /// <param name="ignoreCase">Игнорировать регистр при сравнении</param>
    /// <returns>Выражение, представляющее условие содержания подстроки</returns>
    public static Expression? BuildStringContains<TEntity>(
        ParameterExpression parameter,
        string propertyName,
        string? value,
        bool ignoreCase = false)
    {
        if (string.IsNullOrWhiteSpace(value))
            return null;

        var property = Expression.Property(parameter, propertyName);
        var stringValue = Expression.Constant(value);
        
        if (ignoreCase)
        {
            var containsMethod = typeof(string).GetMethod("Contains", [typeof(string), typeof(StringComparison)]);
            var comparison = Expression.Constant(StringComparison.CurrentCultureIgnoreCase);
            return Expression.Call(property, containsMethod!, stringValue, comparison);
        }
        else
        {
            var containsMethod = typeof(string).GetMethod("Contains", [typeof(string)]);
            return Expression.Call(property, containsMethod!, stringValue);
        }
    }

    /// <summary>
    /// Создает предикат для проверки, содержит ли коллекция указанное значение.
    /// </summary>
    /// <typeparam name="TEntity">Тип сущности</typeparam>
    /// <typeparam name="TItem">Тип элемента коллекции</typeparam>
    /// <param name="parameter">Параметр выражения</param>
    /// <param name="propertyName">Имя свойства-коллекции</param>
    /// <param name="value">Значение для проверки</param>
    /// <returns>Выражение, представляющее условие содержания элемента</returns>
    public static Expression? BuildContains<TEntity, TItem>(
        ParameterExpression parameter,
        string propertyName,
        TItem? value)
    {
        if (value == null)
            return null;
    
        var property = Expression.Property(parameter, propertyName);
        var containsMethod = typeof(ICollection<TItem>).GetMethod("Contains", [typeof(TItem)]);
        var constantValue = Expression.Constant(value);
        
        return Expression.Call(property, containsMethod!, constantValue);
    }

    /// <summary>
    /// Создает предикат для сравнения "больше чем".
    /// </summary>
    /// <typeparam name="TEntity">Тип сущности</typeparam>
    /// <typeparam name="TProperty">Тип свойства</typeparam>
    /// <param name="parameter">Параметр выражения</param>
    /// <param name="propertyName">Имя свойства</param>
    /// <param name="value">Значение для сравнения</param>
    /// <returns>Выражение, представляющее условие "больше чем"</returns>
    public static Expression? BuildGreaterThan<TEntity, TProperty>(
        ParameterExpression parameter,
        string propertyName,
        TProperty? value) where TProperty : struct, IComparable<TProperty>
    {
        if (value == null)
            return null;
    
        var property = Expression.Property(parameter, propertyName);
        var constantValue = Expression.Constant(value);
        
        return Expression.GreaterThan(property, constantValue);
    }

    /// <summary>
    /// Создает предикат для сравнения "больше или равно".
    /// </summary>
    /// <typeparam name="TEntity">Тип сущности</typeparam>
    /// <typeparam name="TProperty">Тип свойства</typeparam>
    /// <param name="parameter">Параметр выражения</param>
    /// <param name="propertyName">Имя свойства</param>
    /// <param name="value">Значение для сравнения</param>
    /// <returns>Выражение, представляющее условие "больше или равно"</returns>
    public static Expression? BuildGreaterThanOrEqual<TEntity, TProperty>(
        ParameterExpression parameter,
        string propertyName,
        TProperty? value) where TProperty : struct, IComparable<TProperty>
    {
        if (value == null)
            return null;
    
        var property = Expression.Property(parameter, propertyName);
        var constantValue = Expression.Constant(value);
        
        return Expression.GreaterThanOrEqual(property, constantValue);
    }

    /// <summary>
    /// Создает предикат для сравнения "меньше чем".
    /// </summary>
    /// <typeparam name="TEntity">Тип сущности</typeparam>
    /// <typeparam name="TProperty">Тип свойства</typeparam>
    /// <param name="parameter">Параметр выражения</param>
    /// <param name="propertyName">Имя свойства</param>
    /// <param name="value">Значение для сравнения</param>
    /// <returns>Выражение, представляющее условие "меньше чем"</returns>
    public static Expression? BuildLessThan<TEntity, TProperty>(
        ParameterExpression parameter,
        string propertyName,
        TProperty? value) where TProperty : struct, IComparable<TProperty>
    {
        if (value == null)
            return null;
    
        var property = Expression.Property(parameter, propertyName);
        var constantValue = Expression.Constant(value);
        
        return Expression.LessThan(property, constantValue);
    }

    /// <summary>
    /// Создает предикат для сравнения "меньше или равно".
    /// </summary>
    /// <typeparam name="TEntity">Тип сущности</typeparam>
    /// <typeparam name="TProperty">Тип свойства</typeparam>
    /// <param name="parameter">Параметр выражения</param>
    /// <param name="propertyName">Имя свойства</param>
    /// <param name="value">Значение для сравнения</param>
    /// <returns>Выражение, представляющее условие "меньше или равно"</returns>
    public static Expression? BuildLessThanOrEqual<TEntity, TProperty>(
        ParameterExpression parameter,
        string propertyName,
        TProperty? value) where TProperty : struct, IComparable<TProperty>
    {
        if (value == null)
            return null;
    
        var property = Expression.Property(parameter, propertyName);
        var constantValue = Expression.Constant(value);
        
        return Expression.LessThanOrEqual(property, constantValue);
    }

    /// <summary>
    /// Создает предикат для проверки, начинается ли строка с указанного значения.
    /// </summary>
    /// <typeparam name="TEntity">Тип сущности</typeparam>
    /// <param name="parameter">Параметр выражения</param>
    /// <param name="propertyName">Имя свойства</param>
    /// <param name="value">Значение для проверки</param>
    /// <param name="ignoreCase">Игнорировать регистр при сравнении</param>
    /// <returns>Выражение, представляющее условие "начинается с"</returns>
    public static Expression? BuildStartsWith<TEntity>(
        ParameterExpression parameter,
        string propertyName,
        string? value,
        bool ignoreCase = false)
    {
        if (string.IsNullOrEmpty(value))
            return null;
    
        var property = Expression.Property(parameter, propertyName);
        var stringValue = Expression.Constant(value);
        
        if (ignoreCase)
        {
            var startsWithMethod = typeof(string).GetMethod("StartsWith", [typeof(string), typeof(StringComparison)]);
            var comparison = Expression.Constant(StringComparison.CurrentCultureIgnoreCase);
            return Expression.Call(property, startsWithMethod!, stringValue, comparison);
        }
        else
        {
            var startsWithMethod = typeof(string).GetMethod("StartsWith", [typeof(string)]);
            return Expression.Call(property, startsWithMethod!, stringValue);
        }
    }

    /// <summary>
    /// Создает предикат для проверки, заканчивается ли строка указанным значением.
    /// </summary>
    /// <typeparam name="TEntity">Тип сущности</typeparam>
    /// <param name="parameter">Параметр выражения</param>
    /// <param name="propertyName">Имя свойства</param>
    /// <param name="value">Значение для проверки</param>
    /// <param name="ignoreCase">Игнорировать регистр при сравнении</param>
    /// <returns>Выражение, представляющее условие "заканчивается на"</returns>
    public static Expression? BuildEndsWith<TEntity>(
        ParameterExpression parameter,
        string propertyName,
        string? value,
        bool ignoreCase = false)
    {
        if (string.IsNullOrEmpty(value))
            return null;
    
        var property = Expression.Property(parameter, propertyName);
        var stringValue = Expression.Constant(value);
        
        if (ignoreCase)
        {
            var endsWithMethod = typeof(string).GetMethod("EndsWith", [typeof(string), typeof(StringComparison)]);
            var comparison = Expression.Constant(StringComparison.CurrentCultureIgnoreCase);
            return Expression.Call(property, endsWithMethod!, stringValue, comparison);
        }
        else
        {
            var endsWithMethod = typeof(string).GetMethod("EndsWith", [typeof(string)]);
            return Expression.Call(property, endsWithMethod!, stringValue);
        }
    }

    /// <summary>
    /// Создает предикат для проверки, находится ли значение в указанном диапазоне.
    /// </summary>
    /// <typeparam name="TEntity">Тип сущности</typeparam>
    /// <typeparam name="TProperty">Тип свойства</typeparam>
    /// <param name="parameter">Параметр выражения</param>
    /// <param name="propertyName">Имя свойства</param>
    /// <param name="min">Минимальное значение диапазона</param>
    /// <param name="max">Максимальное значение диапазона</param>
    /// <returns>Выражение, представляющее условие "в диапазоне"</returns>
    public static Expression? BuildBetween<TEntity, TProperty>(
        ParameterExpression parameter,
        string propertyName,
        TProperty? min,
        TProperty? max) where TProperty : struct, IComparable<TProperty>
    {
        if (min == null && max == null)
            return null;
            
        var property = Expression.Property(parameter, propertyName);
        
        Expression? minExpression = null;
        if (min != null)
        {
            var minConstant = Expression.Constant(min.Value);
            minExpression = Expression.GreaterThanOrEqual(property, minConstant);
        }
        
        Expression? maxExpression = null;
        if (max != null)
        {
            var maxConstant = Expression.Constant(max.Value);
            maxExpression = Expression.LessThanOrEqual(property, maxConstant);
        }
        
        if (minExpression != null && maxExpression != null)
            return Expression.AndAlso(minExpression, maxExpression);
        
        return minExpression ?? maxExpression;
    }

    /// <summary>
    /// Создает предикат для проверки, равно ли значение null.
    /// </summary>
    /// <typeparam name="TEntity">Тип сущности</typeparam>
    /// <param name="parameter">Параметр выражения</param>
    /// <param name="propertyName">Имя свойства</param>
    /// <returns>Выражение, представляющее условие "равно null"</returns>
    public static Expression BuildIsNull<TEntity>(
        ParameterExpression parameter,
        string propertyName)
    {
        var property = Expression.Property(parameter, propertyName);
        return Expression.Equal(property, Expression.Constant(null, property.Type));
    }

    /// <summary>
    /// Создает предикат для проверки, не равно ли значение null.
    /// </summary>
    /// <typeparam name="TEntity">Тип сущности</typeparam>
    /// <param name="parameter">Параметр выражения</param>
    /// <param name="propertyName">Имя свойства</param>
    /// <returns>Выражение, представляющее условие "не равно null"</returns>
    public static Expression BuildIsNotNull<TEntity>(
        ParameterExpression parameter,
        string propertyName)
    {
        var property = Expression.Property(parameter, propertyName);
        return Expression.NotEqual(property, Expression.Constant(null, property.Type));
    }

    /// <summary>
    /// Создает предикат для проверки, равно ли значение указанному.
    /// </summary>
    /// <typeparam name="TEntity">Тип сущности</typeparam>
    /// <typeparam name="TProperty">Тип свойства</typeparam>
    /// <param name="parameter">Параметр выражения</param>
    /// <param name="propertyName">Имя свойства</param>
    /// <param name="value">Значение для сравнения</param>
    /// <returns>Выражение, представляющее условие равенства</returns>
    public static Expression? BuildEqual<TEntity, TProperty>(
        ParameterExpression parameter,
        string propertyName,
        TProperty? value)
    {
        if (value == null)
            return null;
            
        var property = Expression.Property(parameter, propertyName);
        var constantValue = Expression.Constant(value);
        
        return Expression.Equal(property, constantValue);
    }

    /// <summary>
    /// Создает предикат для проверки, не равно ли значение указанному.
    /// </summary>
    /// <typeparam name="TEntity">Тип сущности</typeparam>
    /// <typeparam name="TProperty">Тип свойства</typeparam>
    /// <param name="parameter">Параметр выражения</param>
    /// <param name="propertyName">Имя свойства</param>
    /// <param name="value">Значение для сравнения</param>
    /// <returns>Выражение, представляющее условие неравенства</returns>
    public static Expression? BuildNotEqual<TEntity, TProperty>(
        ParameterExpression parameter,
        string propertyName,
        TProperty? value)
    {
        if (value == null)
            return null;
            
        var property = Expression.Property(parameter, propertyName);
        var constantValue = Expression.Constant(value);
        
        return Expression.NotEqual(property, constantValue);
    }

    /// <summary>
    /// Объединяет несколько выражений с помощью оператора AND, фильтруя null-выражения.
    /// </summary>
    /// <param name="expressions">Выражения для объединения</param>
    /// <returns>Объединенное выражение или Expression.Constant(true), если нет допустимых выражений</returns>
    public static Expression CombineWithAnd(params Expression?[] expressions)
    {
        var validExpressions = expressions.Where(e => e != null).ToArray();
        
        if (validExpressions.Length == 0)
            return Expression.Constant(true);
            
        var result = validExpressions[0]!;
        
        for (var i = 1; i < validExpressions.Length; i++)
        {
            result = Expression.AndAlso(result, validExpressions[i]!);
        }
        
        return result;
    }
    
    /// <summary>
    /// Объединяет несколько выражений с помощью оператора OR, фильтруя null-выражения.
    /// </summary>
    /// <param name="expressions">Выражения для объединения</param>
    /// <returns>Объединенное выражение или Expression.Constant(false), если нет допустимых выражений</returns>
    public static Expression CombineWithOr(params Expression?[] expressions)
    {
        var validExpressions = expressions.Where(e => e != null).ToArray();
        
        if (validExpressions.Length == 0)
            return Expression.Constant(false);
            
        var result = validExpressions[0]!;
        
        for (var i = 1; i < validExpressions.Length; i++)
        {
            result = Expression.OrElse(result, validExpressions[i]!);
        }
        
        return result;
    }
}