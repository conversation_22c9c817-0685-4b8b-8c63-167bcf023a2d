using System.Linq.Expressions;
using TeamFlow.Shared.Contracts.Pagination;
using TeamFlow.Shared.Contracts.Sorting;

namespace TeamFlow.Shared.Repositories.Repositories.Common;

/// <summary>
/// Интерфейс для получения всех сущностей типа <typeparamref name="TEntity"/>.
/// </summary>
/// <typeparam name="TEntity">Тип сущности, с которой работает репозиторий. Должен быть классом.</typeparam>
public interface IGetMany<TEntity> where TEntity : class
{
    /// <summary>
    /// Асинхронно получает все сущности типа <typeparamref name="TEntity"/> без применения пагинации.
    /// </summary>
    /// <param name="predicate">Лямбда-выражение для фильтрации сущностей. Если значение равно <c>null</c>, фильтрация не применяется.</param>
    /// <param name="sortExpression">Объект <see cref="OrderByExpression{TEntity}"/>, определяющий порядок сортировки. Если значение равно <c>null</c>, сортировка не применяется.</param>
    /// <param name="cancellationToken">Токен отмены операции.</param>
    /// <returns>Задача, представляющая асинхронную операцию. Результатом является коллекция всех сущностей типа <typeparamref name="TEntity"/>.</returns>
    Task<IEnumerable<TEntity>> GetManyAsync(Expression<Func<TEntity, bool>>? predicate = null,
        OrderByExpression<TEntity>? sortExpression = null, CancellationToken cancellationToken = default);

    /// <summary>
    /// Асинхронно получает постранично список сущностей типа <typeparamref name="TEntity"/>.
    /// </summary>
    /// <param name="pageable">Объект <see cref="Pageable"/>, содержащий информацию о номере страницы и размере страницы.</param>
    /// <param name="predicate">Лямбда-выражение для фильтрации сущностей. Если значение равно <c>null</c>, фильтрация не применяется.</param>
    /// <param name="sortExpression">Объект <see cref="OrderByExpression{TEntity}"/>, определяющий порядок сортировки. Если значение равно <c>null</c>, сортировка не применяется.</param>
    /// <param name="cancellationToken">Токен отмены операции.</param>
    /// <returns>Задача, представляющая асинхронную операцию. Результатом является объект <see cref="PagedResult{TEntity}"/>, содержащий коллекцию сущностей на текущей странице и метаданные пагинации.</returns>
    Task<PagedResult<TEntity>> GetManyAsync(Pageable pageable, Expression<Func<TEntity, bool>>? predicate = null,
        OrderByExpression<TEntity>? sortExpression = null, CancellationToken cancellationToken = default);
}