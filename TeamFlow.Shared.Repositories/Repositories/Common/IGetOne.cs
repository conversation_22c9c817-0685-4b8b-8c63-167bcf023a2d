using System.Linq.Expressions;

namespace TeamFlow.Shared.Repositories.Repositories.Common;

/// <summary>
/// Интерфейс для получения одной сущности тип <typeparamref name="TEntity"/>.
/// </summary>
/// <typeparam name="TEntity">Тип сущности для получения, должен быть классом.</typeparam>
public interface IGetOne<TEntity> where TEntity : class
{
    /// <summary>
    /// Асинхронно получает одну сущность на основе указанного предиката.
    /// </summary>
    /// <param name="predicate">Выражение, используемое для фильтрации сущностей.</param>
    /// <param name="cancellationToken">Токен для отмены операции при необходимости.</param>
    /// <returns>Задача, представляющая асинхронную операцию. Результатом является сущность типа <typeparamref name="TEntity"/>.</returns>
    Task<TEntity?> GetOneAsync(Expression<Func<TEntity, bool>> predicate, CancellationToken cancellationToken = default);
}