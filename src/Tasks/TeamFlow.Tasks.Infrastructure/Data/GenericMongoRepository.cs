using System.Linq.Expressions;
using MongoDB.Driver;
using TeamFlow.Shared.Contracts.Pagination;
using TeamFlow.Shared.Contracts.Sorting;
using TeamFlow.Shared.Repositories.Entities;
using TeamFlow.Tasks.Core.Abstractions.Repositories;
using SortDirection = TeamFlow.Shared.Contracts.Enums.SortDirection;

namespace TeamFlow.Tasks.Infrastructure.Data;

public class GenericMongoRepository<TEntity, TKey> : IRepository<TEntity, TKey>
    where TEntity : Entity<TKey> where TKey : IEquatable<TKey>
{
    private readonly IMongoCollection<TEntity> _collection;

    public GenericMongoRepository(IMongoDatabase database, string collectionName)
    {
        ArgumentNullException.ThrowIfNull(database, nameof(database));

        if (string.IsNullOrWhiteSpace(collectionName))
        {
            throw new ArgumentException($"{nameof(collectionName)} is null or empty", nameof(collectionName));
        }

        // var collectionName = nameof(TEntity);
        _collection = database.GetCollection<TEntity>(collectionName);
    }

    public async Task<TEntity?> GetOneAsync(
        Expression<Func<TEntity, bool>> predicate,
        CancellationToken cancellationToken = default)
    {
        return await _collection.Find(predicate).FirstOrDefaultAsync(cancellationToken);
    }

    public async Task<IEnumerable<TEntity>> GetManyAsync(
        Expression<Func<TEntity, bool>>? predicate = null,
        OrderByExpression<TEntity>? sortExpression = null,
        CancellationToken cancellationToken = default)
    {
        var filter = BuildFilter(predicate);

        var find = _collection.Find(filter);

        if (sortExpression is null)
        {
            return await find.ToListAsync(cancellationToken);
        }

        find = find.Sort(BuildSort(sortExpression));

        return await find.ToListAsync(cancellationToken);
    }

    public async Task<PagedResult<TEntity>> GetManyAsync(
        Pageable pageable,
        Expression<Func<TEntity, bool>>? predicate = null,
        OrderByExpression<TEntity>? sortExpression = null,
        CancellationToken cancellationToken = default)
    {
        int pageNumber = Math.Max(1, pageable.PageNumber);
        int pageSize = Math.Clamp(pageable.PageSize, 1, 1000);

        var filter = BuildFilter(predicate);

        var find = _collection.Find(filter);

        var totalCount =
            await _collection.CountDocumentsAsync(
                filter, cancellationToken: cancellationToken);

        if (sortExpression is not null)
        {
            find = find.Sort(BuildSort(sortExpression));
        }

        var elements = await find
            .Skip((pageNumber - 1) * pageSize)
            .Limit(pageSize)
            .ToListAsync(cancellationToken);

        return new PagedResult<TEntity>(elements, totalCount);
    }

    public virtual async Task AddAsync(TEntity entity,
        CancellationToken cancellationToken = default)
    {
        await _collection.InsertOneAsync(entity, null, cancellationToken);
    }

    public virtual async Task UpdateAsync(TEntity entity,
        CancellationToken cancellationToken = default)
    {
        var filter = Builders<TEntity>.Filter.Eq(e => e.Id, entity.Id);

        var result = await _collection.ReplaceOneAsync(
            filter,
            entity,
            new ReplaceOptions { IsUpsert = false },
            cancellationToken);

        if (!result.IsAcknowledged)
        {
            throw new MongoException($"Update failed for entity {nameof(TEntity)} with ID {entity.Id}");
        }
    }

    public virtual async Task RemoveAsync(TEntity entity,
        CancellationToken cancellationToken = default)
    {
        var filter = Builders<TEntity>.Filter.Eq(e => e.Id, entity.Id);
        await _collection.DeleteOneAsync(filter, cancellationToken); /*var result = */
        // return result.IsAcknowledged && result.DeletedCount > 0;
    }

    public async Task<bool> FindAnyAsync(Expression<Func<TEntity, bool>> predicate,
        CancellationToken cancellationToken = default)
    {
        return await _collection
            .Find(predicate)
            .AnyAsync(cancellationToken);
    }


    public async Task BulkInsertAsync(IEnumerable<TEntity> entities,
        CancellationToken cancellationToken = default)
    {
        ArgumentNullException.ThrowIfNull(entities, nameof(entities));

        var list = entities.ToList();

        if (list.Count == 0)
        {
            return;
        }

        await _collection.InsertManyAsync(list, new InsertManyOptions { IsOrdered = false }, cancellationToken);
    }

    public async Task BulkUpdateAsync(IEnumerable<TEntity> entities,
        CancellationToken cancellationToken = default)
    {
        ArgumentNullException.ThrowIfNull(entities, nameof(entities));

        var list = entities.ToList();

        if (list.Count == 0)
        {
            return;
        }

        var models = list
            .Select(WriteModel<TEntity> (entity) => new ReplaceOneModel<TEntity>(
                Builders<TEntity>.Filter.Eq(e => e.Id, entity.Id),
                entity)
            {
                IsUpsert = false // не вставляем новые, только обновляем существующие
            })
            .ToList();

        var result = await _collection.BulkWriteAsync(
            models,
            new BulkWriteOptions { IsOrdered = false },
            cancellationToken);

        if (!result.IsAcknowledged)
        {
            throw new MongoException(
                $"Bulk update for {typeof(TEntity).Name} failed: IsAcknowledged = false");
        }
    }

    public async Task BulkDeleteAsync(IEnumerable<TKey> ids,
        CancellationToken cancellationToken = default)
    {
        ArgumentNullException.ThrowIfNull(ids, nameof(ids));

        var idList = ids.Distinct().ToList();

        if (idList.Count == 0)
        {
            return;
        }

        var filter = Builders<TEntity>.Filter.In(e => e.Id, idList);
        var result = await _collection.DeleteManyAsync(filter, cancellationToken);

        if (!result.IsAcknowledged)
        {
            throw new MongoException($"Bulk delete failed for {typeof(TEntity).Name} by ids.");
        }
    }

    public async Task BulkDeleteAsync(Expression<Func<TEntity, bool>> predicate,
        CancellationToken cancellationToken = default)
    {
        var filter = BuildFilter(predicate);
        var result = await _collection.DeleteManyAsync(filter, cancellationToken);

        if (!result.IsAcknowledged)
        {
            throw new MongoException($"Bulk delete failed for {typeof(TEntity).Name} by ids.");
        }
    }

    private static SortDefinition<TEntity> BuildSort(OrderByExpression<TEntity> orderByExpression)
    {
        ArgumentNullException.ThrowIfNull(orderByExpression, nameof(orderByExpression));

        var builder = Builders<TEntity>.Sort;

        return orderByExpression.Direction switch
        {
            SortDirection.Ascending => builder.Ascending(orderByExpression.KeySelector),
            SortDirection.Descending => builder.Descending(orderByExpression.KeySelector),
            _ => builder.Ascending(orderByExpression.KeySelector)
        };
    }

    private static FilterDefinition<TEntity> BuildFilter(Expression<Func<TEntity, bool>>? predicate)
    {
        return predicate is null
            ? Builders<TEntity>.Filter.Empty
            : Builders<TEntity>.Filter.Where(predicate);
    }
}