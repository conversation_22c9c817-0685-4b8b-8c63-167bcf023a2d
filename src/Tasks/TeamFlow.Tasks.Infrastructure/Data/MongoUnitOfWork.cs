using MongoDB.Driver;
using TeamFlow.Tasks.Core.Abstractions.Repositories;

namespace TeamFlow.Tasks.Infrastructure.Data;

public class MongoUnitOfWork : IUnitOfWork
{
    private readonly IMongoClient _client;

    private IClientSessionHandle? _session;
    
    public bool HasActiveTransaction => _session?.IsInTransaction == true;
    
    public MongoUnitOfWork(IMongoClient client)
    {
        ArgumentNullException.ThrowIfNull(client, nameof(client));
        
        _client = client;
    }

    public async System.Threading.Tasks.Task BeginTransactionAsync(CancellationToken cancellationToken = default)
    {
        if (_session is not null)
        {
            if (_session.IsInTransaction)
            {
                return;
            }
            
            _session.StartTransaction();
            return;
        }
        
        _session = await _client.StartSessionAsync(cancellationToken: cancellationToken);
        _session.StartTransaction();
    }

    public async System.Threading.Tasks.Task CommitAsync(CancellationToken cancellationToken = default)
    {
        if (_session?.IsInTransaction == true)
        {
            await _session.CommitTransactionAsync(cancellationToken);
        }
        Dispose();
    }

    public async System.Threading.Tasks.Task RollbackAsync(CancellationToken cancellationToken = default)
    {
        if (_session?.IsInTransaction == true)
        {
            await _session.AbortTransactionAsync(cancellationToken);
        }
        Dispose();
    }

    public object? GetCurrentSession()
    {
        return _session;
    }
    
    public void Dispose()
    {
        _session?.AbortTransaction();
        _session?.Dispose();
        _session = null;
    }
}