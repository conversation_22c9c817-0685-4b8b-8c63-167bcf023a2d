namespace TeamFlow.Tasks.Infrastructure.Caching;

public class MemoryCacheOptions
{
    /// <summary>
    /// Ограничение размера кеша в байтах
    /// </summary>
    public long? SizeLimit { get; set; } = 1024 * 1024 * 500; // 500 МБ по умолчанию
    
    /// <summary>
    /// Частота сканирования для удаления просроченных элементов
    /// </summary>
    public TimeSpan ExpirationScanFrequency { get; set; } = TimeSpan.FromMinutes(5);
    
    /// <summary>
    /// Процент элементов для удаления при достижении лимита размера
    /// </summary>
    public double CompactionPercentage { get; set; } = 0.2; // 20% по умолчанию
}