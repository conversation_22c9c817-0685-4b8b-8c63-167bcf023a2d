using System.Threading.Channels;
using AsyncKeyedLock;
using LazyCache;
using Microsoft.Extensions.Caching.Distributed;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;

namespace TeamFlow.Tasks.Infrastructure.Caching;

public class HotKeysProcessingBackgroundService : BackgroundService
{
    private readonly IAppCache _appCache;
    private readonly IDistributedCache _distributedCache;
    private readonly Channel<string> _hotKeysChannel;
    private readonly AsyncKeyedLocker<string> _asyncKeyedLocker = new();

    private readonly ILogger<HotKeysProcessingBackgroundService> _logger;
    private readonly TwoLevelCacheOptions _options;

    public HotKeysProcessingBackgroundService(
        IAppCache appCache, 
        IDistributedCache distributedCache,
        Channel<string> hotKeysChannel, 
        ILogger<HotKeysProcessingBackgroundService> logger,
        TwoLevelCacheOptions options)
    {
        _appCache = appCache;
        _distributedCache = distributedCache;
        _hotKeysChannel = hotKeysChannel;
        _logger = logger;
        _options = options;
    }

    protected override async Task ExecuteAsync(CancellationToken stoppingToken)
    {
        try
        {
            while (!stoppingToken.IsCancellationRequested)
            {
                var key = await _hotKeysChannel.Reader.ReadAsync(stoppingToken);

                if (_appCache.Get<object>(key) is not null)
                {
                    continue;
                }

                using (await _asyncKeyedLocker.LockAsync(key, stoppingToken))
                {
                    if (_appCache.Get<object>(key) is not null)
                    {
                        continue;
                    }

                    try
                    {
                        var data = await _distributedCache.GetStringAsync(key, stoppingToken);
                        if (!string.IsNullOrWhiteSpace(data))
                        {
                            _appCache.Add(key, data, _options.DefaultLocalExpirationTime);
                            _logger.LogTrace("Hot key {Key} promoted to L1 cache", key);
                        }
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, "Error promoting hot key {Key} to L1 cache", key);
                    }
                }
            }
        }
        catch when (stoppingToken.IsCancellationRequested)
        {
            _logger.LogDebug("Hot keys processing cancelled");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in hot keys processing task");
        }
    }

    public override void Dispose()
    {
        _asyncKeyedLocker.Dispose();
        base.Dispose();
    }
}