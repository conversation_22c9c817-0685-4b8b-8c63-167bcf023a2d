<Project Sdk="Microsoft.NET.Sdk">

    <PropertyGroup>
        <TargetFramework>net9.0</TargetFramework>
        <ImplicitUsings>enable</ImplicitUsings>
        <Nullable>enable</Nullable>
    </PropertyGroup>

    <ItemGroup>
        <PackageReference Include="AsyncKeyedLock" Version="7.1.6" />
        <PackageReference Include="Grpc.AspNetCore" Version="2.70.0"/>
        <PackageReference Include="Grpc.Net.Client" Version="2.70.0"/>
        <PackageReference Include="Grpc.Tools" Version="2.71.0">
            <PrivateAssets>all</PrivateAssets>
            <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
        </PackageReference>
        <PackageReference Include="LazyCache" Version="2.4.0" />
        <PackageReference Include="Microsoft.EntityFrameworkCore" Version="9.0.4"/>
        <PackageReference Include="Microsoft.EntityFrameworkCore.Tools" Version="9.0.4">
            <PrivateAssets>all</PrivateAssets>
            <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
        </PackageReference>
        <PackageReference Include="MongoDB.Driver" Version="3.3.0"/>
    </ItemGroup>

    <ItemGroup>
        <ProjectReference Include="..\TeamFlow.Tasks.Application\TeamFlow.Tasks.Application.csproj" />
    </ItemGroup>

    <ItemGroup>
        <ProtoBuf Include="Protos/user.proto" GrpcServices="Client"/>
        <!--        <ProtoBuf Include="Protos/user.proto" GrpcServices="Client" OutputDir="GrpcGenerated"/>-->
    </ItemGroup>

</Project>
