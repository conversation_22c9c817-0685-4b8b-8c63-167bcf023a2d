using TeamFlow.Shared.Contracts.Mapping;
using TeamFlow.Tasks.Application.Contracts.Contracts.ViewModels;
using TeamFlow.Tasks.Core.Entities;

namespace TeamFlow.Tasks.Infrastructure.Mappers.Projects;

public class ProjectMapper : IMapper<ProjectViewModel, Project>
{
    private readonly ProjectMappingProfile _mapper;

    public ProjectMapper(ProjectMappingProfile mapper)
    {
        ArgumentNullException.ThrowIfNull(mapper, nameof(mapper));
        _mapper = mapper;
    }

    public ProjectViewModel MapToModel(Project source) => _mapper.MapToViewModel(source);

    public List<ProjectViewModel> MapToModel(IEnumerable<Project> sources)
        => sources.Select(u => _mapper.MapToViewModel(u)).ToList();
}