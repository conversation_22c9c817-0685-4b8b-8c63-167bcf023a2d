using Riok.Mapperly.Abstractions;
using TeamFlow.Tasks.Application.Contracts.Contracts.ViewModels;
using TeamFlow.Tasks.Core.Entities;

namespace TeamFlow.Tasks.Infrastructure.Mappers.Projects;

[Mapper]
public partial class ProjectMappingProfile
{
    [MapperIgnoreSource(nameof(Project.Id))]
    [MapperIgnoreSource(nameof(Project.CreatedAt))]
    [MapperIgnoreSource(nameof(Project.UpdatedAt))]
    [MapperIgnoreSource(nameof(Project.TasksId))]
    public partial ProjectViewModel MapToViewModel(Project project);
}