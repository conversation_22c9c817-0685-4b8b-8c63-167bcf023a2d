using Riok.Mapperly.Abstractions;
using TeamFlow.Tasks.Application.Contracts.Contracts.ViewModels;
using TeamFlow.Tasks.Core.Entities;

namespace TeamFlow.Tasks.Infrastructure.Mappers.KanbanColumns;

[Mapper]
public partial class KanbanColumnMappingProfile
{
    [MapperIgnoreSource(nameof(Comment.Id))]
    [MapperIgnoreSource(nameof(KanbanColumn.CreatedAt))]
    [MapperIgnoreSource(nameof(KanbanColumn.UpdatedAt))]
    public partial KanbanColumnViewModel MapToViewModel(KanbanColumn comment);
}