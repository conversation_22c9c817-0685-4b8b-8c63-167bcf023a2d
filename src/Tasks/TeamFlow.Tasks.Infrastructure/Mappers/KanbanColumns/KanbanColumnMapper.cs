using TeamFlow.Shared.Contracts.Mapping;
using TeamFlow.Tasks.Application.Contracts.Contracts.ViewModels;
using TeamFlow.Tasks.Core.Entities;

namespace TeamFlow.Tasks.Infrastructure.Mappers.KanbanColumns;

public class KanbanColumnMapper : IMapper<KanbanColumnViewModel, KanbanColumn>
{
    private readonly KanbanColumnMappingProfile _mapper;

    public KanbanColumnMapper(KanbanColumnMappingProfile mapper)
    {
        ArgumentNullException.ThrowIfNull(mapper, nameof(mapper));
        _mapper = mapper;
    }

    public KanbanColumnViewModel MapToModel(KanbanColumn source) => _mapper.MapToViewModel(source);

    public List<KanbanColumnViewModel> MapToModel(IEnumerable<KanbanColumn> sources)
        => sources.Select(x => _mapper.MapToViewModel(x)).ToList();
}