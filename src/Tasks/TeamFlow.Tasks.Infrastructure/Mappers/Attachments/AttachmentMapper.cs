using TeamFlow.Shared.Contracts.Mapping;
using TeamFlow.Tasks.Application.Contracts.Contracts.ViewModels;
using TeamFlow.Tasks.Core.Entities;

namespace TeamFlow.Tasks.Infrastructure.Mappers.Attachments;

public class AttachmentMapper : IMapper<AttachmentViewModel, Attachment>
{
    private readonly AttachmentMappingProfile _mapper;

    public AttachmentMapper(AttachmentMappingProfile mapper)
    {
        ArgumentNullException.ThrowIfNull(mapper, nameof(mapper));
        _mapper = mapper;
    }

    public AttachmentViewModel MapToModel(Attachment source) => _mapper.MapToViewModel(source);

    public List<AttachmentViewModel> MapToModel(IEnumerable<Attachment> sources) =>
        sources.Select(a => _mapper.MapToViewModel(a)).ToList();
}