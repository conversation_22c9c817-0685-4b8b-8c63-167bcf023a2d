using Riok.Mapperly.Abstractions;
using TeamFlow.Tasks.Application.Contracts.Contracts.ViewModels;
using TeamFlow.Tasks.Core.Entities;

namespace TeamFlow.Tasks.Infrastructure.Mappers.Attachments;

[Mapper]
public partial class AttachmentMappingProfile
{
    [MapperIgnoreSource(nameof(Attachment.Id))]
    [MapperIgnoreSource(nameof(Attachment.FilePath))]
    public partial AttachmentViewModel MapToViewModel(Attachment attachment);
}