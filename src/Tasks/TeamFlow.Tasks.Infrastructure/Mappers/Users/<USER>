using TeamFlow.Shared.Contracts.Mapping;
using TeamFlow.Tasks.Core.Contracts.External;
using VanId;

namespace TeamFlow.Tasks.Infrastructure.Mappers.Users;

public class UserMapper : IMapper<UserDto, User>
{
    private readonly UserMappingProfile _mapper;

    public UserMapper(UserMappingProfile mapper)
    {
        ArgumentNullException.ThrowIfNull(mapper, nameof(mapper));
        _mapper = mapper;
    }

    public UserDto MapToModel(User source) => _mapper.MapToUserDto(source);

    public List<UserDto> MapToModel(IEnumerable<User> sources) => sources.Select(u => _mapper.MapToUserDto(u)).ToList();
}