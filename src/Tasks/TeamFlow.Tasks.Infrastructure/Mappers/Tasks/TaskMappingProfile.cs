using Riok.Mapperly.Abstractions;
using TeamFlow.Tasks.Application.Contracts.Contracts.ViewModels;
using TaskEntity = TeamFlow.Tasks.Core.Entities.Task;

namespace TeamFlow.Tasks.Infrastructure.Mappers.Tasks;

[Mapper]
public partial class TaskMappingProfile
{
    [MapperIgnoreSource(nameof(TaskEntity.Id))]
    [MapperIgnoreSource(nameof(TaskEntity.TimeInStatus))]
    [MapperIgnoreSource(nameof(TaskEntity.StatusChangedAt))]
    public partial TaskViewModel MapToViewModel(TaskEntity task);
}