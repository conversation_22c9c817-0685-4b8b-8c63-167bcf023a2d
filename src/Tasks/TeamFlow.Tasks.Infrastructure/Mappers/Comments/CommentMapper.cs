using TeamFlow.Shared.Contracts.Mapping;
using TeamFlow.Tasks.Application.Contracts.Contracts.ViewModels;
using TeamFlow.Tasks.Core.Entities;

namespace TeamFlow.Tasks.Infrastructure.Mappers.Comments;

public class CommentMapper : IMapper<CommentViewModel, Comment>
{
    private readonly CommentMappingProfile _mapper;

    public CommentMapper(CommentMappingProfile mapper)
    {
        ArgumentNullException.ThrowIfNull(mapper, nameof(mapper));
        _mapper = mapper;
    }

    public CommentViewModel MapToModel(Comment source) => _mapper.MapToViewModel(source);

    public List<CommentViewModel> MapToModel(IEnumerable<Comment> sources)
        => sources.Select(u => _mapper.MapToViewModel(u)).ToList();
}