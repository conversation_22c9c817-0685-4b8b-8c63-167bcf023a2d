using FluentResults;
using TeamFlow.Shared.Contracts.Mapping;
using TeamFlow.Tasks.Application.Errors;
using TeamFlow.Tasks.Core.Abstractions.Services.Infrastructure;
using TeamFlow.Tasks.Core.Contracts.External;
using TeamFlow.Tasks.Infrastructure.Mappers;
using TeamFlow.Tasks.Infrastructure.Mappers.Users;
using VanId;

namespace TeamFlow.Tasks.Infrastructure.Grpc;

public class GrpcClientAdapter : IExternalUserService
{
    private readonly GrpcUserService.GrpcUserServiceClient _client;
    private readonly IMapper<UserDto, User> _mapper;

    public GrpcClientAdapter(GrpcUserService.GrpcUserServiceClient client,
        IMapper<UserDto, User> userMappingProfile, IMapper<UserDto, User> mapper)
    {
        ArgumentNullException.ThrowIfNull(client, nameof(client));
        ArgumentNullException.ThrowIfNull(userMappingProfile, nameof(userMappingProfile));
        _client = client;
        _mapper = mapper;
    }

    public async Task<Result<UserDto>> GetByGuidAsync(Guid id, CancellationToken cancellationToken = default)
    {
        var request = new GetUserByGuidRequest
        {
            Uuid = id.ToString()
        };

        var response = await _client.GetUserByGuidAsync(request, cancellationToken: cancellationToken);

        return response is null
            ? Result.Fail(ErrorsFactory.NotFound("User", id))
            : Result.Ok(_mapper.MapToModel(response));
    }

    public async Task<Result<UserDto>> GetByLoginAsync(string login, CancellationToken cancellationToken = default)
    {
        var request = new GetUserByLoginRequest
        {
            Login = login
        };

        var response = await _client.GetUserByLoginAsync(request, cancellationToken: cancellationToken);

        return response is null
            ? Result.Fail(ErrorsFactory.NotFound("User", $"User with login '{login}' not found"))
            : Result.Ok(_mapper.MapToModel(response));
    }

    public async Task<Result<UserDto>> GetByEmailAsync(string email, CancellationToken cancellationToken = default)
    {
        var request = new GetUserByEmailRequest
        {
            Email = email
        };

        var response = await _client.GetUserByEmailAsync(request, cancellationToken: cancellationToken);

        return response is null
            ? Result.Fail(ErrorsFactory.NotFound("User", $"User with email '{email}' not found"))
            : Result.Ok(_mapper.MapToModel(response));
    }

    public async Task<Result<UserDtoPaged>> GetPagedAsync(int pageNumber, int pageSize,
        CancellationToken cancellationToken = default)
    {
        var request = new GetUsersPagedRequest
        {
            PageNumber = pageNumber,
            PageSize = pageSize
        };

        var response = await _client.GetUsersPagedAsync(request, cancellationToken: cancellationToken);

        if (response is null)
        {
            return Result.Fail(ErrorsFactory.NotFound("User", "Users not found"));
        }

        return Result.Ok(new UserDtoPaged
        {
            Users = _mapper.MapToModel(response.Users),
            TotalCount = response.TotalCount,
            TotalPages = response.TotalPages,
            PageSize = pageSize,
            PageNumber = pageNumber
        });
    }
}