<Project Sdk="Microsoft.NET.Sdk">

    <PropertyGroup>
        <TargetFramework>net9.0</TargetFramework>
        <ImplicitUsings>enable</ImplicitUsings>
        <Nullable>enable</Nullable>
    </PropertyGroup>

    <ItemGroup>
      <PackageReference Include="FluentValidation" Version="11.11.0" />
      <PackageReference Include="MediatR" Version="12.5.0" />
      <PackageReference Include="Riok.Mapperly" Version="4.2.1" />
    </ItemGroup>

    <ItemGroup>
      <ProjectReference Include="..\..\..\TeamFlow.Tasks.Application.Contracts\TeamFlow.Tasks.Application.Contracts.csproj" />
    </ItemGroup>

</Project>
