using Microsoft.Extensions.Logging;
using TeamFlow.Tasks.Core.Abstractions.Services;
using TeamFlow.Tasks.Core.Shared.Enums;
using Task = TeamFlow.Tasks.Core.Entities.Task;

namespace TeamFlow.Tasks.Application.Services;

public class TaskStatusTracker : ITaskStatusTracker
{
    private readonly ILogger<TaskStatusTracker> _logger;

    public TaskStatusTracker(ILogger<TaskStatusTracker> logger)
    {
        _logger = logger;
    }

    public void TrackTimeAndStatusChange(Task task, TaskStatuses newStatus)
    {
        _logger.LogDebug("Отслеживание изменения времени и статуса для задачи {TaskId}: {OldStatus} -> {NewStatus}",
            task.Id, task.Status, newStatus);

        TimeSpan timeInCurrentStatus = DateTime.UtcNow - task.StatusChangedAt;
        _logger.LogDebug("Время в текущем статусе {Status}: {TimeInStatus}", task.Status, timeInCurrentStatus);

        if (!task.TimeInStatus.TryAdd(task.Status, timeInCurrentStatus))
        {
            _logger.LogDebug("Обновление существующего времени в статусе {Status} для задачи {TaskId}",
                task.Status, task.Id);
            task.TimeInStatus[task.Status] += timeInCurrentStatus;
        }

        if (task.Status == TaskStatuses.InProgress)
        {
            _logger.LogDebug("Обновление фактического времени выполнения задачи {TaskId}: +{AdditionalTime}",
                task.Id, timeInCurrentStatus);
            task.ActualTime = (task.ActualTime ?? TimeSpan.Zero) + timeInCurrentStatus;
        }

        task.Status = newStatus;
        task.StatusChangedAt = DateTime.UtcNow;
        _logger.LogDebug("Статус задачи {TaskId} изменен на {NewStatus}", task.Id, newStatus);

        if (newStatus == TaskStatuses.Completed)
        {
            _logger.LogDebug("Задача {TaskId} отмечена как завершенная в {CompletedAt}", task.Id, DateTime.UtcNow);
            task.CompletedAt = DateTime.UtcNow;
        }
        else if (task.CompletedAt.HasValue)
        {
            _logger.LogDebug("Сброс времени завершения для задачи {TaskId}", task.Id);
            task.CompletedAt = null;
        }

    }
}