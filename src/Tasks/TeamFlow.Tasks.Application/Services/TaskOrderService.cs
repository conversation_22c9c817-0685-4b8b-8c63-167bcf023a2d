using Microsoft.Extensions.Logging;
using TeamFlow.Shared.Contracts.Sorting;
using TeamFlow.Tasks.Core.Abstractions.Repositories;
using TeamFlow.Tasks.Core.Abstractions.Services;
using TaskEntity = TeamFlow.Tasks.Core.Entities.Task;

namespace TeamFlow.Tasks.Application.Services;

public class TaskOrderService : ITaskOrderService
{
    private readonly IRepository<TaskEntity, Guid> _tasksRepository;
    private readonly ILogger<TaskOrderService> _logger;

    public TaskOrderService(IRepository<TaskEntity, Guid> tasksRepository, ILogger<TaskOrderService> logger)
    {
        _tasksRepository = tasksRepository;
        _logger = logger;
    }
    
    public async Task ShiftTaskOrdersAsync(Guid columnId, int newOrder, int? oldOrder,
        CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation(
                "Сдвиг порядка задач в колонке {ColumnId}, новый порядок: {NewOrder}, старый порядок: {OldOrder}",
                columnId, newOrder, oldOrder);
            var sortExpression = new OrderByExpression<TaskEntity>(t => t.KanbanOrder ?? 0);

            var tasksToShift = (await _tasksRepository.GetManyAsync(
                t => t.KanbanColumnId == columnId && t.KanbanOrder >= newOrder &&
                     (oldOrder == null || t.KanbanOrder != oldOrder),
                sortExpression,
                cancellationToken: cancellationToken)).ToList();

            if (tasksToShift.Count == 0)
            {
                _logger.LogInformation("Нет задач для сдвига в колонке {ColumnId}", columnId);
                return;
            }

            _logger.LogInformation("Сдвиг порядка для {Count} задач в колонке {ColumnId}", tasksToShift.Count, columnId);
            foreach (var task in tasksToShift)
            {
                task.KanbanOrder = (task.KanbanOrder ?? 0) + 1;
            }

            await _tasksRepository.BulkUpdateAsync(tasksToShift, cancellationToken);
        }
        catch (Exception e)
        {
            _logger.LogError(e, "Ошибка при сдвиге задач в колонке {ColumnId}", columnId);
            throw;
        }
    }

    public async Task CompactTaskOrdersAsync(Guid columnId, int removedOrder,
        CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Уплотнение порядка задач в колонке {ColumnId} после удаления порядка {RemovedOrder}",
                columnId, removedOrder);
            var sortExpression = new OrderByExpression<TaskEntity>(t => t.KanbanOrder ?? 0);

            var tasksToShift = (await _tasksRepository.GetManyAsync(
                t => t.KanbanColumnId == columnId && t.KanbanOrder > removedOrder,
                sortExpression,
                cancellationToken: cancellationToken)).ToList();

            if (tasksToShift.Count == 0)
            {
                _logger.LogInformation("Нет задач для уплотнения в колонке {ColumnId}", columnId);
                return;
            }

            _logger.LogInformation("Уплотнение порядка для {Count} задач в колонке {ColumnId}", tasksToShift.Count,
                columnId);
            foreach (var task in tasksToShift)
            {
                task.KanbanOrder = (task.KanbanOrder ?? 0) - 1;
            }

            await _tasksRepository.BulkUpdateAsync(tasksToShift, cancellationToken);
        }
        catch (Exception e)
        {
            _logger.LogError(e, "Ошибка при сдвиге задач в колонке {ColumnId}", columnId);
            throw;
        }
    }
}