using FluentValidation;
using TeamFlow.Tasks.Application.Contracts.Contracts.Features.Projects.Commands;

namespace TeamFlow.Tasks.Application.Validators.Project;

public class UpdateProjectCommandValidator : AbstractValidator<UpdateProjectCommand>
{
    public UpdateProjectCommandValidator()
    {
        RuleFor(x => x.Id).NotNull();
        
        RuleFor(x => x.Name)
            .Length(1, 100)
            .When(x => x.Name != null);
            
        RuleFor(x => x.OwnerId)
            .NotEmpty()
            .When(x => x.OwnerId.HasValue);
            
        RuleFor(x => x.StartDate)
            .LessThan(x => x.EndDate)
            .When(x => x.StartDate.HasValue && x.EndDate.HasValue)
            .WithMessage("Дата начала должна быть раньше даты окончания");
            
        RuleFor(x => x.PriorityLevel)
            .IsInEnum()
            .When(x => x.PriorityLevel.HasValue);
            
        RuleFor(x => x.Status)
            .IsInEnum()
            .When(x => x.Status.HasValue);
    }
}