using FluentValidation;
using TeamFlow.Tasks.Application.Contracts.Contracts.Features.Projects.Commands;

namespace TeamFlow.Tasks.Application.Validators.Project;

public class CreateProjectCommandValidator : AbstractValidator<CreateProjectCommand>
{
    public CreateProjectCommandValidator()
    {
        RuleFor(x => x.Name)
            .NotEmpty()
            .Length(1, 100);
            
        RuleFor(x => x.OwnerId)
            .NotEmpty();
            
        RuleFor(x => x.StartDate)
            .NotEmpty()
            .LessThan(x => x.EndDate)
            .WithMessage("Дата начала должна быть раньше даты окончания");
            
        RuleFor(x => x.EndDate)
            .NotEmpty();
            
        RuleFor(x => x.PriorityLevel)
            .IsInEnum();
            
        RuleFor(x => x.Status)
            .IsInEnum();
    }
}