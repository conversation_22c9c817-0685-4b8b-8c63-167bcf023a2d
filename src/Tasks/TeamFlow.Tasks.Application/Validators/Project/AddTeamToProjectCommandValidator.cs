using FluentValidation;
using TeamFlow.Tasks.Application.Contracts.Contracts.Features.Projects.Commands;

namespace TeamFlow.Tasks.Application.Validators.Project;

public class AddTeamToProjectCommandValidator : AbstractValidator<AddTeamToProjectCommand>
{
    public AddTeamToProjectCommandValidator()
    {
        RuleFor(x => x.ProjectId)
            .NotEmpty().WithMessage("Идентификатор проекта не может быть пустым");
                
        RuleFor(x => x.TeamId)
            .NotEmpty().WithMessage("Идентификатор команды не может быть пустым");
    }
}