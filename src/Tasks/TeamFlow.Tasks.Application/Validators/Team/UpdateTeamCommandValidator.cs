using FluentValidation;
using TeamFlow.Tasks.Application.Contracts.Contracts.Features.Team.Commands;

namespace TeamFlow.Tasks.Application.Validators.Team;

public class UpdateTeamCommandValidator : AbstractValidator<UpdateTeamCommand>
{
    public UpdateTeamCommandValidator()
    {
        RuleFor(x => x.Id).NotNull();
        
        RuleFor(x => x.Name)
            .Length(1, 100)
            .When(x => x.Name != null);
            
        RuleFor(x => x.TeamLeadId)
            .NotEmpty()
            .When(x => x.TeamLeadId.HasValue);
            
        RuleForEach(x => x.MembersIds)
            .NotEmpty()
            .When(x => x.MembersIds is not null && x.MembersIds.Count != 0);
    }
}