using FluentValidation;
using TeamFlow.Tasks.Application.Contracts.Contracts.Features.Team.Commands;

namespace TeamFlow.Tasks.Application.Validators.Team.@new;

public class AddMemberToTeamCommandValidator : AbstractValidator<AddMemberToTeamCommand>
{
    public AddMemberToTeamCommandValidator()
    {
        RuleFor(x => x.TeamId)
            .NotEmpty().WithMessage("Идентификатор команды не может быть пустым");
                
        RuleFor(x => x.MemberId)
            .NotEmpty().WithMessage("Идентификатор участника не может быть пустым");
    }
}