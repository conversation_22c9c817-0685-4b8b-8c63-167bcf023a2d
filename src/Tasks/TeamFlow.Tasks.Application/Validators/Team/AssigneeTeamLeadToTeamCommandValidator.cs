using FluentValidation;
using TeamFlow.Tasks.Application.Contracts.Contracts.Features.Team.Commands;

namespace TeamFlow.Tasks.Application.Validators.Team.NewA;

public class AssigneeTeamLeadToTeamCommandValidator : AbstractValidator<AssigneeTeamLeadToTeamCommand>
{
    public AssigneeTeamLeadToTeamCommandValidator()
    {
        RuleFor(x => x.TeamId)
            .NotEmpty().WithMessage("Идентификатор команды не может быть пустым");
                
        RuleFor(x => x.TeamLeadId)
            .NotEmpty().WithMessage("Идентификатор руководителя команды не может быть пустым");
    }
}