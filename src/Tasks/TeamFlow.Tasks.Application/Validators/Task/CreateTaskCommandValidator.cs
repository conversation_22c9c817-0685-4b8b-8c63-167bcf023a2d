using FluentValidation;
using TeamFlow.Tasks.Application.Contracts.Contracts.Features.Task.Commands;

namespace TeamFlow.Tasks.Application.Validators.Task.NewA;

public class CreateTaskCommandValidator : AbstractValidator<CreateTaskCommand>
{
    public CreateTaskCommandValidator()
    {
        RuleFor(x => x.Title)
            .NotEmpty()
            .Length(1, 200);

        RuleFor(x => x.ProjectId)
            .NotEmpty();

        RuleFor(x => x.ReporterId)
            .NotEmpty();

        RuleFor(x => x.Priority)
            .IsInEnum();

        RuleFor(x => x.Type)
            .IsInEnum();

        RuleFor(x => x.StoryPoint)
            .GreaterThanOrEqualTo(0);

        RuleFor(x => x.DueDate)
            .GreaterThan(DateTime.Now)
            .When(x => x.DueDate.HasValue)
            .WithMessage("Срок выполнения должен быть в будущем");

        RuleForEach(x => x.Tags)
            .NotEmpty()
            .When(x => x.Tags is not null && x.Tags.Count != 0);

        RuleFor(x => x.KanbanColumnId)
            .NotEmpty()
            .When(x => x.KanbanColumnId is not null);
    }
}