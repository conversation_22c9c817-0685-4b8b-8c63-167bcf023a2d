using FluentValidation;
using TeamFlow.Tasks.Application.Contracts.Contracts.Features.Task.Commands;

namespace TeamFlow.Tasks.Application.Validators.Task.NewA;

public class UpdateTaskCommandValidator : AbstractValidator<UpdateTaskCommand>
{
    public UpdateTaskCommandValidator()
    {
        RuleFor(x => x.Title)
            .Length(1, 200)
            .When(x => x.Title != null);
            
        RuleFor(x => x.KanbanColumnId)
            .NotEmpty()
            .When(x => x.KanbanColumnId.HasValue);
            
        RuleFor(x => x.AssigneeId)
            .NotEmpty()
            .When(x => x.AssigneeId.HasValue);
            
        RuleFor(x => x.Type)
            .IsInEnum()
            .When(x => x.Type.HasValue);
            
        RuleFor(x => x.KanbanOrder)
            .GreaterThanOrEqualTo(0)
            .When(x => x.KanbanOrder.HasValue);
            
        RuleFor(x => x.StoryPoints)
            .GreaterThanOrEqualTo(0)
            .When(x => x.StoryPoints.HasValue);
    }
}