using FluentValidation;
using TeamFlow.Tasks.Application.Contracts.Contracts.Features.Task.Commands;

namespace TeamFlow.Tasks.Application.Validators.Task.NewA;

public class UpdateEstimatedTimeCommandValidator : AbstractValidator<UpdateEstimatedTimeCommand>
{
    public UpdateEstimatedTimeCommandValidator()
    {
        RuleFor(x => x.TaskId)
            .NotEmpty().WithMessage("Идентификатор задачи не может быть пустым");
                
        RuleFor(x => x.EstimatedTime)
            .Must(time => !time.HasValue || time.Value.TotalMinutes > 0)
            .WithMessage("Оценка времени должна быть положительным значением");
    }
}