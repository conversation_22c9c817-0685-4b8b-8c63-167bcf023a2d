using FluentValidation;
using TeamFlow.Tasks.Application.Contracts.Contracts.Features.Task.Commands;

namespace TeamFlow.Tasks.Application.Validators.Task.NewA;

public class UpdateTaskPriorityCommandValidator : AbstractValidator<UpdateTaskPriorityCommand>
{
    public UpdateTaskPriorityCommandValidator()
    {
        RuleFor(x => x.TaskId)
            .NotEmpty().WithMessage("Идентификатор задачи не может быть пустым");
                
        RuleFor(x => x.PriorityLevel)
            .IsInEnum().WithMessage("Недопустимый уровень приоритета");
    }
}