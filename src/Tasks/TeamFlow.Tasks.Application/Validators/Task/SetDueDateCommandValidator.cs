using FluentValidation;
using TeamFlow.Tasks.Application.Contracts.Contracts.Features.Task.Commands;

namespace TeamFlow.Tasks.Application.Validators.Task.NewA;

public class SetDueDateCommandValidator : AbstractValidator<SetDueDateCommand>
{
    public SetDueDateCommandValidator()
    {
        RuleFor(x => x.TaskId)
            .NotEmpty().WithMessage("Идентификатор задачи не может быть пустым");
                
        RuleFor(x => x.DueDate)
            .NotEmpty().WithMessage("Срок выполнения не может быть пустым")
            .GreaterThan(DateTime.Now).WithMessage("Срок выполнения должен быть в будущем");
    }
}