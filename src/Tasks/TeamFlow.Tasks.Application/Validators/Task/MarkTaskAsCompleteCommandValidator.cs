using FluentValidation;
using TeamFlow.Tasks.Application.Contracts.Contracts.Features.Task.Commands;

namespace TeamFlow.Tasks.Application.Validators.Task.NewA;

public class MarkTaskAsCompleteCommandValidator : AbstractValidator<MarkTaskAsCompleteCommand>
{
    public MarkTaskAsCompleteCommandValidator()
    {
        RuleFor(x => x.TaskId)
            .NotEmpty().WithMessage("Идентификатор задачи не может быть пустым");
    }
}