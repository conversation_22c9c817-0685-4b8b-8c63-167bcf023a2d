using FluentValidation;
using TeamFlow.Tasks.Application.Contracts.Contracts.Features.Comment.Commands;

namespace TeamFlow.Tasks.Application.Validators.Comment.NewA;

public class CreateCommentCommandValidator : AbstractValidator<CreateCommentCommand>
{
    public CreateCommentCommandValidator()
    {
        RuleFor(x => x.PublisherId)
            .NotEmpty();
            
        RuleFor(x => x.Content)
            .NotEmpty()
            .MaximumLength(1000);
            
        RuleFor(x => x.TaskId)
            .NotEmpty();
            
        RuleForEach(x => x.MentionedUserIds)
            .NotEmpty()
            .When(x => x.MentionedUserIds != null && x.MentionedUserIds.Any());
            
        RuleFor(x => x.ParentCommentId)
            .NotEmpty()
            .When(x => x.ParentCommentId.HasValue);
    }
}