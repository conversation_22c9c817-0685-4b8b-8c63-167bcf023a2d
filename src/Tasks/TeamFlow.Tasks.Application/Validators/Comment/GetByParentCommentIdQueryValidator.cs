using FluentValidation;
using TeamFlow.Tasks.Application.Contracts.Contracts.Features.Comment.Queries;

namespace TeamFlow.Tasks.Application.Validators.Comment.NewA;

public class GetByParentCommentIdQueryValidator : AbstractValidator<GetCommentsByParentCommentIdQuery>
{
    public GetByParentCommentIdQueryValidator()
    {
        RuleFor(x => x.ParentCommentId)
            .NotEmpty().WithMessage("Идентификатор родительского комментария не может быть пустым");
    }
}