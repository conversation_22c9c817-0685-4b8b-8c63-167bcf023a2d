using FluentValidation;
using TeamFlow.Tasks.Application.Contracts.Contracts.Features.Comment.Queries;

namespace TeamFlow.Tasks.Application.Validators.Comment.NewA;

public class GetByPublisherIdPageableQueryValidator : AbstractValidator<GetCommentsByPublisherIdPageableQuery>
{
    public GetByPublisherIdPageableQueryValidator()
    {
        RuleFor(x => x.PublisherId)
            .NotEmpty().WithMessage("Идентификатор публикатора не может быть пустым");
    }
}