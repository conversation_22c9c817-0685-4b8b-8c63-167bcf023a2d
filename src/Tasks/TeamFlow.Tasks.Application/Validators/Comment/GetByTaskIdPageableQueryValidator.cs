using FluentValidation;
using TeamFlow.Tasks.Application.Contracts.Contracts.Features.Comment.Queries;

namespace TeamFlow.Tasks.Application.Validators.Comment.NewA;

public class GetByTaskIdPageableQueryValidator : AbstractValidator<GetCommentsByTaskIdPageableQuery>
{
    public GetByTaskIdPageableQueryValidator()
    {
        RuleFor(x => x.TaskId)
            .NotEmpty().WithMessage("Идентификатор задачи не может быть пустым");
    }
}