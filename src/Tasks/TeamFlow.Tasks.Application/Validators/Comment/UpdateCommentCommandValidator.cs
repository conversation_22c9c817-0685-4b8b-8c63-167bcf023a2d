using FluentValidation;
using TeamFlow.Tasks.Application.Contracts.Contracts.Features.Comment.Commands;

namespace TeamFlow.Tasks.Application.Validators.Comment;

public class UpdateCommentCommandValidator : AbstractValidator<UpdateCommentCommand>
{
    public UpdateCommentCommandValidator()
    {
        RuleFor(x => x.Id).NotEmpty();
        
        RuleFor(x => x.Content)
            .NotEmpty()
            .MaximumLength(1000)
            .When(x => x.Content != null);
            
        RuleForEach(x => x.MentionedUserIds)
            .NotEmpty()
            .When(x => x.MentionedUserIds != null && x.MentionedUserIds.Any());
    }
}