using FluentValidation;
using TeamFlow.Tasks.Application.Contracts.Contracts.Features.Attachment.Commands;

namespace TeamFlow.Tasks.Application.Validators.Attachment;

public class UpdateAttachmentCommandValidator : AbstractValidator<UpdateAttachmentCommand>
{
    public UpdateAttachmentCommandValidator()
    {
        RuleFor(x => x.Id).NotEmpty();
        
        RuleFor(x => x.ContentType)
            .NotEmpty()
            .When(x => x.ContentType != null)
            .Length(1, 100)
            .When(x => x.ContentType != null);
    }
}