using FluentValidation;
using TeamFlow.Tasks.Application.Contracts.Contracts.Features.Attachment.Queries;

namespace TeamFlow.Tasks.Application.Validators.Attachment;

public class GetAttachmentByFileNameQueryValidator : AbstractValidator<GetAttachmentByFileNameQuery>
{
    public GetAttachmentByFileNameQueryValidator()
    {
        RuleFor(x => x.FileName)
            .NotEmpty()
            .WithMessage("Please specify a file name");
    }
}