using FluentResults;
using FluentValidation;
using MediatR;
using Microsoft.Extensions.Logging;
using TeamFlow.Tasks.Application.Contracts.Contracts.Features.Comment.Commands;
using TeamFlow.Tasks.Application.Errors;
using TeamFlow.Tasks.Application.Extensions;
using TeamFlow.Tasks.Core.Abstractions.Repositories;
using TeamFlow.Tasks.Core.Entities;
using TaskEntity = TeamFlow.Tasks.Core.Entities.Task;

namespace TeamFlow.Tasks.Application.Features.Comments.Commands;

public sealed class DeleteCommentCommandHandler : IRequestHandler<DeleteCommentCommand, Result>
{
    private readonly IRepository<Comment, Guid> _commentsRepository;
    private readonly ILogger<DeleteCommentCommandHandler> _logger;
    private readonly IValidator<DeleteCommentCommand> _validator;

    public DeleteCommentCommandHandler(IRepository<Comment, Guid> commentsRepository,
        IRepository<TaskEntity, Guid> tasksRepository, ILogger<DeleteCommentCommandHandler> logger,
        IValidator<DeleteCommentCommand> validator)
    {
        _commentsRepository = commentsRepository;
        _logger = logger;
        _validator = validator;
    }

    public async Task<Result> Handle(DeleteCommentCommand request, CancellationToken cancellationToken)
    {
        _logger.LogInformation("Удаление комментария с ID {CommentId}", request.Id);

        var validationResult = await _validator.ValidateAsync(request, cancellationToken);
        if (!validationResult.IsValid)
        {
            _logger.LogValidationErrors(validationResult.Errors, "удалении комментария");
            return Result.Fail(ErrorsFactory.FromValidationResult(nameof(Comment), validationResult));
        }

        var comment = await _commentsRepository.GetOneAsync(x => x.Id == request.Id, cancellationToken);

        if (comment is null)
        {
            _logger.LogWarning("Комментарий с ID {CommentId} не найден при удалении", request.Id);
            return Result.Fail(ErrorsFactory.NotFound(nameof(Comment), request.Id));
        }

        var childComments = (await _commentsRepository
                .GetManyAsync(x => x.ParentCommentId == request.Id, cancellationToken: cancellationToken))
            .ToList();

        if (childComments.Count == 0)
        {
            _logger.LogInformation("Комментарий с ID {CommentId} не имеет дочерних комментариев, выполняется удаление",
                request.Id);
            await _commentsRepository.RemoveAsync(comment, cancellationToken);
            _logger.LogInformation("Комментарий с ID {CommentId} успешно удален", request.Id);
            return Result.Ok();
        }

        _logger.LogInformation(
            "Комментарий с ID {CommentId} имеет {Count} дочерних комментариев, выполняется перемещение",
            request.Id, childComments.Count);

        foreach (var child in childComments)
        {
            child.ParentCommentId = comment.ParentCommentId;
        }

        await _commentsRepository.BulkUpdateAsync(childComments, cancellationToken);
        _logger.LogInformation("Дочерние комментарии для комментария с ID {CommentId} успешно перемещены", request.Id);

        await _commentsRepository.RemoveAsync(comment, cancellationToken);
        _logger.LogInformation("Комментарий с ID {CommentId} успешно удален", request.Id);
        return Result.Ok();
    }
}