using FluentResults;
using FluentValidation;
using MediatR;
using Microsoft.Extensions.Logging;
using TeamFlow.Shared.Utils;
using TeamFlow.Tasks.Application.Contracts.Contracts.Features.Comment.Commands;
using TeamFlow.Tasks.Application.Errors;
using TeamFlow.Tasks.Core.Abstractions.Repositories;
using TeamFlow.Tasks.Core.Abstractions.Services.Infrastructure;
using TeamFlow.Tasks.Core.Entities;

namespace TeamFlow.Tasks.Application.Features.Comments.Commands;

public sealed class UpdateCommentCommandHandler : IRequestHandler<UpdateCommentCommand, Result<Guid>>
{
    private readonly IRepository<Comment, Guid> _commentsRepository;
    private readonly IExternalUserService _externalUserService;
    private readonly ILogger<UpdateCommentCommandHandler> _logger;
    private readonly IValidator<UpdateCommentCommand> _validator;

    public UpdateCommentCommandHandler(
        IRepository<Comment, Guid> commentsRepository,
        ILogger<UpdateCommentCommandHandler> logger, 
        IValidator<UpdateCommentCommand> validator,
        IExternalUserService externalUserService)
    {
        _commentsRepository = commentsRepository;
        _logger = logger;
        _validator = validator;
        _externalUserService = externalUserService;
    }

    public async Task<Result<Guid>> Handle(UpdateCommentCommand request, CancellationToken cancellationToken)
    {
        _logger.LogInformation("Обновление комментария с ID {CommentId}", request.Id);

        var validationResult = await _validator.ValidateAsync(request, cancellationToken);
        if (!validationResult.IsValid)
        {
            _logger.LogWarning("Ошибка валидации при обновлении комментария: {Errors}",
                string.Join(", ", validationResult.Errors.Select(e => e.ErrorMessage)));
            return Result.Fail(ErrorsFactory.FromValidationResult(nameof(Comment), validationResult));
        }

        var comment = await _commentsRepository
            .GetOneAsync(x => x.Id == request.Id, cancellationToken);

        if (comment is null)
        {
            _logger.LogWarning("Комментарий с ID {CommentId} не найден при обновлении", request.Id);
            return Result.Fail(ErrorsFactory.NotFound(nameof(Comment), request.Id));
        }

        var newContent = ValueComparer.GetNewValueIfUpdated(comment.Content, request.Content);

        comment.Content = newContent ?? comment.Content;

        if (request.MentionedUserIds is not null
            && !comment.MentionedUserIds!.SequenceEqual(request.MentionedUserIds))
        {
            List<Guid> validIds = [];

            foreach (var id in request.MentionedUserIds)
            {
                var userResult = await _externalUserService.GetByGuidAsync(id, cancellationToken);

                if (userResult.IsFailed)
                {
                    continue;
                }

                validIds.Add(id);
            }
            
            comment.MentionedUserIds = validIds;
            _logger.LogInformation("Обновлен список упомянутых пользователей в комментарии {CommentId}", request.Id);
        }

        await _commentsRepository.UpdateAsync(comment, cancellationToken);
        _logger.LogInformation("Комментарий с ID {CommentId} успешно обновлен", request.Id);

        return Result.Ok(comment.Id);
    }
}