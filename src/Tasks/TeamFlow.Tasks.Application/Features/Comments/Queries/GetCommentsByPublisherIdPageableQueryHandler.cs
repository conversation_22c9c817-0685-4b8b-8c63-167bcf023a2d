using FluentResults;
using FluentValidation;
using MediatR;
using Microsoft.Extensions.Logging;
using TeamFlow.Shared.Contracts.Mapping;
using TeamFlow.Shared.Contracts.Pagination;
using TeamFlow.Tasks.Application.Contracts.Contracts.Features.Comment.Queries;
using TeamFlow.Tasks.Application.Contracts.Contracts.ViewModels;
using TeamFlow.Tasks.Application.Errors;
using TeamFlow.Tasks.Application.Extensions;
using TeamFlow.Tasks.Core.Abstractions.Repositories;
using TeamFlow.Tasks.Core.Entities;

namespace TeamFlow.Tasks.Application.Features.Comments.Queries;

public sealed class
    GetCommentsByPublisherIdPageableQueryHandler : IRequestHandler<GetCommentsByPublisherIdPageableQuery,
    Result<Page<CommentViewModel>>>
{
    private readonly IRepository<Comment, Guid> _commentsRepository;
    private readonly IMapper<CommentViewModel, Comment> _mapper;
    private readonly ILogger<GetCommentsByPublisherIdPageableQueryHandler> _logger;
    private readonly IValidator<GetCommentsByPublisherIdPageableQuery> _validator;

    public GetCommentsByPublisherIdPageableQueryHandler(IRepository<Comment, Guid> commentsRepository,
        IMapper<CommentViewModel, Comment> mapper, ILogger<GetCommentsByPublisherIdPageableQueryHandler> logger,
        IValidator<GetCommentsByPublisherIdPageableQuery> validator)
    {
        _commentsRepository = commentsRepository;
        _mapper = mapper;
        _logger = logger;
        _validator = validator;
    }

    public async Task<Result<Page<CommentViewModel>>> Handle(GetCommentsByPublisherIdPageableQuery request,
        CancellationToken cancellationToken)
    {
        _logger.LogInformation(
            "Получение страницы комментариев по ID публикатора {PublisherId}: Страница {PageNumber}, Размер {PageSize}",
            request.PublisherId, request.Pageable.PageNumber, request.Pageable.PageSize);

        var validationResult = await _validator.ValidateAsync(request, cancellationToken);
        if (!validationResult.IsValid)
        {
            _logger.LogValidationErrors(validationResult.Errors, "получении страницы комментариев по ID публикатора");
            return Result.Fail(ErrorsFactory.FromValidationResult(nameof(Comment), validationResult));
        }

        var pagedResult = await _commentsRepository.GetManyAsync(request.Pageable,
            x => x.PublisherId == request.PublisherId,
            cancellationToken: cancellationToken);

        if (pagedResult.Elements is null)
        {
            return Result.Fail(ErrorsFactory.NotFound(nameof(Comment), request.PublisherId));
        }

        var comments = _mapper.MapToModel(pagedResult.Elements);

        if (comments.Count == 0)
        {
            _logger.LogWarning("Комментарии пользователя с ID {PublisherId} не найдены при постраничном запросе",
                request.PublisherId);
            return Result.Fail(ErrorsFactory.NotFound(nameof(Comment),
                $"Комментарии пользователя с ID {request.PublisherId} не найдены"));
        }

        _logger.LogInformation(
            "Успешно получено {Count} комментариев из {Total} для пользователя {PublisherId} при постраничном запросе",
            comments.Count, pagedResult.Count, request.PublisherId);
        return Result.Ok(new Page<CommentViewModel>(comments,
            pagedResult.Count, request.Pageable));
    }
}