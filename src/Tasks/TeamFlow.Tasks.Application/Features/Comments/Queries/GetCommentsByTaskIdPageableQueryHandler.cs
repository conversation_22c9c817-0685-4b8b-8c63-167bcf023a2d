using FluentResults;
using FluentValidation;
using MediatR;
using Microsoft.Extensions.Logging;
using TeamFlow.Shared.Contracts.Mapping;
using TeamFlow.Shared.Contracts.Pagination;
using TeamFlow.Tasks.Application.Contracts.Contracts.Features.Comment.Queries;
using TeamFlow.Tasks.Application.Contracts.Contracts.ViewModels;
using TeamFlow.Tasks.Application.Errors;
using TeamFlow.Tasks.Application.Extensions;
using TeamFlow.Tasks.Core.Abstractions.Repositories;
using TeamFlow.Tasks.Core.Entities;

namespace TeamFlow.Tasks.Application.Features.Comments.Queries;

public sealed class
    GetCommentsByTaskIdPageableQueryHandler : IRequestHandler<GetCommentsByTaskIdPageableQuery,
    Result<Page<CommentViewModel>>>
{
    private readonly IRepository<Comment, Guid> _commentsRepository;
    private readonly IMapper<CommentViewModel, Comment> _mapper;
    private readonly ILogger<GetCommentsByTaskIdPageableQueryHandler> _logger;
    private readonly IValidator<GetCommentsByTaskIdPageableQuery> _validator;

    public GetCommentsByTaskIdPageableQueryHandler(
        IRepository<Comment, Guid> commentsRepository,
        IMapper<CommentViewModel, Comment> mapper,
        ILogger<GetCommentsByTaskIdPageableQueryHandler> logger,
        IValidator<GetCommentsByTaskIdPageableQuery> validator)
    {
        _commentsRepository = commentsRepository;
        _mapper = mapper;
        _logger = logger;
        _validator = validator;
    }

    public async Task<Result<Page<CommentViewModel>>> Handle(GetCommentsByTaskIdPageableQuery request,
        CancellationToken cancellationToken)
    {
        _logger.LogInformation(
            "Получение страницы комментариев по ID задачи {TaskId}: Страница {PageNumber}, Размер {PageSize}",
            request.TaskId, request.Pageable.PageNumber, request.Pageable.PageSize);

        var validationResult = await _validator.ValidateAsync(request, cancellationToken);
        if (!validationResult.IsValid)
        {
            _logger.LogValidationErrors(validationResult.Errors, "получении страницы комментариев по ID задачи");
            return Result.Fail(ErrorsFactory.FromValidationResult(nameof(Comment), validationResult));
        }

        var pagedResult = await _commentsRepository.GetManyAsync(request.Pageable, x => x.TaskId == request.TaskId,
            cancellationToken: cancellationToken);

        if (pagedResult.Elements is null)
        {
            return Result.Fail(ErrorsFactory.NotFound(nameof(Comment), request.TaskId));
        }

        var comments = _mapper.MapToModel(pagedResult.Elements);

        if (comments.Count == 0)
        {
            _logger.LogWarning("Комментарии к задаче с ID {TaskId} не найдены при постраничном запросе",
                request.TaskId);
            return Result.Fail(ErrorsFactory.NotFound(nameof(Comment),
                $"Комментарии к задаче с ID {request.TaskId} не найдены"));
        }

        _logger.LogInformation(
            "Успешно получено {Count} комментариев из {Total} для задачи {TaskId} при постраничном запросе",
            comments.Count, pagedResult.Count, request.TaskId);
        return new Page<CommentViewModel>(comments,
            pagedResult.Count, request.Pageable);
    }
}