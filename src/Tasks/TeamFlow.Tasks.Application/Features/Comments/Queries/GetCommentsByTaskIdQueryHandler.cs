using FluentResults;
using FluentValidation;
using MediatR;
using Microsoft.Extensions.Logging;
using TeamFlow.Shared.Contracts.Mapping;
using TeamFlow.Tasks.Application.Contracts.Contracts.Features.Comment.Queries;
using TeamFlow.Tasks.Application.Contracts.Contracts.ViewModels;
using TeamFlow.Tasks.Application.Errors;
using TeamFlow.Tasks.Application.Extensions;
using TeamFlow.Tasks.Core.Abstractions.Repositories;
using TeamFlow.Tasks.Core.Entities;

namespace TeamFlow.Tasks.Application.Features.Comments.Queries;

public sealed class
    GetCommentsByTaskIdQueryHandler : IRequestHandler<GetCommentsByTaskIdQuery,
    Result<IReadOnlyCollection<CommentViewModel>>>
{
    private readonly IRepository<Comment, Guid> _commentsRepository;
    private readonly IMapper<CommentViewModel, Comment> _mapper;
    private readonly ILogger<GetCommentsByTaskIdQueryHandler> _logger;
    private readonly IValidator<GetCommentsByTaskIdQuery> _validator;

    public GetCommentsByTaskIdQueryHandler(
        IRepository<Comment, Guid> commentsRepository,
        IMapper<CommentViewModel, Comment> mapper, 
        ILogger<GetCommentsByTaskIdQueryHandler> logger,
        IValidator<GetCommentsByTaskIdQuery> validator)
    {
        _commentsRepository = commentsRepository;
        _mapper = mapper;
        _logger = logger;
        _validator = validator;
    }

    public async Task<Result<IReadOnlyCollection<CommentViewModel>>> Handle(GetCommentsByTaskIdQuery request,
        CancellationToken cancellationToken)
    {
        _logger.LogInformation("Получение комментариев по ID задачи {TaskId}", request.TaskId);

        var validationResult = await _validator.ValidateAsync(request, cancellationToken);
        if (!validationResult.IsValid)
        {
            _logger.LogValidationErrors(validationResult.Errors, "получении комментариев по ID задачи");
            return Result.Fail(ErrorsFactory.FromValidationResult(nameof(Comment), validationResult));
        }

        var comments =
            (await _commentsRepository.GetManyAsync(x => x.TaskId == request.TaskId,
                cancellationToken: cancellationToken))
            .ToList();

        if (comments.Count == 0)
        {
            _logger.LogWarning("Комментарии к задаче с ID {TaskId} не найдены", request.TaskId);
            return Result.Fail(ErrorsFactory.NotFound(nameof(Comment),
                $"Комментарии к задаче с ID {request.TaskId} не найдены"));
        }

        _logger.LogInformation("Успешно получено {Count} комментариев для задачи {TaskId}",
            comments.Count, request.TaskId);
        return Result.Ok<IReadOnlyCollection<CommentViewModel>>(_mapper.MapToModel(comments));
    }
}