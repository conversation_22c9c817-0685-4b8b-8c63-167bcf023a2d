using FluentResults;
using FluentValidation;
using MediatR;
using Microsoft.Extensions.Logging;
using TeamFlow.Shared.Contracts.Mapping;
using TeamFlow.Tasks.Application.Contracts.Contracts.Features.Comment.Queries;
using TeamFlow.Tasks.Application.Contracts.Contracts.ViewModels;
using TeamFlow.Tasks.Application.Errors;
using TeamFlow.Tasks.Application.Extensions;
using TeamFlow.Tasks.Core.Abstractions.Repositories;
using TeamFlow.Tasks.Core.Entities;

namespace TeamFlow.Tasks.Application.Features.Comments.Queries;

public class GetCommentsByPublisherIdQueryHandler : IRequestHandler<GetCommentsByPublisherIdQuery,
    Result<IReadOnlyCollection<CommentViewModel>>>
{
    private readonly IRepository<Comment, Guid> _commentsRepository;
    private readonly IMapper<CommentViewModel, Comment> _mapper;
    private readonly ILogger<GetCommentsByPublisherIdQueryHandler> _logger;
    private readonly IValidator<GetCommentsByPublisherIdQuery> _validator;

    public GetCommentsByPublisherIdQueryHandler(
        IRepository<Comment, Guid> commentsRepository,
        IMapper<CommentViewModel, Comment> mapper, 
        ILogger<GetCommentsByPublisherIdQueryHandler> logger,
        IValidator<GetCommentsByPublisherIdQuery> validator)
    {
        _commentsRepository = commentsRepository;
        _mapper = mapper;
        _logger = logger;
        _validator = validator;
    }

    public async Task<Result<IReadOnlyCollection<CommentViewModel>>> Handle(GetCommentsByPublisherIdQuery request,
        CancellationToken cancellationToken)
    {
        _logger.LogInformation("Получение комментариев по ID публикатора {PublisherId}", request.PublisherId);

        var validationResult = await _validator.ValidateAsync(request, cancellationToken);
        if (!validationResult.IsValid)
        {
            _logger.LogValidationErrors(validationResult.Errors, "получении комментариев по ID публикатора");
            return Result.Fail(ErrorsFactory.FromValidationResult(nameof(Comment), validationResult));
        }

        var comments =
            (await _commentsRepository.GetManyAsync(x => x.PublisherId == request.PublisherId,
                cancellationToken: cancellationToken)).ToList();

        if (comments.Count == 0)
        {
            _logger.LogWarning("Комментарии пользователя с ID {PublisherId} не найдены", request.PublisherId);
            return Result.Fail(ErrorsFactory.NotFound(nameof(Comment),
                $"Комментарии пользователя с ID {request.PublisherId} не найдены"));
        }

        _logger.LogInformation("Успешно получено {Count} комментариев для пользователя {PublisherId}",
            comments.Count, request.PublisherId);
        return Result.Ok<IReadOnlyCollection<CommentViewModel>>(_mapper.MapToModel(comments));
    }
}