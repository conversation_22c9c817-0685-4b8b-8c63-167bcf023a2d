using FluentResults;
using FluentValidation;
using MediatR;
using Microsoft.Extensions.Logging;
using TeamFlow.Shared.Contracts.Mapping;
using TeamFlow.Tasks.Application.Contracts.Contracts.Features.Comment.Queries;
using TeamFlow.Tasks.Application.Contracts.Contracts.ViewModels;
using TeamFlow.Tasks.Application.Errors;
using TeamFlow.Tasks.Application.Extensions;
using TeamFlow.Tasks.Core.Abstractions.Repositories;
using TeamFlow.Tasks.Core.Entities;

namespace TeamFlow.Tasks.Application.Features.Comments.Queries;

public sealed class GetCommentsByParentCommentIdQueryHandler : IRequestHandler<GetCommentsByParentCommentIdQuery,
    Result<IReadOnlyCollection<CommentViewModel>>>
{
    private readonly IRepository<Comment, Guid> _commentsRepository;
    private readonly IMapper<CommentViewModel, Comment> _mapper;
    private readonly ILogger<GetCommentsByParentCommentIdQueryHandler> _logger;
    private readonly IValidator<GetCommentsByParentCommentIdQuery> _validator;

    public GetCommentsByParentCommentIdQueryHandler(
        IRepository<Comment, Guid> commentsRepository,
        ILogger<GetCommentsByParentCommentIdQueryHandler> logger,
        IValidator<GetCommentsByParentCommentIdQuery> validator,
        IMapper<CommentViewModel, Comment> mapper)
    {
        _commentsRepository = commentsRepository;
        _logger = logger;
        _validator = validator;
        _mapper = mapper;
    }

    public async Task<Result<IReadOnlyCollection<CommentViewModel>>> Handle(GetCommentsByParentCommentIdQuery request,
        CancellationToken cancellationToken)
    {
        _logger.LogInformation("Получение комментариев по ID родительского комментария {ParentCommentId}",
            request.ParentCommentId);

        var validationResult = await _validator.ValidateAsync(request, cancellationToken);
        if (!validationResult.IsValid)
        {
            _logger.LogValidationErrors(validationResult.Errors, "получении комментариев по ID родительского комментария");
            return Result.Fail(ErrorsFactory.FromValidationResult(nameof(Comment), validationResult));
        }

        var comments = (await _commentsRepository
            .GetManyAsync(x => x.ParentCommentId == request.ParentCommentId,
                cancellationToken: cancellationToken)).ToList();

        if (comments.Count == 0)
        {
            _logger.LogWarning("Комментарии с корневым комментарием с ID {ParentCommentId} не найдены",
                request.ParentCommentId);
            return Result.Fail(ErrorsFactory.NotFound(nameof(Comment),
                $"Комментарии с корневым комментарием с ID {request.ParentCommentId} не найдены"));
        }

        _logger.LogInformation("Успешно получено {Count} комментариев для родительского комментария {ParentCommentId}",
            comments.Count, request.ParentCommentId);
        return Result.Ok<IReadOnlyCollection<CommentViewModel>>(_mapper.MapToModel(comments));
    }
}