using System.Net;
using FluentResults;
using FluentValidation;
using MediatR;
using Microsoft.Extensions.Logging;
using TeamFlow.Shared.Contracts.Mapping;
using TeamFlow.Shared.Contracts.Pagination;
using TeamFlow.Shared.Contracts.Sorting;
using TeamFlow.Tasks.Application.Contracts.Contracts.Features.Comment.Queries;
using TeamFlow.Tasks.Application.Contracts.Contracts.ViewModels;
using TeamFlow.Tasks.Application.Errors;
using TeamFlow.Tasks.Core.Abstractions.Repositories;
using TeamFlow.Tasks.Core.Entities;

namespace TeamFlow.Tasks.Application.Features.Comments.Queries;

public sealed class GetCommentsQueryHandler : IRequestHandler<GetCommentsQuery, Result<Page<CommentViewModel>>>
{
    private readonly IRepository<Comment, Guid> _commentsRepository;
    private readonly IMapper<CommentViewModel, Comment> _mapper;
    private readonly ILogger<GetCommentsQueryHandler> _logger;

    public GetCommentsQueryHandler(
        IRepository<Comment, Guid> commentsRepository,
        IMapper<CommentViewModel, Comment> mapper, 
        ILogger<GetCommentsQueryHandler> logger)
    {
        _commentsRepository = commentsRepository;
        _mapper = mapper;
        _logger = logger;
    }

    public async Task<Result<Page<CommentViewModel>>> Handle(GetCommentsQuery request,
        CancellationToken cancellationToken)
    {
        _logger.LogInformation("Получение страницы комментариев: Страница {PageNumber}, Размер {PageSize}",
            request.Pageable.PageNumber, request.Pageable.PageSize);

        try
        {
            var filter = request.Filter?.ToPredicate();

            var sort = request.Sorting is not null
                ? request.Sorting.ToOrderByExpression()
                : new OrderByExpression<Comment>(t => t.CreatedAt);

            var pagedResult = await _commentsRepository.GetManyAsync(request.Pageable, filter, sort, cancellationToken);

            if (pagedResult.Elements == null)
            {
                _logger.LogError("Репозиторий вернул null для Elements");
                return Result.Fail(ErrorsFactory.Custom("Ошибка при получении данных",
                    HttpStatusCode.InternalServerError));
            }

            var comments = _mapper.MapToModel(pagedResult.Elements);

            if (comments.Count == 0)
            {
                _logger.LogWarning("Комментарии не найдены на странице {PageNumber}", request.Pageable.PageNumber);
                return Result.Fail(ErrorsFactory.NotFound(nameof(Comment), "Комментарии не найдены"));
            }

            _logger.LogInformation("Успешно получено {Count} комментариев на странице {PageNumber}",
                comments.Count, request.Pageable.PageNumber);

            return Result.Ok(new Page<CommentViewModel>(comments, pagedResult.Count, request.Pageable));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Ошибка при получении страницы задач");
            throw;
        }
    }
}