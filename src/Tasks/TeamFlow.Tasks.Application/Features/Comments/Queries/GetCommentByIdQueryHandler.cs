using FluentResults;
using FluentValidation;
using MediatR;
using Microsoft.Extensions.Logging;
using TeamFlow.Shared.Contracts.Mapping;
using TeamFlow.Tasks.Application.Contracts.Contracts.Features.Comment.Queries;
using TeamFlow.Tasks.Application.Contracts.Contracts.ViewModels;
using TeamFlow.Tasks.Application.Errors;
using TeamFlow.Tasks.Application.Extensions;
using TeamFlow.Tasks.Core.Abstractions.Repositories;
using TeamFlow.Tasks.Core.Entities;

namespace TeamFlow.Tasks.Application.Features.Comments.Queries;

public sealed class GetCommentByIdQueryHandler : IRequestHandler<GetCommentByIdQuery, Result<CommentViewModel>>
{
    private readonly IRepository<Comment, Guid> _commentsRepository;
    private readonly IMapper<CommentViewModel, Comment> _mapper;
    private readonly ILogger<GetCommentByIdQueryHandler> _logger;
    private readonly IValidator<GetCommentByIdQuery> _validator;

    public GetCommentByIdQueryHandler(
        IRepository<Comment, Guid> commentsRepository,
        IMapper<CommentViewModel, Comment> mapper, 
        ILogger<GetCommentByIdQueryHandler> logger,
        IValidator<GetCommentByIdQuery> validator)
    {
        _commentsRepository = commentsRepository;
        _mapper = mapper;
        _logger = logger;
        _validator = validator;
    }

    public async Task<Result<CommentViewModel>> Handle(GetCommentByIdQuery request, CancellationToken cancellationToken)
    {
        _logger.LogInformation("Получение комментария по ID: {CommentId}", request.Id);

        var validationResult = await _validator.ValidateAsync(request, cancellationToken);
        if (!validationResult.IsValid)
        {
            _logger.LogValidationErrors(validationResult.Errors, "получении комментария по ID");
            return Result.Fail(ErrorsFactory.FromValidationResult(nameof(Comment), validationResult));
        }

        var comment = await _commentsRepository.GetOneAsync(x => x.Id == request.Id, cancellationToken);

        if (comment is null)
        {
            _logger.LogWarning("Комментарий с ID {CommentId} не найден", request.Id);
            return Result.Fail(ErrorsFactory.NotFound(nameof(Comment), request.Id));
        }

        _logger.LogInformation("Комментарий с ID {CommentId} успешно получен", request.Id);
        return Result.Ok(_mapper.MapToModel(comment));
    }
}