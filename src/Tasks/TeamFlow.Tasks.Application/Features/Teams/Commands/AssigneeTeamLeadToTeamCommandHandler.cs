using FluentResults;
using FluentValidation;
using MediatR;
using Microsoft.Extensions.Logging;
using TeamFlow.Shared.Utils;
using TeamFlow.Tasks.Application.Contracts.Contracts.Features.Team.Commands;
using TeamFlow.Tasks.Application.Errors;
using TeamFlow.Tasks.Core.Abstractions.Repositories;
using TeamFlow.Tasks.Core.Abstractions.Services.Infrastructure;
using TeamFlow.Tasks.Core.Entities;

namespace TeamFlow.Tasks.Application.Features.Teams.Commands;

public sealed class AssigneeTeamLeadToTeamCommandHandler : IRequestHandler<AssigneeTeamLeadToTeamCommand, Result> 
{
    private readonly IRepository<Team, Guid> _teamsRepository;
    private readonly IExternalUserService _externalUserService;
    private readonly ILogger<AssigneeTeamLeadToTeamCommandHandler> _logger;
    private readonly IValidator<AssigneeTeamLeadToTeamCommand> _validator;

    public AssigneeTeamLeadToTeamCommandHandler(IRepository<Team, Guid> teamsRepository, IExternalUserService externalUserService, ILogger<AssigneeTeamLeadToTeamCommandHandler> logger, IValidator<AssigneeTeamLeadToTeamCommand> validator)
    {
        _teamsRepository = teamsRepository;
        _externalUserService = externalUserService;
        _logger = logger;
        _validator = validator;
    }

    public async Task<Result> Handle(AssigneeTeamLeadToTeamCommand request, CancellationToken cancellationToken)
    {
         _logger.LogInformation("Назначение руководителя {TeamLeadId} команде {TeamId}", request.TeamLeadId, request.TeamId);
        
        // Валидация запроса
        var validationResult = await _validator.ValidateAsync(request, cancellationToken);
        if (!validationResult.IsValid)
        {
            _logger.LogWarning("Ошибка валидации при назначении руководителя команде: {Errors}", 
                string.Join(", ", validationResult.Errors.Select(e => e.ErrorMessage)));
            return Result.Fail(ErrorsFactory.FromValidationResult("Team", validationResult));
        }
        
        var teamLeadTask = _externalUserService.GetByGuidAsync(request.TeamLeadId, cancellationToken);
        var teamTask = _teamsRepository.GetOneAsync(x => x.Id == request.TeamId, cancellationToken);
        await System.Threading.Tasks.Task.WhenAll(teamLeadTask, teamTask);

        var (team, teamLead) = (teamTask.Result, teamLeadTask.Result);

        if (teamLead.IsFailed)
        {
            _logger.LogWarning("Пользователь с ID {TeamLeadId} не найден", request.TeamLeadId);
            return Result.Fail(ErrorsFactory.NotFound("User", $"Team lead with id '{request.TeamLeadId}' not found"));
        }

        if (team is null)
        {
            _logger.LogWarning("Команда с ID {TeamId} не найдена", request.TeamId);
            return Result.Fail(ErrorsFactory.NotFound(nameof(Team), $"Team with id '{request.TeamId}' not found"));
        }

        var newTeamLead = ValueComparer.GetNewValueIfUpdated(team.TeamLeadId, request.TeamId);

        team.TeamLeadId = newTeamLead ?? team.TeamLeadId;

        await _teamsRepository.UpdateAsync(team, cancellationToken);
        _logger.LogInformation("Руководитель {TeamLeadId} успешно назначен команде {TeamId}", request.TeamLeadId, request.TeamId);
        return Result.Ok();
    }
}