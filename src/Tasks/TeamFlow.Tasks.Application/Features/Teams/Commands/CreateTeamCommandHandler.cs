using FluentResults;
using FluentValidation;
using MediatR;
using Microsoft.Extensions.Logging;
using TeamFlow.Tasks.Application.Contracts.Contracts.Features.Team.Commands;
using TeamFlow.Tasks.Application.Errors;
using TeamFlow.Tasks.Core.Abstractions.Repositories;
using TeamFlow.Tasks.Core.Abstractions.Services.Infrastructure;
using TeamFlow.Tasks.Core.Entities;

namespace TeamFlow.Tasks.Application.Features.Teams.Commands;

public sealed class CreateTeamCommandHandler : IRequestHandler<CreateTeamCommand, Result<Guid>>
{
    private readonly IRepository<Team, Guid> _teamsRepository;
    private readonly ILogger<CreateTeamCommandHandler> _logger;
    private readonly IValidator<CreateTeamCommand> _validator;

    public CreateTeamCommandHandler(IRepository<Team, Guid> teamsRepository,
        ILogger<CreateTeamCommandHandler> logger, IValidator<CreateTeamCommand> validator)
    {
        _teamsRepository = teamsRepository;
        _logger = logger;
        _validator = validator;
    }

    public async Task<Result<Guid>> Handle(CreateTeamCommand request, CancellationToken cancellationToken)
    {
        _logger.LogInformation("Создание новой команды с именем: {TeamName}", request.Name);

        var validationResult = await _validator.ValidateAsync(request, cancellationToken);
        if (!validationResult.IsValid)
        {
            _logger.LogWarning("Ошибка валидации при создании команды: {Errors}",
                string.Join(", ", validationResult.Errors.Select(e => e.ErrorMessage)));
            return Result.Fail(ErrorsFactory.FromValidationResult("Team", validationResult));
        }

        var isTeamExists = await _teamsRepository.FindAnyAsync(x => x.Name == request.Name, cancellationToken);

        if (isTeamExists)
        {
            _logger.LogWarning("Команда с именем {TeamName} уже существует", request.Name);
            return Result.Fail(ErrorsFactory.AlreadyExists("Team", "Name", request.Name));
        }

        var team = new Team
        {
            Id = Guid.CreateVersion7(),
            Name = request.Name,
            TeamLeadId = request.TeamLeadId,
            MembersIds = request.MembersIds
        };

        await _teamsRepository.AddAsync(team, cancellationToken);
        _logger.LogInformation("Команда успешно создана с ID: {TeamId}", team.Id);

        return Result.Ok(team.Id);
    }
}