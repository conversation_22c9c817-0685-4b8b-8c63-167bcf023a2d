using FluentResults;
using FluentValidation;
using MediatR;
using Microsoft.Extensions.Logging;
using TeamFlow.Tasks.Application.Contracts.Contracts.Features.Team.Commands;
using TeamFlow.Tasks.Application.Errors;
using TeamFlow.Tasks.Core.Abstractions.Repositories;
using TeamFlow.Tasks.Core.Entities;

namespace TeamFlow.Tasks.Application.Features.Teams.Commands;

public sealed class DeleteTeamCommandHandler : IRequestHandler<DeleteTeamCommand, Result>
{
    private readonly IRepository<Team, Guid> _teamsRepository;
    private readonly ILogger<DeleteTeamCommandHandler> _logger;
    private readonly IValidator<DeleteTeamCommand> _validator;

    public DeleteTeamCommandHandler(IRepository<Team, Guid> teamsRepository, ILogger<DeleteTeamCommandHandler> logger,
        IValidator<DeleteTeamCommand> validator)
    {
        _teamsRepository = teamsRepository;
        _logger = logger;
        _validator = validator;
    }


    public async Task<Result> Handle(DeleteTeamCommand request, CancellationToken cancellationToken)
    {
        _logger.LogInformation("Удаление команды с ID: {TeamId}", request.Id);

        var validationResult = await _validator.ValidateAsync(request, cancellationToken);
        if (!validationResult.IsValid)
        {
            _logger.LogWarning("Ошибка валидации при удалении команды: {Errors}",
                string.Join(", ", validationResult.Errors.Select(e => e.ErrorMessage)));
            return Result.Fail(ErrorsFactory.FromValidationResult("Team", validationResult));
        }

        var team = await _teamsRepository.GetOneAsync(x => x.Id == request.Id, cancellationToken);

        if (team is null)
        {
            _logger.LogWarning("Команда с ID {TeamId} не найдена", request.Id);
            return Result.Fail(ErrorsFactory.NotFound(nameof(Team), request.Id));
        }

        await _teamsRepository.RemoveAsync(team, cancellationToken);
        _logger.LogInformation("Команда с ID {TeamId} успешно удалена", request.Id);
        return Result.Ok();
    }
}