using System.Linq.Expressions;
using FluentResults;
using FluentValidation;
using MediatR;
using Microsoft.Extensions.Logging;
using TeamFlow.Shared.Utils;
using TeamFlow.Tasks.Application.Contracts.Contracts.Features.Team.Commands;
using TeamFlow.Tasks.Application.Errors;
using TeamFlow.Tasks.Core.Abstractions.Repositories;
using TeamFlow.Tasks.Core.Abstractions.Services.Infrastructure;
using TeamFlow.Tasks.Core.Entities;

namespace TeamFlow.Tasks.Application.Features.Teams.Commands;

public sealed class UpdateTeamCommandHandler : IRequestHandler<UpdateTeamCommand, Result<Guid>>
{
    private readonly IRepository<Team, Guid> _teamsRepository;
    private readonly IExternalUserService _externalUserService;
    private readonly ILogger<UpdateTeamCommandHandler> _logger;
    private readonly IValidator<UpdateTeamCommand> _validator;

    public UpdateTeamCommandHandler(IRepository<Team, Guid> teamsRepository, IExternalUserService externalUserService,
        ILogger<UpdateTeamCommandHandler> logger, IValidator<UpdateTeamCommand> validator)
    {
        _teamsRepository = teamsRepository;
        _externalUserService = externalUserService;
        _logger = logger;
        _validator = validator;
    }

    public async Task<Result<Guid>> Handle(UpdateTeamCommand request, CancellationToken cancellationToken)
    {
        _logger.LogInformation("Обновление команды с ID: {TeamId}", request.Id);

        // Валидация запроса
        var validationResult = await _validator.ValidateAsync(request, cancellationToken);
        if (!validationResult.IsValid)
        {
            _logger.LogWarning("Ошибка валидации при обновлении команды: {Errors}",
                string.Join(", ", validationResult.Errors.Select(e => e.ErrorMessage)));
            return Result.Fail(ErrorsFactory.FromValidationResult("Team", validationResult));
        }

        var team = await _teamsRepository.GetOneAsync(x => x.Id == request.Id, cancellationToken);

        if (team is null)
        {
            _logger.LogWarning("Команда с ID {TeamId} не найдена", request.Id);
            return Result.Fail(ErrorsFactory.NotFound(nameof(Team), request.Id));
        }

        var newName = ValueComparer.GetNewValueIfUpdated(team.Name, request.Name);
        var newTeamLeaderId = ValueComparer.GetNewValueIfUpdated(request.TeamLeadId, team.TeamLeadId);

        if (newTeamLeaderId is not null)
        {
            var teamLead = await _externalUserService.GetByGuidAsync(newTeamLeaderId.Value, cancellationToken);

            if (teamLead.IsFailed)
            {
                return Result.Fail(ErrorsFactory.NotFound("User", newTeamLeaderId.Value));
            }
        }

        bool isNameUpdated = newName is not null;

        if (isNameUpdated)
        {
            _logger.LogInformation("Обновление имени команды с '{OldName}' на '{NewName}'", team.Name,
                request.Name);
            Expression<Func<Team, bool>> filter = t =>
                isNameUpdated && t.Name == request.Name
                              && t.Id != request.Id;

            bool isConflict = await _teamsRepository.FindAnyAsync(filter, cancellationToken);

            if (isConflict)
            {
                _logger.LogWarning("Конфликт имен: команда с именем '{TeamName}' уже существует", request.Name);
                return Result.Fail(ErrorsFactory.AlreadyExists(nameof(Team), "Name", request.Name!));
            }
        }

        team.Name = newName ?? team.Name;
        team.TeamLeadId = newTeamLeaderId ?? team.TeamLeadId;

        //TODO Not optimal, should be changed in future.
        if (request.MembersIds is not null
            && !team.MembersIds!.SequenceEqual(request.MembersIds))
        {
            _logger.LogInformation("Обновление списка участников команды с ID: {TeamId}", request.Id);

            List<Guid> validIds = [];

            foreach (var id in request.MembersIds)
            {
                var userResult = await _externalUserService.GetByGuidAsync(id, cancellationToken);

                if (userResult.IsFailed)
                {
                    continue;
                }

                validIds.Add(id);
            }

            //TODO Can be changed, if we want to add members, but don't replace them
            team.MembersIds = validIds;
        }

        await _teamsRepository.UpdateAsync(team, cancellationToken);
        _logger.LogInformation("Команда с ID {TeamId} успешно обновлена", team.Id);
        return Result.Ok(team.Id);
    }
}