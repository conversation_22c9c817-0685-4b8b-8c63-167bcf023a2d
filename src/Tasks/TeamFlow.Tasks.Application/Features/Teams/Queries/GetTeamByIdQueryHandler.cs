using FluentResults;
using FluentValidation;
using MediatR;
using Microsoft.Extensions.Logging;
using TeamFlow.Shared.Contracts.Mapping;
using TeamFlow.Tasks.Application.Contracts.Contracts.Features.Team.Queries;
using TeamFlow.Tasks.Application.Contracts.Contracts.ViewModels;
using TeamFlow.Tasks.Application.Errors;
using TeamFlow.Tasks.Core.Abstractions.Repositories;
using TeamFlow.Tasks.Core.Entities;

namespace TeamFlow.Tasks.Application.Features.Teams.Queries;

public sealed class GetTeamByIdQueryHandler : IRequestHandler<GetTeamByIdQuery, Result<TeamViewModel>>
{
    private readonly IRepository<Team, Guid> _teamsRepository;
    private readonly ILogger<GetTeamByIdQueryHandler> _logger;
    private readonly IValidator<GetTeamByIdQuery> _validator;
    private readonly IMapper<TeamViewModel, Team> _mapper;

    public GetTeamByIdQueryHandler(IRepository<Team, Guid> teamsRepository, ILogger<GetTeamByIdQueryHandler> logger, IValidator<GetTeamByIdQuery> validator, IMapper<TeamViewModel, Team> mapper)
    {
        _teamsRepository = teamsRepository;
        _logger = logger;
        _validator = validator;
        _mapper = mapper;
    }

    public async Task<Result<TeamViewModel>> Handle(GetTeamByIdQuery request, CancellationToken cancellationToken)
    {
        _logger.LogInformation("Получение команды по ID: {TeamId}", request.Id);

        var validationResult = await _validator.ValidateAsync(request, cancellationToken);
        if (!validationResult.IsValid)
        {
            _logger.LogWarning("Ошибка валидации при получении команды: {Errors}",
                string.Join(", ", validationResult.Errors.Select(e => e.ErrorMessage)));
            return Result.Fail(ErrorsFactory.FromValidationResult("Team", validationResult));
        }

        var team = await _teamsRepository.GetOneAsync(x => x.Id == request.Id, cancellationToken);

        if (team is null)
        {
            _logger.LogWarning("Команда с ID {TeamId} не найдена", request.Id);
            return Result.Fail(ErrorsFactory.NotFound("Team", request.Id));
        }

        _logger.LogInformation("Команда с ID {TeamId} успешно получена", request.Id);
        return Result.Ok(_mapper.MapToModel(team));
    }
}