using FluentResults;
using FluentValidation;
using MediatR;
using Microsoft.Extensions.Logging;
using TeamFlow.Shared.Utils;
using TeamFlow.Tasks.Application.Contracts.Contracts.Features.Attachment.Commands;
using TeamFlow.Tasks.Application.Errors;
using TeamFlow.Tasks.Core.Abstractions.Repositories;
using TeamFlow.Tasks.Core.Entities;

namespace TeamFlow.Tasks.Application.Features.Attachments.Commands;

public sealed class UpdateAttachmentCommandHandler : IRequestHandler<UpdateAttachmentCommand, Result<Guid>>
{
    private readonly IRepository<Attachment, Guid> _attachmentRepository;
    private readonly ILogger<UpdateAttachmentCommandHandler> _logger;
    private readonly IValidator<UpdateAttachmentCommand> _validator;

    public UpdateAttachmentCommandHandler(IRepository<Attachment, Guid> attachmentRepository, ILogger<UpdateAttachmentCommandHandler> logger, IValidator<UpdateAttachmentCommand> validator)
    {
        _attachmentRepository = attachmentRepository;
        _logger = logger;
        _validator = validator;
    }

    public async Task<Result<Guid>> Handle(UpdateAttachmentCommand request, CancellationToken cancellationToken)
    {
        _logger.LogInformation("Обновление вложения с ID {AttachmentId}", request.Id);

        var validationResult = await _validator.ValidateAsync(request, cancellationToken);
        if (!validationResult.IsValid)
        {
            _logger.LogWarning("Ошибка валидации при обновлении вложения: {Errors}",
                string.Join(", ", validationResult.Errors.Select(e => e.ErrorMessage)));
            return Result.Fail(ErrorsFactory.FromValidationResult("Attachment", validationResult));
        }

        var attachment = await _attachmentRepository.GetOneAsync(a => a.Id == request.Id, cancellationToken);

        if (attachment is null)
        {
            _logger.LogWarning("Вложение с ID {AttachmentId} не найдено при попытке обновления", request.Id);
            return Result.Fail(ErrorsFactory.NotFound(nameof(Attachment), request.Id));
        }

        var newContentType = ValueComparer.GetNewValueIfUpdated(attachment.ContentType, request.ContentType);

        attachment.ContentType = newContentType ?? attachment.ContentType;

        await _attachmentRepository.UpdateAsync(attachment, cancellationToken);
        _logger.LogInformation("Вложение с ID {AttachmentId} успешно обновлено", request.Id);
        return Result.Ok(request.Id);
    }
}