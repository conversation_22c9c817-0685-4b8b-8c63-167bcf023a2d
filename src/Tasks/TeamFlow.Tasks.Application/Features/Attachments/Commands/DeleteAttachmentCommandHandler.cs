using System.Net;
using FluentResults;
using FluentValidation;
using MediatR;
using Microsoft.Extensions.Logging;
using TeamFlow.Tasks.Application.Contracts.Contracts.Features.Attachment.Commands;
using TeamFlow.Tasks.Application.Errors;
using TeamFlow.Tasks.Core.Abstractions.Repositories;
using TeamFlow.Tasks.Core.Abstractions.Services.Infrastructure;
using TeamFlow.Tasks.Core.Entities;

namespace TeamFlow.Tasks.Application.Features.Attachments.Commands;

public sealed class DeleteAttachmentCommandHandler : IRequestHandler<DeleteAttachmentCommand, Result>
{
    private readonly IRepository<Attachment, Guid> _attachmentRepository;
    private readonly IFileService _fileService;
    private readonly ILogger<DeleteAttachmentCommandHandler> _logger;
    private readonly IValidator<DeleteAttachmentCommand> _validator;

    public DeleteAttachmentCommandHandler(IRepository<Attachment, Guid> attachmentRepository, IFileService fileService,
        ILogger<DeleteAttachmentCommandHandler> logger, IValidator<DeleteAttachmentCommand> validator)
    {
        _attachmentRepository = attachmentRepository;
        _fileService = fileService;
        _logger = logger;
        _validator = validator;
    }

    public async Task<Result> Handle(DeleteAttachmentCommand request, CancellationToken cancellationToken)
    {
        _logger.LogInformation("Удаление вложения с ID {AttachmentId}", request.Id);
        
        var validationResult = await _validator.ValidateAsync(request, cancellationToken);
        if (!validationResult.IsValid)
        {
            _logger.LogWarning("Ошибка валидации при удалении вложения: {Errors}",
                string.Join(", ", validationResult.Errors.Select(e => e.ErrorMessage)));
            return Result.Fail(ErrorsFactory.FromValidationResult(nameof(Attachment), validationResult));
        }

        var attachment = await _attachmentRepository.GetOneAsync(a => a.Id == request.Id, cancellationToken);

        if (attachment is null)
        {
            _logger.LogWarning("Вложение с ID {AttachmentId} не найдено при попытке удаления", request.Id);
            return Result.Fail(ErrorsFactory.NotFound(nameof(Attachment), request.Id));
        }

        var hasDeleted = await _fileService.DeleteFileAsync(attachment.FileName, cancellationToken);

        if (!hasDeleted)
        {
            _logger.LogError("Не удалось удалить файл вложения {FileName}", attachment.FileName);
            return Result.Fail(ErrorsFactory.Custom("Не удалось удалить файл вложения",
                HttpStatusCode.InternalServerError));
        }

        await _attachmentRepository.RemoveAsync(attachment, cancellationToken);
        _logger.LogInformation("Вложение с ID {AttachmentId} успешно удалено", request.Id);
        return Result.Ok();
    }
}