using FluentResults;
using MediatR;
using Microsoft.Extensions.Logging;
using TeamFlow.Shared.Contracts.Mapping;
using TeamFlow.Shared.Contracts.Pagination;
using TeamFlow.Shared.Contracts.Sorting;
using TeamFlow.Tasks.Application.Contracts.Contracts.Features.Attachment.Queries;
using TeamFlow.Tasks.Application.Contracts.Contracts.ViewModels;
using TeamFlow.Tasks.Application.Errors;
using TeamFlow.Tasks.Core.Abstractions.Repositories;
using TeamFlow.Tasks.Core.Entities;

namespace TeamFlow.Tasks.Application.Features.Attachments.Queries;

public sealed class GetAttachmentsQueryHandler : IRequestHandler<GetAttachmentsQuery, Result<Page<AttachmentViewModel>>>
{
    private readonly IRepository<Attachment, Guid> _attachmentRepository;
    private readonly ILogger<GetAttachmentByFileNameQueryHandler> _logger;
    private readonly IMapper<AttachmentViewModel, Attachment> _mapper;

    public GetAttachmentsQueryHandler(IRepository<Attachment, Guid> attachmentRepository,
        ILogger<GetAttachmentByFileNameQueryHandler> logger, IMapper<AttachmentViewModel, Attachment> mapper)
    {
        _attachmentRepository = attachmentRepository;
        _logger = logger;
        _mapper = mapper;
    }

    public async Task<Result<Page<AttachmentViewModel>>> Handle(GetAttachmentsQuery request,
        CancellationToken cancellationToken)
    {
        _logger.LogInformation("Получение страницы вложений: Страница {PageNumber}, Размер {PageSize}",
            request.Pageable.PageNumber, request.Pageable.PageSize);

        try
        {
            var filter = request.Filter?.ToPredicate();

            var sort = request.Sorting is not null
                ? request.Sorting.ToOrderByExpression()
                : new OrderByExpression<Attachment>(t => t.CreatedAt);

            var pagedResult = await _attachmentRepository.GetManyAsync(request.Pageable, filter, sort, cancellationToken);

            if (pagedResult.Elements is null)
            {
                _logger.LogError("Репозиторий вернул null для Elements");
                return Result.Fail(ErrorsFactory.InternalServerError("Ошибка при получении данных"));
            }

            var attachments = _mapper.MapToModel(pagedResult.Elements);

            if (attachments.Count == 0)
            {
                _logger.LogWarning("Вложения не найдены на странице {PageNumber}", request.Pageable.PageNumber);
                return Result.Fail(ErrorsFactory.NotFound(nameof(Attachment), "Вложения не найдены"));
            }

            _logger.LogInformation("Успешно получено {Count} вложений на странице {PageNumber}",
                attachments.Count, request.Pageable.PageNumber);

            return Result.Ok(new Page<AttachmentViewModel>(attachments, pagedResult.Count, request.Pageable));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Ошибка при получении страницы вложений");
            throw;
        }
    }
}