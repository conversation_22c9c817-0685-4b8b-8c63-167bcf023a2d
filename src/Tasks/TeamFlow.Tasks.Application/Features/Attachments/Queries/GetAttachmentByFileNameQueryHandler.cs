using FluentResults;
using FluentValidation;
using MediatR;
using Microsoft.Extensions.Logging;
using TeamFlow.Shared.Contracts.Mapping;
using TeamFlow.Tasks.Application.Contracts.Contracts.Features.Attachment.Queries;
using TeamFlow.Tasks.Application.Contracts.Contracts.ViewModels;
using TeamFlow.Tasks.Application.Errors;
using TeamFlow.Tasks.Core.Abstractions.Repositories;
using TeamFlow.Tasks.Core.Entities;

namespace TeamFlow.Tasks.Application.Features.Attachments.Queries;

public sealed class
    GetAttachmentByFileNameQueryHandler : IRequestHandler<GetAttachmentByFileNameQuery, Result<AttachmentViewModel>>
{
    private readonly IRepository<Attachment, Guid> _attachmentRepository;
    private readonly ILogger<GetAttachmentByFileNameQueryHandler> _logger;
    private readonly IValidator<GetAttachmentByFileNameQuery> _validator;
    private readonly IMapper<AttachmentViewModel, Attachment> _mapper;

    public GetAttachmentByFileNameQueryHandler(IRepository<Attachment, Guid> attachmentRepository,
        ILogger<GetAttachmentByFileNameQueryHandler> logger, IValidator<GetAttachmentByFileNameQuery> validator,
        IMapper<AttachmentViewModel, Attachment> mapper)
    {
        _attachmentRepository = attachmentRepository;
        _logger = logger;
        _validator = validator;
        _mapper = mapper;
    }

    public async Task<Result<AttachmentViewModel>> Handle(GetAttachmentByFileNameQuery request,
        CancellationToken cancellationToken)
    {
        _logger.LogInformation("Получение вложения по имени файла {FileName}", request.FileName);

        // Валидация запроса
        var validationResult = await _validator.ValidateAsync(request, cancellationToken);
        if (!validationResult.IsValid)
        {
            _logger.LogWarning("Ошибка валидации при получении вложения по имени файла: {Errors}",
                string.Join(", ", validationResult.Errors.Select(e => e.ErrorMessage)));
            return Result.Fail(ErrorsFactory.FromValidationResult("Attachment", validationResult));
        }

        var attachment = await _attachmentRepository.GetOneAsync(
            a => a.FileName == request.FileName,
            cancellationToken);

        if (attachment is null)
        {
            _logger.LogWarning("Вложение с именем файла {FileName} не найдено", request.FileName);
            return Result.Fail(ErrorsFactory.NotFound(nameof(Attachment), request.FileName));
        }

        _logger.LogInformation("Вложение с именем файла {FileName} успешно получено", request.FileName);
        return Result.Ok(_mapper.MapToModel(attachment));
    }
}