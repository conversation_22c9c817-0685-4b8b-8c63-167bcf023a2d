using FluentResults;
using MediatR;
using Microsoft.Extensions.Logging;
using TeamFlow.Shared.Contracts.Mapping;
using TeamFlow.Shared.Contracts.Pagination;
using TeamFlow.Shared.Contracts.Sorting;
using TeamFlow.Tasks.Application.Contracts.Contracts.Features.Projects.Queries;
using TeamFlow.Tasks.Application.Contracts.Contracts.ViewModels;
using TeamFlow.Tasks.Application.Errors;
using TeamFlow.Tasks.Core.Abstractions.Repositories;
using TeamFlow.Tasks.Core.Entities;

namespace TeamFlow.Tasks.Application.Features.Projects.Queries;

public sealed class GetProjectsQueryHandler : IRequestHandler<GetProjectsQuery, Result<Page<ProjectViewModel>>>
{
    private readonly IRepository<Project, Guid> _projectsRepository;
    private readonly ILogger<GetProjectsQueryHandler> _logger;
    private readonly IMapper<ProjectViewModel, Project> _mapper;

    public GetProjectsQueryHandler(
        IRepository<Project, Guid> projectsRepository,
        ILogger<GetProjectsQueryHandler> logger, 
        IMapper<ProjectViewModel, Project> mapper)
    {
        _projectsRepository = projectsRepository;
        _logger = logger;
        _mapper = mapper;
    }

    public async Task<Result<Page<ProjectViewModel>>> Handle(GetProjectsQuery request,
        CancellationToken cancellationToken)
    {
        _logger.LogInformation("Получение страницы проектов: страница {PageNumber}, размер {PageSize}",
            request.Pageable.PageNumber, request.Pageable.PageSize);

        try
        {
            var filter = request.Filter?.ToPredicate();

            var sort = request.Sorting is not null
                ? request.Sorting.ToOrderByExpression()
                : new OrderByExpression<Project>(t => t.CreatedAt);

            var pagedResult = await _projectsRepository.GetManyAsync(request.Pageable, filter, sort, cancellationToken);

            if (pagedResult.Elements is null)
            {
                _logger.LogError("Репозиторий вернул null для Elements");
                return Result.Fail(ErrorsFactory.InternalServerError("Ошибка при получении данных"));
            }

            var projects = _mapper.MapToModel(pagedResult.Elements);

            if (projects.Count == 0)
            {
                _logger.LogWarning("Проекты не найдены на странице {PageNumber}", request.Pageable.PageNumber);
                return Result.Fail(ErrorsFactory.NotFound(nameof(Team), "Проекты не найдены"));
            }

            _logger.LogInformation("Успешно получено {Count} проектов на странице {PageNumber}",
                projects.Count, request.Pageable.PageNumber);

            return Result.Ok(new Page<ProjectViewModel>(projects, pagedResult.Count, request.Pageable));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Ошибка при получении страницы проектов");
            throw;
        }
    }
}