using FluentResults;
using FluentValidation;
using MediatR;
using Microsoft.Extensions.Logging;
using TeamFlow.Shared.Contracts.Mapping;
using TeamFlow.Tasks.Application.Contracts.Contracts.Features.Projects.Queries;
using TeamFlow.Tasks.Application.Contracts.Contracts.ViewModels;
using TeamFlow.Tasks.Application.Errors;
using TeamFlow.Tasks.Application.Extensions;
using TeamFlow.Tasks.Core.Abstractions.Repositories;
using TeamFlow.Tasks.Core.Entities;

namespace TeamFlow.Tasks.Application.Features.Projects.Queries;

public sealed class GetProjectByNameQueryHandler : IRequestHandler<GetProjectByNameQuery, Result<ProjectViewModel>>
{
    private readonly IRepository<Project, Guid> _projectsRepository;
    private readonly ILogger<GetProjectByNameQueryHandler> _logger;
    private readonly IMapper<ProjectViewModel, Project> _mapper;
    private readonly IValidator<GetProjectByNameQuery> _validator;

    public GetProjectByNameQueryHandler(
        IRepository<Project, Guid> projectsRepository,
        ILogger<GetProjectByNameQueryHandler> logger, 
        IMapper<ProjectViewModel, Project> mapper,
        IValidator<GetProjectByNameQuery> validator)
    {
        _projectsRepository = projectsRepository;
        _logger = logger;
        _mapper = mapper;
        _validator = validator;
    }

    public async Task<Result<ProjectViewModel>> Handle(GetProjectByNameQuery request,
        CancellationToken cancellationToken)
    {
        _logger.LogInformation("Получение проекта по названию: {ProjectName}", request.Name);

        var validationResult = await _validator.ValidateAsync(request, cancellationToken);
        if (!validationResult.IsValid)
        {
            _logger.LogValidationErrors(validationResult.Errors, "получении проекта по названию");
            return Result.Fail(ErrorsFactory.FromValidationResult(nameof(Project), validationResult));
        }

        var project = await _projectsRepository.GetOneAsync(x => x.Name == request.Name, cancellationToken);

        if (project is null)
        {
            _logger.LogWarning("Проект с названием '{ProjectName}' не найден", request.Name);
            return Result.Fail(
                ErrorsFactory.NotFound(nameof(Project), $"Проект с названием '{request.Name}' не найден"));
        }

        _logger.LogInformation("Проект с названием '{ProjectName}' успешно получен", request.Name);
        return Result.Ok(_mapper.MapToModel(project));
    }
}