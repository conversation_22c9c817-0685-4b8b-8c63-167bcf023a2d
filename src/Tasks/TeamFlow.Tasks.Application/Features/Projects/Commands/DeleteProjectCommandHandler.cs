using FluentResults;
using FluentValidation;
using MediatR;
using Microsoft.Extensions.Logging;
using TeamFlow.Tasks.Application.Contracts.Contracts.Features.Projects.Commands;
using TeamFlow.Tasks.Application.Errors;
using TeamFlow.Tasks.Application.Extensions;
using TeamFlow.Tasks.Core.Abstractions.Repositories;
using TeamFlow.Tasks.Core.Abstractions.Services.Infrastructure;
using TeamFlow.Tasks.Core.Entities;
using TaskEntity = TeamFlow.Tasks.Core.Entities.Task;

namespace TeamFlow.Tasks.Application.Features.Projects.Commands;

public class DeleteProjectCommandHandler : IRequestHandler<DeleteProjectCommand, Result>
{
    private readonly IRepository<Project, Guid> _projectsRepository;
    private readonly IRepository<TaskEntity, Guid> _tasksRepository;
    private readonly ILogger<DeleteProjectCommandHandler> _logger;
    private readonly IValidator<DeleteProjectCommand> _validator;

    public DeleteProjectCommandHandler(IRepository<Project, Guid> projectsRepository, IRepository<TaskEntity, Guid> tasksRepository, IExternalUserService externalUserService, ILogger<DeleteProjectCommandHandler> logger, IValidator<DeleteProjectCommand> validator)
    {
        _projectsRepository = projectsRepository;
        _tasksRepository = tasksRepository;
        _logger = logger;
        _validator = validator;
    }

    public async Task<Result> Handle(DeleteProjectCommand request, CancellationToken cancellationToken)
    {
        _logger.LogInformation("Удаление проекта с ID: {ProjectId}", request.Id);
        
        var validationResult = await _validator.ValidateAsync(request, cancellationToken);
        if (!validationResult.IsValid)
        {
            _logger.LogValidationErrors(validationResult.Errors, "удалении проекта по ID");
            return Result.Fail(ErrorsFactory.FromValidationResult(nameof(Project), validationResult));
        }

        var project = await _projectsRepository.GetOneAsync(x => x.Id == request.Id, cancellationToken);

        if (project is null)
        {
            _logger.LogWarning("Проект с ID {ProjectId} не найден при попытке удаления", request.Id);
            return Result.Fail(ErrorsFactory.NotFound(nameof(Project), request.Id));
        }

        //TODO Добавить транзакции.
        /*
         * Если не сейчас с MongoDB.
         * То потом с PostgreSQL.
         * IUnitOfWork готов.
         * MongoUnitOfWork тоже, но нужно как-то прокидывать сессии в репозиторий.
         * Или хз. Пока пускай так
         */

        _logger.LogInformation("Удаление задач, связанных с проектом {ProjectId}", request.Id);
        await _tasksRepository.BulkDeleteAsync(x => x.ProjectId == request.Id, cancellationToken);

        await _projectsRepository.RemoveAsync(project, cancellationToken);
        _logger.LogInformation("Проект с ID {ProjectId} успешно удален", request.Id);
        return Result.Ok();
    }
}