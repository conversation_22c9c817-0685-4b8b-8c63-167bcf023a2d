using FluentResults;
using FluentValidation;
using MediatR;
using Microsoft.Extensions.Logging;
using TeamFlow.Tasks.Application.Contracts.Contracts.Features.Task.Commands;
using TeamFlow.Tasks.Application.Errors;
using TeamFlow.Tasks.Application.Extensions;
using TeamFlow.Tasks.Core.Abstractions.Repositories;
using TaskEntity = TeamFlow.Tasks.Core.Entities.Task;

namespace TeamFlow.Tasks.Application.Features.Tasks.Commands;

public sealed class DeleteTaskCommandHandler : IRequestHandler<DeleteTaskCommand, Result>
{
    private readonly IRepository<TaskEntity, Guid> _taskRepository;
    private readonly ILogger<DeleteTaskCommandHandler> _logger;
    private readonly IValidator<DeleteTaskCommand> _validator;

    public DeleteTaskCommandHandler(
        IRepository<TaskEntity, Guid> taskRepository, 
        ILogger<DeleteTaskCommandHandler> logger, 
        IValidator<DeleteTaskCommand> validator)
    {
        _taskRepository = taskRepository;
        _logger = logger;
        _validator = validator;
    }


    public async Task<Result> Handle(DeleteTaskCommand request, CancellationToken cancellationToken)
    {
        _logger.LogInformation("Удаление задачи с идентификатором {TaskId}", request.Id);
        
        var validationResult = await _validator.ValidateAsync(request, cancellationToken);
        if (!validationResult.IsValid)
        {
            _logger.LogValidationErrors(validationResult.Errors, "удалении задачи");
            return Result.Fail(ErrorsFactory.FromValidationResult(nameof(TaskEntity), validationResult));
        }

        var task = await _taskRepository.GetOneAsync(x => x.Id == request.Id, cancellationToken);
        if (task is null)
        {
            _logger.LogWarning("Задача с идентификатором {TaskId} не найдена", request.Id);
            return Result.Fail(ErrorsFactory.NotFound("Task", request.Id));
        }

        await _taskRepository.RemoveAsync(task, cancellationToken);
        _logger.LogInformation("Задача с идентификатором {TaskId} успешно удалена", request.Id);

        return Result.Ok();
    }
}