using FluentResults;
using FluentValidation;
using MediatR;
using Microsoft.Extensions.Logging;
using TeamFlow.Tasks.Application.Contracts.Contracts.Features.Task.Commands;
using TeamFlow.Tasks.Application.Errors;
using TeamFlow.Tasks.Core.Abstractions.Repositories;
using TeamFlow.Tasks.Core.Abstractions.Services.Infrastructure;
using TeamFlow.Tasks.Core.Entities;
using TeamFlow.Tasks.Core.Shared.Enums;
using TaskEntity = TeamFlow.Tasks.Core.Entities.Task;

namespace TeamFlow.Tasks.Application.Features.Tasks.Commands;

public sealed class CreateTaskCommandHandler : IRequestHandler<CreateTaskCommand, Result<Guid>>
{
    private readonly IRepository<TaskEntity, Guid> _taskRepository;
    private readonly ILogger<CreateTaskCommandHandler> _logger;
    private readonly IValidator<CreateTaskCommand> _validator;
    private readonly IExternalUserService _externalUserService;
    private readonly IRepository<Project, Guid> _projectRepository;
    private readonly IRepository<KanbanColumn, Guid> _columnRepository;

    public CreateTaskCommandHandler(
        IRepository<TaskEntity, Guid> taskRepository,
        ILogger<CreateTaskCommandHandler> logger, 
        IValidator<CreateTaskCommand> validator,
        IExternalUserService externalUserService, 
        IRepository<Project, Guid> projectRepository,
        IRepository<KanbanColumn, Guid> columnRepository)
    {
        _taskRepository = taskRepository;
        _logger = logger;
        _validator = validator;
        _externalUserService = externalUserService;
        _projectRepository = projectRepository;
        _columnRepository = columnRepository;
    }

    public async Task<Result<Guid>> Handle(CreateTaskCommand request, CancellationToken cancellationToken)
    {
        _logger.LogInformation("Создание новой задачи с названием '{Title}'", request.Title);

        var validationResult = await _validator.ValidateAsync(request, cancellationToken);
        if (!validationResult.IsValid)
        {
            _logger.LogWarning("Ошибка валидации при создании задачи: {Errors}",
                string.Join(", ", validationResult.Errors.Select(e => e.ErrorMessage)));
            return Result.Fail(ErrorsFactory.FromValidationResult("Task", validationResult));
        }

        var isProjectExists =
            await _projectRepository.FindAnyAsync(x => x.Id == request.ProjectId, cancellationToken);
        if (!isProjectExists)
        {
            _logger.LogWarning("Проект с идентификатором {ProjectId} не найден", request.ProjectId);
            return Result.Fail(ErrorsFactory.NotFound(nameof(Project), request.ProjectId));
        }

        // Проверка существования репортера
        var reporter = await _externalUserService.GetByGuidAsync(request.ReporterId, cancellationToken);
        if (reporter.IsFailed)
        {
            _logger.LogWarning("Пользователь-репортер с идентификатором {ReporterId} не найден",
                request.ReporterId);
            return Result.Fail(ErrorsFactory.NotFound("User", request.ReporterId));
        }

        // Проверка существования канбан-колонки, если указана
        if (request.KanbanColumnId.HasValue)
        {
            var isColumnExists =
                await _columnRepository.FindAnyAsync(x => x.Id == request.KanbanColumnId, cancellationToken);
            if (!isColumnExists)
            {
                _logger.LogWarning("Канбан-колонка с идентификатором {ColumnId} не найдена",
                    request.KanbanColumnId);
                return Result.Fail(ErrorsFactory.NotFound(nameof(KanbanColumn), request.KanbanColumnId));
            }
        }

        var task = new TaskEntity
        {
            Id = Guid.CreateVersion7(),
            Title = request.Title,
            Description = request.Description,
            ProjectId = request.ProjectId,
            ReporterId = request.ReporterId,
            KanbanColumnId = request.KanbanColumnId,
            DueDate = request.DueDate,
            Priority = request.Priority,
            Type = request.Type,
            StoryPoints = request.StoryPoint,
            Tags = request.Tags ?? [],
            Status = TaskStatuses.Open,
            EstimatedTime = TimeSpan.Zero,
            ActualTime = TimeSpan.Zero,
            KanbanOrder = 0
        };

        await _taskRepository.AddAsync(task, cancellationToken);
        _logger.LogInformation("Задача успешно создана с идентификатором {TaskId}", task.Id);

        return Result.Ok(task.Id);
    }
}