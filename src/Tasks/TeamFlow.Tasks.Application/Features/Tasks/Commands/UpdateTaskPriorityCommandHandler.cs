using FluentResults;
using FluentValidation;
using MediatR;
using Microsoft.Extensions.Logging;
using TeamFlow.Tasks.Application.Contracts.Contracts.Features.Task.Commands;
using TeamFlow.Tasks.Application.Errors;
using TeamFlow.Tasks.Core.Abstractions.Repositories;
using TaskEntity = TeamFlow.Tasks.Core.Entities.Task;

namespace TeamFlow.Tasks.Application.Features.Tasks.Commands;

public sealed class UpdateTaskPriorityCommandHandler : IRequestHandler<UpdateTaskPriorityCommand, Result>
{
    private readonly IRepository<TaskEntity, Guid> _tasksRepository;
    private readonly ILogger<UpdateTaskPriorityCommandHandler> _logger;
    private readonly IValidator<UpdateTaskPriorityCommand> _validator;

    public UpdateTaskPriorityCommandHandler(IRepository<TaskEntity, Guid> tasksRepository, ILogger<UpdateTaskPriorityCommandHandler> logger, IValidator<UpdateTaskPriorityCommand> validator)
    {
        _tasksRepository = tasksRepository;
        _logger = logger;
        _validator = validator;
    }

    public async Task<Result> Handle(UpdateTaskPriorityCommand request, CancellationToken cancellationToken)
    {
        _logger.LogInformation("Обновление приоритета задачи {TaskId} на {Priority}", request.TaskId,
            request.PriorityLevel);
        
        var validationResult = await _validator.ValidateAsync(request, cancellationToken);
        if (!validationResult.IsValid)
        {
            _logger.LogWarning("Ошибка валидации при обновлении приоритета задачи: {Errors}",
                string.Join(", ", validationResult.Errors.Select(e => e.ErrorMessage)));
            return Result.Fail(ErrorsFactory.FromValidationResult("Task", validationResult));
        }

        var task = await _tasksRepository.GetOneAsync(x => x.Id == request.TaskId, cancellationToken);
        if (task is null)
        {
            _logger.LogWarning("Задача с идентификатором {TaskId} не найдена", request.TaskId);
            return Result.Fail(ErrorsFactory.NotFound(nameof(TaskEntity), request.TaskId));
        }

        _logger.LogInformation("Изменение приоритета задачи {TaskId} с {OldPriority} на {NewPriority}",
            request.TaskId, task.Priority, request.PriorityLevel);
        task.Priority = request.PriorityLevel;
        await _tasksRepository.UpdateAsync(task, cancellationToken);
        _logger.LogInformation("Приоритет задачи {TaskId} успешно обновлен на {Priority}", request.TaskId,
            request.PriorityLevel);

        return Result.Ok();
    }
}