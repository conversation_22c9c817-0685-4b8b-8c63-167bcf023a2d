using System.Linq.Expressions;
using FluentResults;
using FluentValidation;
using MediatR;
using Microsoft.Extensions.Logging;
using TeamFlow.Shared.Utils;
using TeamFlow.Tasks.Application.Contracts.Contracts.Features.Task.Commands;
using TeamFlow.Tasks.Application.Errors;
using TeamFlow.Tasks.Core.Abstractions.Repositories;
using TeamFlow.Tasks.Core.Abstractions.Services.Infrastructure;
using TeamFlow.Tasks.Core.Entities;
using Task = System.Threading.Tasks.Task;
using TaskEntity = TeamFlow.Tasks.Core.Entities.Task;

namespace TeamFlow.Tasks.Application.Features.Tasks.Commands;

public sealed class UpdateTaskCommandHandler : IRequestHandler<UpdateTaskCommand, Result<Guid>>
{
    private readonly IRepository<TaskEntity, Guid> _tasksRepository;
    private readonly IRepository<KanbanColumn, Guid> _columnRepository;
    private readonly ILogger<UpdateTaskCommandHandler> _logger;
    private readonly IValidator<UpdateTaskCommand> _validator;
    private readonly IExternalUserService _externalUserService;

    public UpdateTaskCommandHandler(IRepository<TaskEntity, Guid> tasksRepository, IRepository<KanbanColumn, Guid> columnRepository, ILogger<UpdateTaskCommandHandler> logger, IValidator<UpdateTaskCommand> validator, IExternalUserService externalUserService)
    {
        _tasksRepository = tasksRepository;
        _columnRepository = columnRepository;
        _logger = logger;
        _validator = validator;
        _externalUserService = externalUserService;
    }

    public async Task<Result<Guid>> Handle(UpdateTaskCommand request, CancellationToken cancellationToken)
    {
        _logger.LogInformation("Обновление задачи с идентификатором {TaskId}", request.Id);

        var validationResult = await _validator.ValidateAsync(request, cancellationToken);
        if (!validationResult.IsValid)
        {
            _logger.LogWarning("Ошибка валидации при обновлении задачи: {Errors}",
                string.Join(", ", validationResult.Errors.Select(e => e.ErrorMessage)));
            return Result.Fail(ErrorsFactory.FromValidationResult("Task", validationResult));
        }

        var task = await _tasksRepository.GetOneAsync(x => x.Id == request.Id, cancellationToken);
        if (task is null)
        {
            _logger.LogWarning("Задача с идентификатором {TaskId} не найдена", request.Id);
            return Result.Fail(ErrorsFactory.NotFound("Task", request.Id));
        }

        if (request.AssigneeId.HasValue)
        {
            _logger.LogInformation("Проверка существования исполнителя {AssigneeId}", request.AssigneeId);
            var user = await _externalUserService.GetByGuidAsync((Guid)request.AssigneeId, cancellationToken);

            if (user.IsFailed)
            {
                _logger.LogWarning("Пользователь-исполнитель с идентификатором {AssigneeId} не найден",
                    request.AssigneeId);
                return Result.Fail(ErrorsFactory.NotFound("User", request.AssigneeId));
            }
        }

        var newTitle = ValueComparer.GetNewValueIfUpdated(task.Title, request.Title);
        var newDescription = ValueComparer.GetNewValueIfUpdated(task.Description, request.Description);
        var newKanbanColumn = ValueComparer.GetNewValueIfUpdated(task.KanbanColumnId, request.KanbanColumnId);
        var newExecutor = ValueComparer.GetNewValueIfUpdated(task.AssignedId, request.AssigneeId);
        var newType = ValueComparer.GetNewValueIfUpdated(task.Type, request.Type);
        var newOrder = ValueComparer.GetNewValueIfUpdated(task.KanbanOrder, request.KanbanOrder);
        var newStoryPointsValue = ValueComparer.GetNewValueIfUpdated(task.StoryPoints, request.StoryPoints);

        var isTitleUpdated = newTitle is not null;
        var isKanbanColumnUpdated = newKanbanColumn is not null;

        if (newExecutor is not null)
        {
            var user = await _externalUserService.GetByGuidAsync(newExecutor.Value, cancellationToken);
            if (user.IsFailed)
            {
                return Result.Fail(ErrorsFactory.NotFound("Executor", newExecutor.Value));
            }
        }

        if (isTitleUpdated || isKanbanColumnUpdated)
        {
            Expression<Func<TaskEntity, bool>> filter = t =>
                isTitleUpdated && t.Title == request.Title && t.Id != request.Id;

            var columnTask =
                _columnRepository.GetOneAsync(x => x.Id == request.KanbanColumnId, cancellationToken);

            var isTaskExistsTask = _tasksRepository.FindAnyAsync(filter, cancellationToken);

            await Task.WhenAll(columnTask, isTaskExistsTask);

            // var (column, isTaskExists) = (await columnTask, await isTaskExistsTask);

            var isColumnExists = columnTask.Result is null;

            if (isTaskExistsTask.Result)
            {
                return Result.Fail(ErrorsFactory.AlreadyExists("Task", "Title", request.Title!));
            }

            if (isColumnExists)
            {
                return Result.Fail(ErrorsFactory.NotFound(nameof(KanbanColumn), request.KanbanColumnId!));
            }

            if (columnTask.Result?.ProjectId != task.ProjectId)
            {
                return Result.Fail(
                    ErrorsFactory.Conflict("Task", "Задача и колонка должны принадлежать одному проекту"));
            }
        }

        task.Title = newTitle ?? task.Title;
        task.Description = newDescription ?? task.Description;
        task.KanbanColumnId = newKanbanColumn ?? task.KanbanColumnId;
        task.KanbanOrder = newOrder ?? task.KanbanOrder;
        task.StoryPoints = newStoryPointsValue ?? task.StoryPoints;
        task.AssignedId = newExecutor ?? task.Id;
        task.Type = newType ?? task.Type;

        await _tasksRepository.UpdateAsync(task, cancellationToken);
        _logger.LogInformation("Задача с идентификатором {TaskId} успешно обновлена", request.Id);
        return Result.Ok(request.Id);
    }
}