using FluentResults;
using FluentValidation;
using MediatR;
using Microsoft.Extensions.Logging;
using TeamFlow.Tasks.Application.Contracts.Contracts.Features.Task.Commands;
using TeamFlow.Tasks.Application.Errors;
using TeamFlow.Tasks.Application.Extensions;
using TeamFlow.Tasks.Core.Abstractions.Repositories;
using TeamFlow.Tasks.Core.Abstractions.Services.Infrastructure;
using TaskEntity = TeamFlow.Tasks.Core.Entities.Task;

namespace TeamFlow.Tasks.Application.Features.Tasks.Commands;

public sealed class AssignTaskCommandHandler : IRequestHandler<AssignTaskCommand, Result>
{
    private readonly IRepository<TaskEntity, Guid> _taskRepository;
    private readonly ILogger<AssignTaskCommandHandler> _logger;
    private readonly IValidator<AssignTaskCommand> _validator;
    private readonly IExternalUserService _externalUserService;

    public AssignTaskCommandHandler(
        IRepository<TaskEntity, Guid> taskRepository, 
        ILogger<AssignTaskCommandHandler> logger, 
        IValidator<AssignTaskCommand> validator, 
        IExternalUserService externalUserService)
    {
        _taskRepository = taskRepository;
        _logger = logger;
        _validator = validator;
        _externalUserService = externalUserService;
    }

    public async Task<Result> Handle(AssignTaskCommand request, CancellationToken cancellationToken)
    {
        _logger.LogInformation("Назначение задачи с ID '{TaskId}' пользователю с ID '{UserId}'", request.TaskId,
            request.UserId);

        var validationResult = await _validator.ValidateAsync(request, cancellationToken);
        if (!validationResult.IsValid)
        {
            _logger.LogValidationErrors(validationResult.Errors, "назначении задачи");
            return Result.Fail(ErrorsFactory.FromValidationResult("Task", validationResult));
        }

        var task = await _taskRepository.GetOneAsync(x => x.Id == request.TaskId, cancellationToken);
        if (task is null)
        {
            _logger.LogWarning("Задача с идентификатором {TaskId} не найдена", request.TaskId);
            return Result.Fail(ErrorsFactory.NotFound(nameof(TaskEntity), request.TaskId));
        }

        var user = await _externalUserService.GetByGuidAsync(request.UserId, cancellationToken);
        if (user.IsFailed)
        {
            _logger.LogWarning("Пользователь с идентификатором {UserId} не найден", request.UserId);
            return Result.Fail(ErrorsFactory.NotFound("User", request.UserId));
        }

        task.AssignedId = request.UserId;
        await _taskRepository.UpdateAsync(task, cancellationToken);
        _logger.LogInformation("Задача {TaskId} успешно назначена пользователю {UserId}", request.TaskId,
            request.UserId);
        return Result.Ok();
    }
}