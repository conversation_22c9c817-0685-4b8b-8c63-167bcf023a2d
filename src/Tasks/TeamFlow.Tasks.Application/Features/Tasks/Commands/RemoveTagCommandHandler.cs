using FluentResults;
using FluentValidation;
using MediatR;
using Microsoft.Extensions.Logging;
using TeamFlow.Tasks.Application.Contracts.Contracts.Features.Task.Commands;
using TeamFlow.Tasks.Application.Errors;
using TeamFlow.Tasks.Application.Extensions;
using TeamFlow.Tasks.Core.Abstractions.Repositories;
using TaskEntity = TeamFlow.Tasks.Core.Entities.Task;

namespace TeamFlow.Tasks.Application.Features.Tasks.Commands;

public sealed class RemoveTagCommandHandler : IRequestHandler<RemoveTagCommand, Result>
{
    private readonly IRepository<TaskEntity, Guid> _tasksRepository;
    private readonly ILogger<RemoveTagCommandHandler> _logger;
    private readonly IValidator<RemoveTagCommand> _validator;

    public RemoveTagCommandHandler(
        IRepository<TaskEntity, Guid> tasksRepository,
        ILogger<RemoveTagCommandHandler> logger, 
        IValidator<RemoveTagCommand> validator)
    {
        _tasksRepository = tasksRepository;
        _logger = logger;
        _validator = validator;
    }

    public async Task<Result> Handle(RemoveTagCommand request, CancellationToken cancellationToken)
    {
        _logger.LogInformation("Удаление тега '{Tag}' из задачи {TaskId}", request.Tag, request.TaskId);

        var validationResult = await _validator.ValidateAsync(request, cancellationToken);
        if (!validationResult.IsValid)
        {
            _logger.LogValidationErrors(validationResult.Errors, "удалении тега");
            return Result.Fail(ErrorsFactory.FromValidationResult("Task", validationResult));
        }

        var task = await _tasksRepository.GetOneAsync(x => x.Id == request.TaskId, cancellationToken);
        if (task is null)
        {
            _logger.LogWarning("Задача с идентификатором {TaskId} не найдена", request.TaskId);
            return Result.Fail(ErrorsFactory.NotFound(nameof(TaskEntity), request.TaskId));
        }

        if (task.Tags is null || !task.Tags!.Contains(request.Tag))
        {
            _logger.LogWarning("Тег '{Tag}' не найден в задаче {TaskId}", request.Tag, request.TaskId);
            return Result.Fail(ErrorsFactory.Conflict("Tag", $"Tag '{request.Tag}' already exists in task"));
        }

        task.Tags.Remove(request.Tag);
        await _tasksRepository.UpdateAsync(task, cancellationToken);
        _logger.LogInformation("Тег '{Tag}' успешно удален из задачи {TaskId}", request.Tag, request.TaskId);
        return Result.Ok();
    }
}