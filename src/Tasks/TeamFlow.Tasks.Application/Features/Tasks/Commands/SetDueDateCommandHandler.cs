using FluentResults;
using FluentValidation;
using MediatR;
using Microsoft.Extensions.Logging;
using TeamFlow.Tasks.Application.Contracts.Contracts.Features.Task.Commands;
using TeamFlow.Tasks.Application.Errors;
using TeamFlow.Tasks.Application.Extensions;
using TeamFlow.Tasks.Core.Abstractions.Repositories;
using TaskEntity = TeamFlow.Tasks.Core.Entities.Task;

namespace TeamFlow.Tasks.Application.Features.Tasks.Commands;

public sealed class SetDueDateCommandHandler : IRequestHandler<SetDueDateCommand, Result>
{
    private readonly IRepository<TaskEntity, Guid> _tasksRepository;
    private readonly ILogger<SetDueDateCommandHandler> _logger;
    private readonly IValidator<SetDueDateCommand> _validator;

    public SetDueDateCommandHandler(
        IRepository<TaskEntity, Guid> tasksRepository,
        ILogger<SetDueDateCommandHandler> logger, 
        IValidator<SetDueDateCommand> validator)
    {
        _tasksRepository = tasksRepository;
        _logger = logger;
        _validator = validator;
    }

    public async Task<Result> Handle(SetDueDateCommand request, CancellationToken cancellationToken)
    {
        _logger.LogInformation("Установка срока выполнения для задачи {TaskId}: {DueDate}", request.TaskId,
            request.DueDate);

        var validationResult = await _validator.ValidateAsync(request, cancellationToken);
        if (!validationResult.IsValid)
        {
            _logger.LogValidationErrors(validationResult.Errors, "установке срока выполнения");
            return Result.Fail(ErrorsFactory.FromValidationResult("Task", validationResult));
        }

        var task = await _tasksRepository.GetOneAsync(x => x.Id == request.TaskId, cancellationToken);
        if (task is null)
        {
            _logger.LogWarning("Задача с идентификатором {TaskId} не найдена", request.TaskId);
            return Result.Fail(ErrorsFactory.NotFound(nameof(TaskEntity), request.TaskId));
        }

        _logger.LogInformation("Изменение срока выполнения задачи {TaskId} с {OldDueDate} на {NewDueDate}",
            request.TaskId, task.DueDate, request.DueDate);
        task.DueDate = request.DueDate;
        await _tasksRepository.UpdateAsync(task, cancellationToken);
        _logger.LogInformation("Срок выполнения для задачи {TaskId} успешно установлен на {DueDate}", request.TaskId,
            request.DueDate);
        return Result.Ok();
    }
}