using FluentResults;
using FluentValidation;
using MediatR;
using Microsoft.Extensions.Logging;
using TeamFlow.Tasks.Application.Contracts.Contracts.Features.Task.Commands;
using TeamFlow.Tasks.Application.Errors;
using TeamFlow.Tasks.Application.Extensions;
using TeamFlow.Tasks.Core.Abstractions.Repositories;
using TaskEntity = TeamFlow.Tasks.Core.Entities.Task;

namespace TeamFlow.Tasks.Application.Features.Tasks.Commands;

public sealed class UpdateEstimatedTimeCommandHandler : IRequestHandler<UpdateEstimatedTimeCommand, Result>
{
    private readonly IRepository<TaskEntity, Guid> _tasksRepository;
    private readonly ILogger<UpdateEstimatedTimeCommandHandler> _logger;
    private readonly IValidator<UpdateEstimatedTimeCommand> _validator;

    public UpdateEstimatedTimeCommandHandler(IRepository<TaskEntity, Guid> tasksRepository, ILogger<UpdateEstimatedTimeCommandHandler> logger, IValidator<UpdateEstimatedTimeCommand> validator)
    {
        _tasksRepository = tasksRepository;
        _logger = logger;
        _validator = validator;
    }

    public async Task<Result> Handle(UpdateEstimatedTimeCommand request, CancellationToken cancellationToken)
    {
        _logger.LogInformation("Обновление оценочного времени для задачи {TaskId}: {EstimatedTime}",
            request.TaskId, request.EstimatedTime);
        
        var validationResult = await _validator.ValidateAsync(request, cancellationToken);
        if (!validationResult.IsValid)
        {
            _logger.LogValidationErrors(validationResult.Errors, "обновлении оценочного времени");
            return Result.Fail(ErrorsFactory.FromValidationResult("Task", validationResult));
        }

        var task = await _tasksRepository.GetOneAsync(x => x.Id == request.TaskId, cancellationToken);
        if (task is null)
        {
            _logger.LogWarning("Задача с идентификатором {TaskId} не найдена", request.TaskId);
            return Result.Fail(ErrorsFactory.NotFound(nameof(TaskEntity), request.TaskId));
        }

        _logger.LogInformation(
            "Изменение оценочного времени задачи {TaskId} с {OldEstimatedTime} на {NewEstimatedTime}",
            request.TaskId, task.EstimatedTime, request.EstimatedTime);
        task.EstimatedTime = request.EstimatedTime;
        await _tasksRepository.UpdateAsync(task, cancellationToken);
        _logger.LogInformation("Оценочное время для задачи {TaskId} успешно обновлено на {EstimatedTime}",
            request.TaskId, request.EstimatedTime);
        return Result.Ok();
    }
}