using FluentResults;
using FluentValidation;
using MediatR;
using Microsoft.Extensions.Logging;
using TeamFlow.Tasks.Application.Contracts.Contracts.Features.Task.Commands;
using TeamFlow.Tasks.Application.Errors;
using TeamFlow.Tasks.Application.Extensions;
using TeamFlow.Tasks.Core.Shared.Enums;

namespace TeamFlow.Tasks.Application.Features.Tasks.Commands;

public sealed class MarkTaskAsCompleteCommandHandler : IRequestHandler<MarkTaskAsCompleteCommand, Result>
{
    private readonly ILogger<MarkTaskAsCompleteCommandHandler> _logger;
    private readonly IValidator<MarkTaskAsCompleteCommand> _validator;
    private readonly IMediator _mediator;

    public MarkTaskAsCompleteCommandHandler(
        ILogger<MarkTaskAsCompleteCommandHandler> logger,
        IValidator<MarkTaskAsCompleteCommand> validator,
        IMediator mediator)
    {
        _logger = logger;
        _validator = validator;
        _mediator = mediator;
    }

    public async Task<Result> Handle(MarkTaskAsCompleteCommand request, CancellationToken cancellationToken)
    {
        var validationResult = await _validator.ValidateAsync(request, cancellationToken);
        if (!validationResult.IsValid)
        {
            _logger.LogValidationErrors(validationResult.Errors, "отметке задачи как завершенной");
            return Result.Fail(ErrorsFactory.FromValidationResult("Task", validationResult));
        }

        _logger.LogInformation("Отметка задачи {TaskId} как завершенной", request.TaskId);
        var result = await _mediator.Send(new UpdateTaskStatusCommand(request.TaskId, TaskStatuses.Completed),
            cancellationToken);

        if (result.IsFailed)
        {
            _logger.LogWarning("Не удалось отметить задачу {TaskId} как завершенную: {Errors}",
                request, string.Join(", ", result.Errors.Select(e => e.Message)));
            return Result.Fail(result.Errors);
        }

        _logger.LogInformation("Задача {TaskId} успешно отмечена как завершенная", request.TaskId);
        return Result.Ok();
    }
}