using System.Net;
using FluentResults;
using FluentValidation;
using MediatR;
using Microsoft.Extensions.Logging;
using TeamFlow.Shared.Contracts.Mapping;
using TeamFlow.Shared.Contracts.Pagination;
using TeamFlow.Shared.Contracts.Sorting;
using TeamFlow.Tasks.Application.Contracts.Contracts.Features.Task.Queries;
using TeamFlow.Tasks.Application.Contracts.Contracts.ViewModels;
using TeamFlow.Tasks.Application.Errors;
using TeamFlow.Tasks.Core.Abstractions.Repositories;
using TaskEntity = TeamFlow.Tasks.Core.Entities.Task;


namespace TeamFlow.Tasks.Application.Features.Tasks.Queries;

public sealed class GetTasksQueryHandler : IRequestHandler<GetTasksQuery, Result<Page<TaskViewModel>>>
{
    private readonly IRepository<TaskEntity, Guid> _taskRepository;
    private readonly ILogger<GetTasksQueryHandler> _logger;
    private readonly IMapper<TaskViewModel, TaskEntity> _mapper;

    public GetTasksQueryHandler(IRepository<TaskEntity, Guid> taskRepository, ILogger<GetTasksQueryHandler> logger, IMapper<TaskViewModel, TaskEntity> mapper)
    {
        _taskRepository = taskRepository;
        _logger = logger;
        _mapper = mapper;
    }


    public async Task<Result<Page<TaskViewModel>>> Handle(GetTasksQuery request, CancellationToken cancellationToken)
    {
        _logger.LogInformation("Получение страницы задач: страница {PageNumber}, размер {PageSize}",
            request.Pageable.PageNumber, request.Pageable.PageSize);

        try
        {
            var filter = request.Filter?.ToPredicate();

            var sort = request.Sorting is not null
                ? request.Sorting.ToOrderByExpression()
                : new OrderByExpression<TaskEntity>(t => t.CreatedAt);

            var pagedResult = await _taskRepository.GetManyAsync(request.Pageable, filter, sort, cancellationToken);

            if (pagedResult.Elements is null)
            {
                _logger.LogError("Репозиторий вернул null для Elements");
                return Result.Fail(ErrorsFactory.Custom("Ошибка при получении данных", HttpStatusCode.InternalServerError));
            }

            // var tasks = pagedResult.Elements.Select(x => x.ToViewModel()).ToList();
            var tasks = _mapper.MapToModel(pagedResult.Elements);

            if (tasks.Count == 0)
            {
                _logger.LogWarning("Задачи не найдены на странице {PageNumber}", request.Pageable.PageNumber);
                return Result.Fail(ErrorsFactory.NotFound("Task", "Задачи не найдены"));
            }

            _logger.LogInformation("Успешно получено {Count} задач на странице {PageNumber}",
                tasks.Count, request.Pageable.PageNumber);

            return Result.Ok(new Page<TaskViewModel>(tasks, pagedResult.Count, request.Pageable));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Ошибка при получении страницы задач");
            throw;
        }
    }
}