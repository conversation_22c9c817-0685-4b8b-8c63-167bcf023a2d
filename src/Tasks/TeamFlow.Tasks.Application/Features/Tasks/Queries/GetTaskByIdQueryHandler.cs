using FluentResults;
using FluentValidation;
using MediatR;
using Microsoft.Extensions.Logging;
using TeamFlow.Shared.Contracts.Mapping;
using TeamFlow.Tasks.Application.Contracts.Contracts.Features.Task.Queries;
using TeamFlow.Tasks.Application.Contracts.Contracts.ViewModels;
using TeamFlow.Tasks.Application.Errors;
using TeamFlow.Tasks.Application.Extensions;
using TeamFlow.Tasks.Core.Abstractions.Repositories;
using TaskEntity = TeamFlow.Tasks.Core.Entities.Task;

namespace TeamFlow.Tasks.Application.Features.Tasks.Queries;

public sealed class GetTaskByIdQueryHandler : IRequestHandler<GetTaskByIdQuery, Result<TaskViewModel>>
{
    private readonly IRepository<TaskEntity, Guid> _taskRepository;
    private readonly ILogger<GetTaskByIdQueryHandler> _logger;
    private readonly IValidator<GetTaskByIdQuery> _validator;
    private readonly IMapper<TaskViewModel, TaskEntity> _mapper;

    public GetTaskByIdQueryHandler(
        IRepository<TaskEntity, Guid> taskRepository, 
        ILogger<GetTaskByIdQueryHandler> logger,
        IMapper<TaskViewModel, TaskEntity> mapper, 
        IValidator<GetTaskByIdQuery> validator)
    {
        _taskRepository = taskRepository;
        _logger = logger;
        _mapper = mapper;
        _validator = validator;
    }

    public async Task<Result<TaskViewModel>> Handle(GetTaskByIdQuery request, CancellationToken cancellationToken)
    {
        _logger.LogInformation("Получение задачи по идентификатору: {TaskId}", request.Id);
        
        var validationResult = await _validator.ValidateAsync(request, cancellationToken);
        if (!validationResult.IsValid)
        {
            _logger.LogValidationErrors(validationResult.Errors, "получении задачи по ID");
            return Result.Fail(ErrorsFactory.FromValidationResult(nameof(TaskEntity), validationResult));
        }

        var task = await _taskRepository.GetOneAsync(x => x.Id == request.Id, cancellationToken);

        if (task is null)
        {
            _logger.LogWarning("Задача с идентификатором {TaskId} не найдена", request.Id);
            return Result.Fail(ErrorsFactory.NotFound("Task", request.Id));
        }

        _logger.LogInformation("Задача с идентификатором {TaskId} успешно получена", request.Id);
        return Result.Ok(_mapper.MapToModel(task));

    }
}