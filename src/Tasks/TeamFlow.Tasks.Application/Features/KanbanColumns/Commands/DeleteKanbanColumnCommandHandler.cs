using FluentResults;
using FluentValidation;
using MediatR;
using Microsoft.Extensions.Logging;
using TeamFlow.Tasks.Application.Contracts.Contracts.Features.KanbanColumn.Commands;
using TeamFlow.Tasks.Application.Errors;
using TeamFlow.Tasks.Application.Extensions;
using TeamFlow.Tasks.Core.Abstractions.Repositories;
using TeamFlow.Tasks.Core.Entities;

namespace TeamFlow.Tasks.Application.Features.KanbanColumns.Commands;

public sealed class DeleteKanbanColumnCommandHandler : IRequestHandler<DeleteKanbanColumnCommand, Result>
{
    private readonly IRepository<KanbanColumn, Guid> _columnsRepository;
    private readonly ILogger<DeleteKanbanColumnCommandHandler> _logger;
    private readonly IValidator<DeleteKanbanColumnCommand> _validator;

    public DeleteKanbanColumnCommandHandler(IRepository<KanbanColumn, Guid> columnsRepository, ILogger<DeleteKanbanColumnCommandHandler> logger, IValidator<DeleteKanbanColumnCommand> validator)
    {
        _columnsRepository = columnsRepository;
        _logger = logger;
        _validator = validator;
    }

    public async Task<Result> Handle(DeleteKanbanColumnCommand request, CancellationToken cancellationToken)
    {
        _logger.LogInformation("Удаление колонки с ID: {Id}", request.Id);
        
        var validationResult = await _validator.ValidateAsync(request, cancellationToken);
        if (!validationResult.IsValid)
        {
            _logger.LogValidationErrors(validationResult.Errors, "удалении колонки");
        }

        var column = await _columnsRepository.GetOneAsync(x => x.Id == request.Id, cancellationToken);

        if (column is null)
        {
            _logger.LogWarning("Колонка с идентификатором {Id} не найдена для удаления", request.Id);
            return Result.Fail(ErrorsFactory.NotFound(nameof(KanbanColumn), request.Id));
        }

        await _columnsRepository.RemoveAsync(column, cancellationToken);
        _logger.LogInformation("Колонка с ID {Id} успешно удалена", request.Id);

        return Result.Ok();
    }
}