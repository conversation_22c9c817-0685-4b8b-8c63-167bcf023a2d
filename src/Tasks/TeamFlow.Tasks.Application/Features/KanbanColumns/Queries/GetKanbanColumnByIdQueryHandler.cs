using FluentResults;
using FluentValidation;
using MediatR;
using Microsoft.Extensions.Logging;
using TeamFlow.Shared.Contracts.Mapping;
using TeamFlow.Tasks.Application.Contracts.Contracts.Features.KanbanColumn.Queries;
using TeamFlow.Tasks.Application.Contracts.Contracts.ViewModels;
using TeamFlow.Tasks.Application.Errors;
using TeamFlow.Tasks.Application.Extensions;
using TeamFlow.Tasks.Core.Abstractions.Repositories;
using TeamFlow.Tasks.Core.Entities;

namespace TeamFlow.Tasks.Application.Features.KanbanColumns.Queries;

public sealed class GetKanbanColumnByIdQueryHandler : IRequestHandler<GetKanbanColumnByIdQuery, Result<KanbanColumnViewModel>>
{
    private readonly IRepository<KanbanColumn, Guid> _columnsRepository;
    private readonly ILogger<GetKanbanColumnByIdQueryHandler> _logger;
    private readonly IValidator<GetKanbanColumnByIdQuery> _validator;
    private readonly IMapper<KanbanColumnViewModel, KanbanColumn> _mapper;

    public GetKanbanColumnByIdQueryHandler(
        IRepository<KanbanColumn, Guid> columnsRepository, 
        ILogger<GetKanbanColumnByIdQueryHandler> logger, 
        IValidator<GetKanbanColumnByIdQuery> validator, 
        IMapper<KanbanColumnViewModel, KanbanColumn> mapper)
    {
        _columnsRepository = columnsRepository;
        _logger = logger;
        _validator = validator;
        _mapper = mapper;
    }

    public async Task<Result<KanbanColumnViewModel>> Handle(GetKanbanColumnByIdQuery request, CancellationToken cancellationToken)
    {
        _logger.LogInformation("Получение колонки по идентификатору: {Id}", request.Id);
        
        var validationResult = await _validator.ValidateAsync(request, cancellationToken);
        if (!validationResult.IsValid)
        {
            _logger.LogValidationErrors(validationResult.Errors, "получении колонки по ID");
            return Result.Fail(ErrorsFactory.FromValidationResult(nameof(KanbanColumn), validationResult));
        }

        var column = await _columnsRepository.GetOneAsync(x => x.Id == request.Id, cancellationToken);

        if (column is null)
        {
            _logger.LogWarning("Колонка с идентификатором {Id} не найдена", request.Id);
            return Result.Fail(ErrorsFactory.NotFound(nameof(KanbanColumn), request.Id));
        }

        _logger.LogInformation("Колонка с идентификатором {Id} успешно получена", request.Id);
        return Result.Ok(_mapper.MapToModel(column));
    }
}