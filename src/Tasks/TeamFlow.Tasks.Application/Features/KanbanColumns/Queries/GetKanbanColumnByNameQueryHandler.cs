using FluentResults;
using FluentValidation;
using MediatR;
using Microsoft.Extensions.Logging;
using TeamFlow.Shared.Contracts.Mapping;
using TeamFlow.Tasks.Application.Contracts.Contracts.Features.KanbanColumn.Queries;
using TeamFlow.Tasks.Application.Contracts.Contracts.ViewModels;
using TeamFlow.Tasks.Application.Errors;
using TeamFlow.Tasks.Application.Extensions;
using TeamFlow.Tasks.Core.Abstractions.Repositories;
using TeamFlow.Tasks.Core.Entities;

namespace TeamFlow.Tasks.Application.Features.KanbanColumns.Queries;

public sealed class GetKanbanColumnByNameQueryHandler : IRequestHandler<GetKanbanColumnByNameQuery, Result<KanbanColumnViewModel>>
{
    private readonly IRepository<KanbanColumn, Guid> _columnsRepository;
    private readonly ILogger<GetKanbanColumnByNameQueryHandler> _logger;
    private readonly IValidator<GetKanbanColumnByNameQuery> _validator;
    private readonly IMapper<KanbanColumnViewModel, KanbanColumn> _mapper;

    public GetKanbanColumnByNameQueryHandler(
        IRepository<KanbanColumn, Guid> columnsRepository, 
        ILogger<GetKanbanColumnByNameQueryHandler> logger, 
        IValidator<GetKanbanColumnByNameQuery> validator, 
        IMapper<KanbanColumnViewModel, KanbanColumn> mapper)
    {
        _columnsRepository = columnsRepository;
        _logger = logger;
        _validator = validator;
        _mapper = mapper;
    }

    public async Task<Result<KanbanColumnViewModel>> Handle(GetKanbanColumnByNameQuery request, CancellationToken cancellationToken)
    {
        _logger.LogInformation("Получение колонки по имени: {Name}", request.Name);

        var validationResult = await _validator.ValidateAsync(request, cancellationToken);
        if (!validationResult.IsValid)
        {
            _logger.LogValidationErrors(validationResult.Errors, "получении колонки по имени");
            return Result.Fail(ErrorsFactory.FromValidationResult("KanbanColumn", validationResult));
        }

        var column = await _columnsRepository.GetOneAsync(x => x.Name == request.Name, cancellationToken);

        if (column is null)
        {
            _logger.LogWarning("Колонка с именем {Name} не найдена", request.Name);
            return Result.Fail(ErrorsFactory.NotFound("KanbanColumn", $"Колонка с именем {request.Name} не найдена"));
        }

        _logger.LogInformation("Колонка с именем {Name} успешно получена", request.Name);
        return Result.Ok(_mapper.MapToModel(column));
    }
}