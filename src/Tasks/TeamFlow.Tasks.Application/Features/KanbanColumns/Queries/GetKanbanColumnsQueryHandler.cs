using FluentResults;
using MediatR;
using Microsoft.Extensions.Logging;
using TeamFlow.Shared.Contracts.Mapping;
using TeamFlow.Shared.Contracts.Pagination;
using TeamFlow.Shared.Contracts.Sorting;
using TeamFlow.Tasks.Application.Contracts.Contracts.Features.KanbanColumn.Queries;
using TeamFlow.Tasks.Application.Contracts.Contracts.ViewModels;
using TeamFlow.Tasks.Application.Errors;
using TeamFlow.Tasks.Core.Abstractions.Repositories;
using TeamFlow.Tasks.Core.Entities;

namespace TeamFlow.Tasks.Application.Features.KanbanColumns.Queries;

public sealed class
    GetKanbanColumnsQueryHandler : IRequestHandler<GetKanbanColumnsQuery, Result<Page<KanbanColumnViewModel>>>
{
    private readonly IRepository<KanbanColumn, Guid> _columnsRepository;
    private readonly ILogger<GetKanbanColumnsQueryHandler> _logger;
    private readonly IMapper<KanbanColumnViewModel, KanbanColumn> _mapper;

    public GetKanbanColumnsQueryHandler(
        IRepository<KanbanColumn, Guid> columnsRepository,
        ILogger<GetKanbanColumnsQueryHandler> logger,
        IMapper<KanbanColumnViewModel, KanbanColumn> mapper)
    {
        _columnsRepository = columnsRepository;
        _logger = logger;
        _mapper = mapper;
    }

    public async Task<Result<Page<KanbanColumnViewModel>>> Handle(GetKanbanColumnsQuery request,
        CancellationToken cancellationToken)
    {
        _logger.LogInformation("Получение страницы досок: страница {PageNumber}, размер {PageSize}",
            request.Pageable.PageNumber, request.Pageable.PageSize);

        try
        {
            var filter = request.Filter?.ToPredicate();

            var sort = request.Sorting is not null
                ? request.Sorting.ToOrderByExpression()
                : new OrderByExpression<KanbanColumn>(t => t.CreatedAt);

            var pagedResult = await _columnsRepository.GetManyAsync(request.Pageable, filter, sort, cancellationToken);

            if (pagedResult.Elements is null)
            {
                _logger.LogError("Репозиторий вернул null для Elements");
                return Result.Fail(ErrorsFactory.InternalServerError("Ошибка при получении данных"));
            }

            var columns = _mapper.MapToModel(pagedResult.Elements);

            if (columns.Count == 0)
            {
                _logger.LogWarning("Доски не найдены на странице {PageNumber}", request.Pageable.PageNumber);
                return Result.Fail(ErrorsFactory.NotFound(nameof(KanbanColumn), "Доски не найдены"));
            }

            _logger.LogInformation("Успешно получено {Count} досок на странице {PageNumber}",
                columns.Count, request.Pageable.PageNumber);

            return Result.Ok(new Page<KanbanColumnViewModel>(columns, pagedResult.Count, request.Pageable));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Ошибка при получении страницы досок");
            throw;
        }
    }
}