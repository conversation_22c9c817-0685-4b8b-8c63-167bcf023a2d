using TeamFlow.Tasks.Core.Shared.Exceptions;

namespace TeamFlow.Tasks.Application.Extensions;

public static class GuidExtensions
{
    /// <summary>
    /// Validates that the provided <see cref="Guid"/> is not empty.
    /// </summary>
    /// <param name="guid">The <see cref="Guid"/> to validate.</param>
    /// <param name="message">The message to use when throwing a <see cref="BadRequestException"/>.</param>
    /// <exception cref="BadRequestException">
    /// Thrown when the provided <see cref="Guid"/> is <see cref="Guid.Empty"/>.
    /// </exception>
    /// <remarks>
    /// This method is used to validate that a <see cref="Guid"/> is not empty before using it in a database query.
    /// </remarks>
    public static void ValidateNotEmpty(this Guid guid, string message = "Id cannot be empty")
    {
        if (Guid.Empty == guid)
        {
            throw new ArgumentOutOfRangeException(message);
        }
    }
}