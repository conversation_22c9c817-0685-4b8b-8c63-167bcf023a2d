namespace TeamFlow.Tasks.Api.Api.Responses.Base;

public abstract class BaseApiResponse
{
    public bool IsSuccess { get; }

    public int StatusCode { get; }

    public string? Message { get; }

    public string? Error { get; }

    public Exception? Exception { get; }

    protected BaseApiResponse(bool isSuccess, int statusCode, string? message, string? error, Exception? exception)
    {
        IsSuccess = isSuccess;
        StatusCode = statusCode;
        Message = message;
        Error = error;
        Exception = exception;
    }
}

public class ApiResponse(bool isSuccess, int statusCode, string? message, string? error, Exception? exception)
    : BaseApiResponse(isSuccess, statusCode, message, error, exception)
{
}

public class ApiResponse<T> : BaseApiResponse
{
    public T Data { get; }

    public ApiResponse(bool isSuccess, int statusCode, string? message, string? error, Exception? exception, T data) :
        base(isSuccess, statusCode, message, error, exception)
    {
        Data = data;
    }
}