using TeamFlow.Shared.Contracts.Enums;
using TeamFlow.Tasks.Api.Api.Filtering.Common;
using TeamFlow.Tasks.Application.Contracts.Contracts.Filtering.Team;

namespace TeamFlow.Tasks.Api.Api.Filtering.Teams;

public record TeamSortingQueryParameters(
    TeamSortField SortField = TeamSortField.CreatedAt,
    SortDirection Direction = SortDirection.Descending)
    : AbstractSortingQueryParameters<TeamSortField>(<PERSON><PERSON><PERSON><PERSON>, Direction);