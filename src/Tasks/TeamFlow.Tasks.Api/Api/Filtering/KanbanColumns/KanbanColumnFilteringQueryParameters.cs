using Microsoft.AspNetCore.Mvc;

namespace TeamFlow.Tasks.Api.Api.Filtering.KanbanColumns;

public record KanbanColumnFilteringQueryParameters(
    [property: FromQuery(Name = "projectId")]
    Guid? ProjectId = null,
    [property: FromQuery(Name = "name")] string? Name = null,
    [property: FromQuery(Name = "order")] int? Order = null,
    [property: FromQuery(Name = "minOrder")]
    int? MinOrder = null,
    [property: FromQuery(Name = "maxOrder")]
    int? MaxOrder = null);