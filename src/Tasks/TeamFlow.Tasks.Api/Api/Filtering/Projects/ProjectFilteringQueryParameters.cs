using Microsoft.AspNetCore.Mvc;
using TeamFlow.Tasks.Core.Shared.Enums;

namespace TeamFlow.Tasks.Api.Api.Filtering.Projects;

public record ProjectFilteringQueryParameters(
    [property: FromQuery(Name = "name")] string? Name = null,
    [property: FromQ<PERSON>y(Name = "ownerId")]
    Guid? OwnerId = null,
    [property: FromQuery(Name = "startDate")]
    DateTime? StartDate = null,
    [property: FromQuery(Name = "endDate")]
    DateTime? EndDate = null,
    [property: FromQuery(Name = "priority")]
    PriorityLevel? Priority = null,
    [property: FromQuery(Name = "status")] ProjectStatues? Status = null);