using TeamFlow.Shared.Contracts.Enums;
using TeamFlow.Tasks.Api.Api.Filtering.Common;
using TeamFlow.Tasks.Application.Contracts.Contracts.Filtering.Project;

namespace TeamFlow.Tasks.Api.Api.Filtering.Projects;

public record ProjectSortingQueryParameters(
    ProjectSortField SortField = ProjectSortField.CreatedAt,
    SortDirection Direction = SortDirection.Descending)
    : AbstractSortingQueryParameters<ProjectSortField>(<PERSON><PERSON><PERSON><PERSON>, Direction);