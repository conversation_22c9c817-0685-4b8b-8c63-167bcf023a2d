using TeamFlow.Shared.Contracts.Enums;
using TeamFlow.Tasks.Api.Api.Filtering.Common;
using TeamFlow.Tasks.Application.Contracts.Contracts.Filtering.Attachment;

namespace TeamFlow.Tasks.Api.Api.Filtering.Attachments;

public record AttachmentSortingQueryParameters(
    AttachmentsSortField SortField = AttachmentsSortField.CreatedAt,
    SortDirection Direction = SortDirection.Descending)
    : AbstractSortingQueryParameters<AttachmentsSortField>(SortField, Direction);