using Microsoft.AspNetCore.Mvc;

namespace TeamFlow.Tasks.Api.Api.Filtering.Comments;

public record CommentFilteringQueryParameters(
    [property: FromQuery(Name = "publisherId")]
    Guid? PublisherId = null,
    [property: FromQuery(Name = "taskId")] Guid? TaskId = null,
    [property: FromQ<PERSON>y(Name = "mentionedUserId")]
    Guid? MentionedUserId = null,
    [property: FromQuery(Name = "parentCommentId")]
    Guid? ParentCommentId = null,
    [property: FromQuery(Name = "createdAtFrom")]
    DateTime? CreatedAtFrom = null,
    [property: FromQ<PERSON>y(Name = "createdAtTo")]
    DateTime? CreatedAtTo = null);