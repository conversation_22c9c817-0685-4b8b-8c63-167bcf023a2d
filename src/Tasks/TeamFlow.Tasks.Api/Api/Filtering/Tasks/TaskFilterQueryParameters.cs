using Microsoft.AspNetCore.Mvc;
using TeamFlow.Tasks.Core.Shared.Enums;

namespace TeamFlow.Tasks.Api.Api.Filtering.Tasks;

public record TaskFilterQueryParameters(
    [property: FromQuery(Name = "title")] string? Title = null,
    [property: FromQuery(Name = "projectId")] Guid? ProjectId = null,
    [property: FromQuery(Name = "assigneeId")] Guid? AssigneeId = null,
    [property: FromQuery(Name = "reporterId")] Guid? ReporterId = null,
    [property: FromQuery(Name = "kanbanColumnId")] Guid? KanbanColumnId = null,
    [property: FromQuery(Name = "dueDate")] DateTime? DueDate = null,
    [property: FromQuery(Name = "completedAt")] DateTime? CompletedAt = null,
    [property: FromQuery(Name = "estimatedTime")] TimeSpan? EstimatedTime = null,
    [property: FromQuery(Name = "actualTime")] TimeSpan? ActualTime = null,
    [property: FromQuery(Name = "status")] TaskStatuses? Status = null,
    [property: FromQuery(Name = "type")] TaskType? Type = null,
    [property: FromQuery(Name = "priorityLevel")] PriorityLevel? PriorityLevel = null,
    [property: FromQuery(Name = "storyPoints")] decimal? StoryPoints = null,
    [property: FromQuery(Name = "order")] int? Order = null,
    [property: FromQuery(Name = "tag")] string? Tag = null);