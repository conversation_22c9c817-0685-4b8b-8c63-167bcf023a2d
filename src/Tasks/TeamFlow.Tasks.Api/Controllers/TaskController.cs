using FluentResults.Extensions.AspNetCore;
using MediatR;
using Microsoft.AspNetCore.Mvc;
using TeamFlow.Tasks.Core.Shared.Enums;
using TeamFlow.Shared.Contracts.Pagination;
using TeamFlow.Tasks.Api.Api.Filtering.Tasks;
using TeamFlow.Tasks.Api.Api.Requests.Tasks;
using TeamFlow.Tasks.Application.Contracts.Contracts.Features.Task.Commands;
using TeamFlow.Tasks.Application.Contracts.Contracts.Features.Task.Queries;
using TeamFlow.Tasks.Application.Contracts.Contracts.Filtering.Task;

namespace TeamFlow.Tasks.Api.Controllers;

// [Authorize]
[Route("api/[controller]")]
[ApiController]
public class TaskController : ControllerBase
{
    private readonly ISender _sender;

    public TaskController(ISender sender)
    {
        ArgumentNullException.ThrowIfNull(sender, nameof(sender));
        _sender = sender;
    }

    [HttpGet]
    public async Task<IActionResult> GetManyAsync(
        [FromQuery] TaskFilterQueryParameters filterParameters,
        [FromQuery] TaskSortingQueryParameters sortingParameters,
        [FromQuery] string? searchTerm = null,
        [FromQuery] int pageNumber = 1,
        [FromQuery] int pageSize = 10,
        CancellationToken cancellationToken = default)
    {
        var pageable = new Pageable(pageNumber, pageSize);
        var filterCriteria = new TaskFilterCriteria(filterParameters.Title, filterParameters.ProjectId,
            filterParameters.AssigneeId, filterParameters.ReporterId, filterParameters.KanbanColumnId,
            filterParameters.DueDate,
            filterParameters.CompletedAt, filterParameters.EstimatedTime, filterParameters.ActualTime,
            filterParameters.Status, filterParameters.Type, filterParameters.PriorityLevel,
            filterParameters.StoryPoints, filterParameters.Order, filterParameters.Tag,
            searchTerm);
        var sortingCriteria =
            new TaskSortingCriteria(sortingParameters.SortField, sortingParameters.Direction);

        var tasks = await _sender.Send(new GetTasksQuery(pageable, filterCriteria, sortingCriteria), cancellationToken);
        return tasks.ToActionResult();
    }

    [HttpGet("{id:guid}")]
    public async Task<IActionResult> GetById(Guid id, CancellationToken cancellationToken = default)
    {
        var result = await _sender.Send(new GetTaskByIdQuery(id), cancellationToken);
        return result.ToActionResult();
    }

    [HttpPost]
    public async Task<IActionResult> Create([FromBody] CreateTaskCommand request,
        CancellationToken cancellationToken = default)
    {
        var result = await _sender.Send(request, cancellationToken);
        return result.ToActionResult();
    }

    [HttpPatch("{id:guid}")]
    public async Task<IActionResult> Update(Guid id, [FromBody] UpdateTaskRequest request,
        CancellationToken cancellationToken = default)
    {
        var result = await _sender
            .Send(new UpdateTaskCommand(
                id,
                request.Title,
                request.Description,
                request.KanbanColumnId,
                request.AssigneeId,
                request.Type,
                request.KanbanOrder,
                request.StoryPoints), cancellationToken);
        return result.ToActionResult();
    }

    [HttpDelete("{id:guid}")]
    public async Task<IActionResult> Delete(Guid id, CancellationToken cancellationToken = default)
    {
        var result = await _sender.Send(new DeleteTaskCommand(id), cancellationToken);
        return result.ToActionResult();
    }

    [HttpPost("{taskId:guid}/assign/{userId:guid}")]
    public async Task<IActionResult> AssignTask(Guid taskId, Guid userId,
        CancellationToken cancellationToken = default)
    {
        var result = await _sender.Send(new AssignTaskCommand(taskId, userId), cancellationToken);
        return result.ToActionResult();
    }

    [HttpPatch("{taskId:guid}/status")]
    public async Task<IActionResult> UpdateTaskStatus(Guid taskId, [FromQuery] TaskStatuses newStatus,
        CancellationToken cancellationToken = default)
    {
        var result =
            await _sender.Send(new UpdateTaskStatusCommand(taskId, newStatus), cancellationToken);
        return result.ToActionResult();
    }

    [HttpPatch("{taskId:guid}/priority")]
    public async Task<IActionResult> UpdateTaskPriority(Guid taskId, [FromQuery] PriorityLevel priorityLevel,
        CancellationToken cancellationToken = default)
    {
        var result = await _sender.Send(new UpdateTaskPriorityCommand(taskId, priorityLevel),
            cancellationToken);
        return result.ToActionResult();
    }

    [HttpPatch("{taskId:guid}/due-date")]
    public async Task<IActionResult> SetDueDate(Guid taskId, [FromBody] DateTime dueDate,
        CancellationToken cancellationToken = default)
    {
        var result = await _sender.Send(new SetDueDateCommand(taskId, dueDate), cancellationToken);
        return result.ToActionResult();
    }

    [HttpPatch("{taskId:guid}/estimated-time")]
    public async Task<IActionResult> UpdateEstimatedTime(Guid taskId, [FromBody] TimeSpan? estimatedTime,
        CancellationToken cancellationToken = default)
    {
        var result = await _sender.Send(new UpdateEstimatedTimeCommand(taskId, estimatedTime),
            cancellationToken);
        return result.ToActionResult();
    }

    [HttpPatch("{taskId:guid}/complete")]
    public async Task<IActionResult> MarkTaskAsComplete(Guid taskId, CancellationToken cancellationToken = default)
    {
        var result =
            await _sender.Send(new MarkTaskAsCompleteCommand(taskId), cancellationToken);
        return result.ToActionResult();
    }

    [HttpPatch("{taskId:guid}/reopen")]
    public async Task<IActionResult> ReopenTask(Guid taskId, CancellationToken cancellationToken = default)
    {
        var result = await _sender.Send(new ReopenTaskCommand(taskId), cancellationToken);
        return result.ToActionResult();
    }
    
    [HttpPost("task/{taskId:guid}/move")]
    public async Task<IActionResult> MoveTaskToColumn(
        Guid taskId, 
        [FromQuery] Guid columnId, 
        [FromQuery] int newOrder,
        CancellationToken cancellationToken = default)
    {
        var result = await _sender.Send(new MoveTaskToColumnCommand(taskId, columnId, newOrder), cancellationToken);
        return result.ToActionResult();
    }

    [HttpPost("column/{columnId:guid}/reorder")]
    public async Task<IActionResult> ReorderTaskInColumn(
        Guid columnId, 
        [FromBody] Dictionary<Guid, int> taskOrder,
        CancellationToken cancellationToken = default)
    {
        var result = await _sender.Send(new ReorderTaskInColumnCommand(columnId, taskOrder), cancellationToken);
        return result.ToActionResult();
    }
    
    [HttpPost("task/{taskId:guid}/tag/{tag}")]
    public async Task<IActionResult> AddTag(Guid taskId, string tag, CancellationToken cancellationToken = default)
    {
        var result = await _sender.Send(new AddTagCommand(taskId, tag), cancellationToken);
        return result.ToActionResult();
    }

    [HttpDelete("task/{taskId:guid}/tag/{tag}")]
    public async Task<IActionResult> RemoveTag(Guid taskId, string tag, CancellationToken cancellationToken = default)
    {
        var result = await _sender.Send(new RemoveTagCommand(taskId, tag), cancellationToken);
        return result.ToActionResult();
    }

    [HttpDelete("task/{taskId:guid}/tags")]
    public async Task<IActionResult> ClearTags(Guid taskId, CancellationToken cancellationToken = default)
    {
        var result = await _sender.Send(new ClearTagsCommand(taskId), cancellationToken);
        return result.ToActionResult();
    }
}