using FluentResults.Extensions.AspNetCore;
using MediatR;
using Microsoft.AspNetCore.Mvc;
using TeamFlow.Shared.Contracts.Pagination;
using TeamFlow.Tasks.Api.Api.Filtering.Projects;
using TeamFlow.Tasks.Api.Api.Requests.Projects;
using TeamFlow.Tasks.Application.Contracts.Contracts.Features.Projects.Commands;
using TeamFlow.Tasks.Application.Contracts.Contracts.Features.Projects.Queries;
using TeamFlow.Tasks.Application.Contracts.Contracts.Filtering.Project;

namespace TeamFlow.Tasks.Api.Controllers;

// [Authorize]
[Route("api/[controller]")]
[ApiController]
public class ProjectController : ControllerBase
{
    private readonly ISender _sender;

    public ProjectController(ISender sender)
    {
        ArgumentNullException.ThrowIfNull(sender, nameof(sender));
        _sender = sender;
    }


    [HttpGet("{id:guid}")]
    public async Task<IActionResult> GetById(Guid id, CancellationToken cancellationToken = default)
    {
        var result = await _sender.Send(new GetProjectByIdQuery(id), cancellationToken);
        return result.ToActionResult();
    }

    [HttpGet("name/{name}")]
    public async Task<IActionResult> GetByName(string name, CancellationToken cancellationToken = default)
    {
        var result = await _sender.Send(new GetProjectByNameQuery(name), cancellationToken);
        return result.ToActionResult();
    }

    [HttpGet]
    public async Task<IActionResult> GetManyAsync(
        [FromQuery] ProjectFilteringQueryParameters filterParameters,
        [FromQuery] ProjectSortingQueryParameters sortingParameters,
        [FromQuery] string? searchTerm = null,
        [FromQuery] int pageNumber = 1,
        [FromQuery] int pageSize = 10,
        CancellationToken cancellationToken = default)
    {
        var pageable = new Pageable(pageNumber, pageSize);
        var filterCriteria = new ProjectFilterCriteria(filterParameters.Name, filterParameters.OwnerId,
            filterParameters.StartDate, filterParameters.EndDate, filterParameters.Priority, filterParameters.Status,
            searchTerm);
        var sortingCriteria = new ProjectSortingCriteria(sortingParameters.SortField, sortingParameters.Direction);

        var columns =
            await _sender.Send(new GetProjectsQuery(pageable, filterCriteria, sortingCriteria), cancellationToken);

        return columns.ToActionResult();
    }


    [HttpPost]
    public async Task<IActionResult> Create([FromBody] CreateProjectCommand request,
        CancellationToken cancellationToken = default)
    {
        var result = await _sender.Send(request, cancellationToken);
        return result.ToActionResult();
    }

    [HttpPatch("{id:guid}")]
    public async Task<IActionResult> Update(Guid id, [FromBody] UpdateProjectRequest request,
        CancellationToken cancellationToken = default)
    {
        var result = await _sender.Send(new UpdateProjectCommand(
            id, request.Name,
            request.Description,
            request.OwnerId,
            request.StartDate,
            request.EndDate,
            request.PriorityLevel,
            request.Status), cancellationToken);
        return result.ToActionResult();
    }

    [HttpDelete("{id:guid}")]
    public async Task<IActionResult> Delete(Guid id, CancellationToken cancellationToken = default)
    {
        var result = await _sender.Send(new DeleteProjectCommand(id), cancellationToken);
        return result.ToActionResult();
    }

    [HttpPost("{projectId:guid}/team/{teamId:guid}")]
    public async Task<IActionResult> AddTeamToProject(Guid projectId, Guid teamId,
        CancellationToken cancellationToken = default)
    {
        var result = await _sender
            .Send(new AddTeamToProjectCommand(projectId, teamId), cancellationToken);
        return result.ToActionResult();
    }
}