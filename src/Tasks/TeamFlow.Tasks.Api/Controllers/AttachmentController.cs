using FluentResults.Extensions.AspNetCore;
using MediatR;
using Microsoft.AspNetCore.Mvc;
using TeamFlow.Shared.Contracts.Pagination;
using TeamFlow.Tasks.Api.Api.Filtering.Attachments;
using TeamFlow.Tasks.Api.Api.Requests.Attachments;
using TeamFlow.Tasks.Application.Contracts.Contracts.Features.Attachment.Commands;
using TeamFlow.Tasks.Application.Contracts.Contracts.Features.Attachment.Queries;
using TeamFlow.Tasks.Application.Contracts.Contracts.Filtering.Attachment;

namespace TeamFlow.Tasks.Api.Controllers;

// [Authorize]
[Route("api/[controller]")]
[ApiController]
public class AttachmentController : ControllerBase
{
    private readonly ISender _sender;

    public AttachmentController(ISender sender)
    {
        _sender = sender;
    }

    [HttpGet("{id:guid}")]
    public async Task<IActionResult> GetById(Guid id, CancellationToken cancellationToken = default)
    {
        var result = await _sender.Send(new GetAttachmentByIdQuery(id), cancellationToken);
        return result.ToActionResult();
    }

    [HttpGet("file-name/{fileName}")]
    public async Task<IActionResult> GetByFileName(string fileName, CancellationToken cancellationToken = default)
    {
        var result = await _sender.Send(new GetAttachmentByFileNameQuery(fileName), cancellationToken);
        return result.ToActionResult();
    }

    [HttpGet]
    public async Task<IActionResult> GetManyAsync(
        [FromQuery] AttachmentFilteringQueryParameters filterParameters,
        [FromQuery] AttachmentSortingQueryParameters sortingParameters,
        [FromQuery] string? searchTerm = null,
        [FromQuery] int pageNumber = 1,
        [FromQuery] int pageSize = 10,
        CancellationToken cancellationToken = default)
    {
        var pageable = new Pageable(pageNumber, pageSize);
        var filterCriteria = new AttachmentFilterCriteria(filterParameters.TaskId, filterParameters.UploaderId,
            filterParameters.FileName, filterParameters.OriginalFileName, filterParameters.ContentType,
            filterParameters.FileSize, searchTerm);
        var sortingCriteria = new AttachmentSortingCriteria(sortingParameters.SortField, sortingParameters.Direction);

        var result = await _sender.Send(new GetAttachmentsQuery(pageable, filterCriteria, sortingCriteria), cancellationToken);
        return result.ToActionResult();
    }

    [HttpPost]
    public async Task<IActionResult> Create(
        [FromForm] Guid taskId,
        [FromForm] Guid uploaderId,
        IFormFile file,
        CancellationToken cancellationToken = default)
    {
        if (file.Length == 0)
        {
            return BadRequest("Файл не был предоставлен или пуст");
        }

        await using var stream = file.OpenReadStream();
        var createRequest = new CreateAttachmentCommand(
            taskId,
            uploaderId,
            file.FileName,
            stream);

        var result = await _sender.Send(createRequest, cancellationToken);
        return result.ToActionResult();
    }

    [HttpPatch("{id:guid}")]
    public async Task<IActionResult> Update(Guid id, [FromBody] UpdateAttachmentRequest request,
        CancellationToken cancellationToken = default)
    {
        var result = await _sender.Send(new UpdateAttachmentCommand(id, request.ContentType), cancellationToken);
        return result.ToActionResult();
    }

    [HttpDelete("{id:guid}")]
    public async Task<IActionResult> Delete(Guid id, CancellationToken cancellationToken = default)
    {
        var result = await _sender.Send(new DeleteAttachmentCommand(id), cancellationToken);
        return result.ToActionResult();
    }
}