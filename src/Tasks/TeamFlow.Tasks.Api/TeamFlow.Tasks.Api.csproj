<Project Sdk="Microsoft.NET.Sdk.Web">

    <PropertyGroup>
        <TargetFramework>net9.0</TargetFramework>
        <Nullable>enable</Nullable>
        <ImplicitUsings>enable</ImplicitUsings>
        <DockerDefaultTargetOS>Linux</DockerDefaultTargetOS>
    </PropertyGroup>

    <ItemGroup>
        <PackageReference Include="Elastic.Serilog.Sinks" Version="8.12.3" />
        <PackageReference Include="FluentResults.Extensions.AspNetCore" Version="0.1.0" />
        <PackageReference Include="FluentValidation.AspNetCore" Version="11.3.0" />
        <PackageReference Include="LazyCache.AspNetCore" Version="2.4.0" />
        <PackageReference Include="MediatR.Extensions.FluentValidation.AspNetCore" Version="6.0.0" />
        <PackageReference Include="Microsoft.AspNetCore.Authentication.JwtBearer" Version="9.0.4" />
        <PackageReference Include="Microsoft.AspNetCore.OpenApi" Version="9.0.3"/>
        <PackageReference Include="Microsoft.VisualStudio.Web.CodeGeneration.Design" Version="9.0.0" />
        <PackageReference Include="Serilog" Version="4.2.0" />
        <PackageReference Include="Serilog.AspNetCore" Version="9.0.0" />
        <PackageReference Include="Swashbuckle.AspNetCore.Swagger" Version="8.1.1" />
        <PackageReference Include="Swashbuckle.AspNetCore.SwaggerGen" Version="8.1.1" />
        <PackageReference Include="Swashbuckle.AspNetCore.SwaggerUI" Version="8.1.1" />
    </ItemGroup>

    <ItemGroup>
      <Content Include="..\.dockerignore">
        <Link>.dockerignore</Link>
      </Content>
    </ItemGroup>

    <ItemGroup>
      <ProjectReference Include="..\TeamFlow.Tasks.Infrastructure\TeamFlow.Tasks.Infrastructure.csproj" />
    </ItemGroup>

</Project>
