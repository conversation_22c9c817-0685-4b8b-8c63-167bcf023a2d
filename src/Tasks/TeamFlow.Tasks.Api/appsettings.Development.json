{"Serilog": {"MinimumLevel": {"Default": "Information", "Override": {"Microsoft": "Information", "System": "Warning"}}}, "Elastic": {"Uri": "http://localhost:9200", "Username": "elastic", "Password": "oA1psDf772laPqie", "DataStream": {"Namespace": "logs", "Dataset": "teamflow", "Type": "api"}}, "Auth": {"Issuer": "TeamFlow-Project", "Audience": "TeamFlow-Project", "Key": "29FC9B204F81C6C39498207355946253CF2C5D78"}, "Database": {"ConnectionString": "**************************************************************************************", "DatabaseName": "team-flow-tasks"}, "ExternalServices": {"TeamFlowId": {"Uri": "http://localhost:5292"}}, "FileService": {"BaseStoragePath": "./Uploads"}, "Caching": {"Memory": {"SizeLimit": 524288000, "ExpirationScanFrequency": 300, "CompactionPercentage": 0.2}, "TwoLevelCache": {"DefaultLocalExpirationTime": 300, "DefaultDistributedExpirationTime": 3600, "PopulateLocalCacheOnDistributedHit": true, "HotDataAccessThreshold": 3, "AccessTrackingIntervalSeconds": 60}}}