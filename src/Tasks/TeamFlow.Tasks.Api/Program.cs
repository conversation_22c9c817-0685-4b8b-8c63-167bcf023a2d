using System.Reflection;
using System.Threading.Channels;
using Microsoft.Extensions.Options;
using MongoDB.Bson;
using MongoDB.Bson.Serialization;
using MongoDB.Bson.Serialization.Serializers;
using TeamFlow.Tasks.Api.Extensions.Di;
using TeamFlow.Tasks.Api.Settings;
using TeamFlow.Tasks.Api.Utils;
using TeamFlow.Tasks.Application.Contracts.Internal;
using TeamFlow.Tasks.Application.Internal;
using TeamFlow.Tasks.Application.Settings;
using TeamFlow.Tasks.Core.Utils;

//TODO Улучшить сообщения логов 
var builder = WebApplication.CreateBuilder(args);

builder.Host.UseElasticsearchSerilog();

// Add services to the container.

builder.Services.AddControllers();

// options.Conventions.Add(new RouteTokenTransformerConvention(new CamelCaseParameterTransformer()))

// builder.Services.Configure<JsonOptions>(options => 
// {
// options.SerializerOptions.PropertyNamingPolicy = JsonNamingPolicy.CamelCase;
// });

// Learn more about configuring OpenAPI at https://aka.ms/aspnet/openapi
builder.Services.AddOpenApi();

var configuration = builder.Configuration;

// builder.Services.AddSingleton(new AuthOptions
// {
//     Issuer = configuration["Auth:Issuer"],
//     Audience = configuration["Auth:Audience"],
//     Key = configuration["Auth:Key"],
// });

builder.Services.AddJwtAuthentication(configuration);
builder.Services.AddSwaggerDocumentation();
builder.Services.AddMongoDb(configuration);

BsonSerializer.RegisterSerializer(new GuidSerializer(GuidRepresentation.Standard));
builder.Services.AddSingleton(Channel.CreateUnbounded<string>(new UnboundedChannelOptions
{
    SingleReader = true,
    SingleWriter = false
}));

builder.Services.AddSingleton<IFileTypeProvider, FileTypeProvider>();
builder.Services.AddSingleton<IFileServiceSettings>(sp => sp.GetRequiredService<IOptions<FileServiceSettings>>().Value);
builder.Services.AddMappers();
builder.Services.AddMongoRepositories();
builder.Services.AddGrpcUserClient(configuration);
builder.Services.AddValidators();
builder.Services.AddInfrastructureServices();
builder.Services.AddMediatR(cfg =>
{
    cfg.RegisterServicesFromAssemblies(
        Assembly.GetAssembly(typeof(IApplicationAssemblyMarker))!,
        Assembly.GetAssembly(typeof(IApplicationContractsAssemblyMarker))!);
});
builder.Services.AddHealthChecks();

var app = builder.Build();

//TODO Завести сортировку, которую можно прокидывать в сервисы. На уровне репозитория все готово.

// Configure the HTTP request pipeline.
if (app.Environment.IsDevelopment())
{
    // app.MapOpenApi();
    app.UseSwagger();
    app.UseSwaggerUI();
}

// app.MapGet("/health", () => "healthy");
app.MapHealthChecks("/healthz");

app.UseHttpsRedirection();

app.UseAuthorization();

app.MapControllers();

app.Run();