{"Serilog": {"MinimumLevel": {"Default": "Information", "Override": {"Microsoft": "Information", "System": "Warning"}}}, "Elastic": {"Uri": "http://localhost:9200", "Username": "elastic", "Password": "oA1psDf772laPqie", "DataStream": {"Namespace": "logs", "Dataset": "teamflow-task", "Type": "api"}}, "Auth": {"Issuer": "TeamFlow-Project", "Audience": "TeamFlow-Project", "Key": "29FC9B204F81C6C39498207355946253CF2C5D78"}, "Database": {"ConnectionString": "**************************************************************************************", "DatabaseName": "team-flow-tasks"}, "ExternalServices": {"TeamFlowId": {"Uri": "http://localhost:5292"}}, "FileService": {"BaseStoragePath": "./Uploads"}, "AllowedHosts": "*"}