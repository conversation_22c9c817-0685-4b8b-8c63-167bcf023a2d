using System.Reflection;
using TeamFlow.Shared.Contracts.Mapping;
using TeamFlow.Tasks.Infrastructure.Internal;
using TeamFlow.Tasks.Infrastructure.Mappers.Attachments;
using TeamFlow.Tasks.Infrastructure.Mappers.Comments;
using TeamFlow.Tasks.Infrastructure.Mappers.KanbanColumns;
using TeamFlow.Tasks.Infrastructure.Mappers.Projects;
using TeamFlow.Tasks.Infrastructure.Mappers.Tasks;
using TeamFlow.Tasks.Infrastructure.Mappers.Teams;
using TeamFlow.Tasks.Infrastructure.Mappers.Users;

namespace TeamFlow.Tasks.Api.Extensions.Di;

public static class MappingServiceCollectionExtensions
{
    public static IServiceCollection AddMappers(this IServiceCollection services)
    {
        services.AddScoped<AttachmentMappingProfile>();
        services.AddScoped<CommentMappingProfile>();
        services.AddScoped<KanbanColumnMappingProfile>();
        services.AddScoped<ProjectMappingProfile>();
        services.AddScoped<TaskMappingProfile>();
        services.AddScoped<TeamMappingProfile>();
        services.AddScoped<UserMappingProfile>();

        RegisterMappers(services);

        return services;
    }

    private static void RegisterMappers(IServiceCollection services)
    {
        var assembly = Assembly.GetAssembly(typeof(IInfrastructureAssemblyMarker));

        ArgumentNullException.ThrowIfNull(assembly, nameof(assembly));

        var mapperTypes = assembly.GetTypes()
            .Where(t => t is { IsAbstract: false, IsInterface: false })
            .SelectMany(t => t.GetInterfaces()
                .Where(i => i.IsGenericType && i.GetGenericTypeDefinition() == typeof(IMapper<,>))
                .Select(i => new { InterfaceType = i, ImplementationType = t }))
            .ToList();
        
        foreach (var mapper in mapperTypes)
        {
            services.AddScoped(mapper.InterfaceType, mapper.ImplementationType);
        }
    }
}