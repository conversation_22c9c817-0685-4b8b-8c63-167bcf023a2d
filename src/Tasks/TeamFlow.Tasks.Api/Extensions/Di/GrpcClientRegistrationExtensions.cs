using TeamFlow.Tasks.Core.Abstractions.Services.Infrastructure;
using TeamFlow.Tasks.Infrastructure.Grpc;
using VanId;

namespace TeamFlow.Tasks.Api.Extensions.Di;

public static class GrpcClientRegistrationExtensions
{
    public static IServiceCollection AddGrpcUserClient(this IServiceCollection services, IConfiguration configuration)
    {
        var identityServiceUrl = configuration["ExternalServices:TeamFlowId:Uri"];

        ArgumentNullException.ThrowIfNull(identityServiceUrl);
        
        services.AddGrpcClient<GrpcUserService.GrpcUserServiceClient>(options =>
        {
            options.Address = new Uri(identityServiceUrl);
        });
        
        services.AddScoped<IExternalUserService, GrpcClientAdapter>();
        
        return services;
    }
}