using System.Reflection;
using MongoDB.Driver;
using TeamFlow.Shared.Repositories.Entities;
using TeamFlow.Tasks.Core.Abstractions.Repositories;
using TeamFlow.Tasks.Core.Entities;
using TeamFlow.Tasks.Core.Internal;
using TeamFlow.Tasks.Infrastructure.Data;

namespace TeamFlow.Tasks.Api.Extensions.Di;

public static class RepositoryRegistrationExtensions
{
    // Существующая реализация с использованием рефлексии
    public static IServiceCollection AddRepositories(this IServiceCollection services)
    {
        var entityType = typeof(IEntity<Guid>);
        var repositoryInterfaceType = typeof(IRepository<,>);
        var mongoRepositoryType = typeof(GenericMongoRepository<,>);
    
        var coreAssembly = Assembly.GetAssembly(typeof(ICoreAssemblyMarker));
        // var infrastructureAssembly = Assembly.GetAssembly(typeof(IInfrastructureAssemblyMarker));
    
        var modelTypes = coreAssembly!.GetTypes()
            .Where(t => t.IsClass && !t.IsAbstract && entityType.IsAssignableFrom(t));
    
        foreach (var modelType in modelTypes)
        {
            var repoInterface = repositoryInterfaceType.MakeGenericType(modelType, typeof(Guid));
            var repoImplementation = mongoRepositoryType.MakeGenericType(modelType, typeof(Guid));
            services.AddScoped(repoInterface, repoImplementation);
        }
        
        return services;
    }

    // Метод для регистрации MongoDB клиента и базы данных
    public static IServiceCollection AddMongoDb(this IServiceCollection services, IConfiguration configuration)
    {
        // Регистрация MongoDB клиента
        services.AddSingleton<IMongoClient>(_ =>
        {
            // var connectionString = configuration.GetConnectionString("MongoDb");
            var connectionString = configuration["Database:ConnectionString"];
            return new MongoClient(connectionString);
        });

        // Регистрация MongoDB базы данных
        services.AddSingleton<IMongoDatabase>(sp =>
        {
            var client = sp.GetRequiredService<IMongoClient>();
            var databaseName = configuration["Database:DatabaseName"];
            return client.GetDatabase(databaseName);
        });
        
        return services;
    }

    public static IServiceCollection AddMongoRepositories(this IServiceCollection services)
    {
        // Регистрация репозиториев с явным указанием имен коллекций
        services.AddScoped<IRepository<Core.Entities.Task, Guid>>(sp =>
        {
            var database = sp.GetRequiredService<IMongoDatabase>();
            return new GenericMongoRepository<Core.Entities.Task, Guid>(database, "Tasks");
        });
            
        services.AddScoped<IRepository<Team, Guid>>(sp =>
        {
            var database = sp.GetRequiredService<IMongoDatabase>();
            return new GenericMongoRepository<Team, Guid>(database, "Teams");
        });
            
        services.AddScoped<IRepository<Project, Guid>>(sp =>
        {
            var database = sp.GetRequiredService<IMongoDatabase>();
            return new GenericMongoRepository<Project, Guid>(database, "Projects");
        });
            
        services.AddScoped<IRepository<Comment, Guid>>(sp =>
        {
            var database = sp.GetRequiredService<IMongoDatabase>();
            return new GenericMongoRepository<Comment, Guid>(database, "Comments");
        });
            
        services.AddScoped<IRepository<Attachment, Guid>>(sp =>
        {
            var database = sp.GetRequiredService<IMongoDatabase>();
            return new GenericMongoRepository<Attachment, Guid>(database, "Attachments");
        });
            
        services.AddScoped<IRepository<KanbanColumn, Guid>>(sp =>
        {
            var database = sp.GetRequiredService<IMongoDatabase>();
            return new GenericMongoRepository<KanbanColumn, Guid>(database, "KanbanColumns");
        });

        return services;
    }
}