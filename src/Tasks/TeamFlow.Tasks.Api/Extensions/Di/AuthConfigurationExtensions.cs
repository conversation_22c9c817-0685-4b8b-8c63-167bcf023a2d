using Microsoft.IdentityModel.Tokens;
using TeamFlow.Tasks.Api.Auth;

namespace TeamFlow.Tasks.Api.Extensions.Di;

public static class AuthConfigurationExtensions
{
    public static TokenValidationParameters GetTokenValidationParameters(this AuthConfiguration configuration)
    {
        ArgumentNullException.ThrowIfNull(configuration);

        return new TokenValidationParameters
        {
            ValidateIssuer = true,
            ValidIssuer = configuration.Issuer,
            ValidateAudience = true,
            ValidAudience = configuration.Audience,
            ValidateLifetime = true,
            IssuerSigningKey = configuration.GetSymmetricSecurityKey(),
            ValidateIssuerSigningKey = true,
            ClockSkew = TimeSpan.Zero
        };
    }
}