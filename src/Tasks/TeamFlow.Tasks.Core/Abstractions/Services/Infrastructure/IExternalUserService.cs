using FluentResults;
using TeamFlow.Tasks.Core.Contracts.External;

namespace TeamFlow.Tasks.Core.Abstractions.Services.Infrastructure;

public interface IExternalUserService
{
    Task<Result<UserDto>> GetByGuidAsync(Guid id, CancellationToken cancellationToken = default);

    Task<Result<UserDto>> GetByLoginAsync(string login, CancellationToken cancellationToken = default);

    Task<Result<UserDto>> GetByEmailAsync(string email, CancellationToken cancellationToken = default);

    Task<Result<UserDtoPaged>> GetPagedAsync(int pageNumber, int pageSize,
        CancellationToken cancellationToken = default);
}