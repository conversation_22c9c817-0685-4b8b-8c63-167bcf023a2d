
using TeamFlow.Tasks.Core.Contracts;

namespace TeamFlow.Tasks.Core.Abstractions.Services.Infrastructure;

public interface IFileService
{
    /// <summary>
    /// Сохраняет файл с генерацией уникального имени
    /// </summary>
    /// <param name="fileStream">Поток данных файла</param>
    /// <param name="originalFileName">Оригинальное имя файла</param>
    /// <param name="directoryPath">Опциональный путь к директории (если null, используется путь по умолчанию)</param>
    /// <param name="cancellationToken">Токен отмены операции</param>
    /// <returns>Информация о сохраненном файле</returns>
    Task<FileInfoResponse> SaveFileAsync(Stream fileStream, string originalFileName, string? directoryPath = null, CancellationToken cancellationToken = default);

    /// <summary>
    /// Удаляет файл по имени
    /// </summary>
    /// <param name="fileName">Имя файла для удаления</param>
    /// <param name="cancellationToken">Токен отмены операции</param>
    /// <returns>True, если файл успешно удален</returns>
    Task<bool> DeleteFileAsync(string fileName, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// Получает полный путь к файлу по его имени
    /// </summary>
    /// <param name="fileName">Имя файла</param>
    /// <returns>Полный путь к файлу</returns>
    string GetFilePath(string fileName);
}