using TeamFlow.Shared.Repositories.Entities;
using TeamFlow.Shared.Repositories.Repositories.Common;

namespace TeamFlow.Tasks.Core.Abstractions.Repositories;

public interface IRepository<TEntity, in TKey> : IGetOne<TEntity>, IGetMany<TEntity>, IAdd<TEntity>, IUpdate<TEntity>,
    IRemove<TEntity>, IFindAny<TEntity>, IBulkOperations<TEntity, TKey>
    where TEntity : class, IEntity<TKey> where TK<PERSON> : IEquatable<TKey>;