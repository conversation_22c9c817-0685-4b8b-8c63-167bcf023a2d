namespace TeamFlow.Tasks.Core.Abstractions.Repositories;

public interface IUnitOfWork : IDisposable
{
    System.Threading.Tasks.Task BeginTransactionAsync(CancellationToken cancellationToken = default);
    
    System.Threading.Tasks.Task CommitAsync(CancellationToken cancellationToken = default);

    System.Threading.Tasks.Task RollbackAsync(CancellationToken cancellationToken = default);
    
    // Добавляем метод для получения текущей сессии
    object? GetCurrentSession();
    
    // Добавляем флаг, указывающий, активна ли транзакция
    bool HasActiveTransaction { get; }
}