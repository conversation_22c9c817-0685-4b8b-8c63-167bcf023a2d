using TeamFlow.Shared.Repositories.Entities;
using TeamFlow.Tasks.Core.Shared.Enums;

namespace TeamFlow.Tasks.Core.Entities;

public class Task : Entity<Guid>
{
    public required string Title { get; set; }

    public string? Description { get; set; } = string.Empty;
    
    public required Guid ProjectId { get; set; }
    
    public Guid? AssignedId { get; set; }
    
    public required Guid ReporterId { get; set; }
    
    // Срок выполнения задачи
    public DateTime? DueDate { get; set; } = null; 

    // Дата завершения задачи
    public DateTime? CompletedAt { get; set; } = null;
    
    public TaskStatuses Status { get; set; } = TaskStatuses.Open;
    
    public PriorityLevel Priority { get; set; } = PriorityLevel.Medium;

    public TaskType Type { get; set; } = TaskType.Feature;
    
    /*
     * TODO
     * Рассчитывать время в часах?
     */
    public TimeSpan? EstimatedTime { get; set; } = TimeSpan.Zero;

    public Dictionary<TaskStatuses, TimeSpan> TimeInStatus { get; set; } = new();

    public DateTime StatusChangedAt { get; set; } = DateTime.UtcNow;

    public TimeSpan? ActualTime { get; set; } = TimeSpan.Zero;
    
    public decimal? StoryPoints { get; set; } = 0;
    
    public List<string>? Tags { get; set; } = [];

    public Guid? KanbanColumnId { get; set; } = null;
    
    // Порядок задачи на доске
    public int? KanbanOrder { get; set; } = 0;
}