using System.Net;
using FluentResults;

namespace TeamFlow.Tasks.Core.Shared.Errors;

public abstract class AbstractError : Error
{
    public abstract HttpStatusCode StatusCode { get; }
    
    protected AbstractError(string message) : base(message)
    {
        Metadata.Add("ErrorType", GetType().Name);
    }

    // protected virtual string GetErrorType()
    // {
        // return GetType().Name;
    // }
}