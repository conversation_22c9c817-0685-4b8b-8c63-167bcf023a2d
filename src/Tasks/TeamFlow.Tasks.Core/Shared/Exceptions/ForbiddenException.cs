using System.Net;
using TeamFlow.Tasks.Core.Shared.Exceptions.Base;

namespace TeamFlow.Tasks.Core.Shared.Exceptions;

public class ForbiddenException : HttpException
{
    public ForbiddenException() : base(HttpStatusCode.Forbidden)
    {
    }

    public ForbiddenException(string message) : base(HttpStatusCode.Forbidden, message)
    {
    }

    public ForbiddenException(string message, Exception innerException) : base(HttpStatusCode.Forbidden, message,
        innerException)
    {
    }
}