using System.Net;
using TeamFlow.Tasks.Core.Shared.Exceptions.Base;

namespace TeamFlow.Tasks.Core.Shared.Exceptions;

public class NotFoundException : HttpException
{
    public NotFoundException() : base(HttpStatusCode.NotFound)
    {
    }

    public NotFoundException(string message) : base(HttpStatusCode.NotFound, message)
    {
    }

    public NotFoundException(string name, object key) : base(HttpStatusCode.NotFound, $"\"{name}\" {key} not found")
    {
    }
}