using System.Net;
using TeamFlow.Tasks.Core.Shared.Exceptions.Base;

namespace TeamFlow.Tasks.Core.Shared.Exceptions;

public class UnprocessableEntityException : HttpException
{
    public UnprocessableEntityException() : base(HttpStatusCode.UnprocessableEntity)
    {
    }

    public UnprocessableEntityException(string message) : base(HttpStatusCode.UnprocessableEntity, message)
    {
    }

    public UnprocessableEntityException(string message, Exception innerException) : base(HttpStatusCode.UnprocessableEntity, message, innerException)
    {
    }
}