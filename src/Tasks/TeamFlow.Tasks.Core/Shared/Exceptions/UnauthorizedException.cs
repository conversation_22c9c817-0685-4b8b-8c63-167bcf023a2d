using System.Net;
using TeamFlow.Tasks.Core.Shared.Exceptions.Base;

namespace TeamFlow.Tasks.Core.Shared.Exceptions;

public class UnauthorizedException : HttpException
{
    public UnauthorizedException() : base(HttpStatusCode.Unauthorized)
    {
    }

    public UnauthorizedException(string message) : base(HttpStatusCode.Unauthorized, message)
    {
    }

    public UnauthorizedException(string message, Exception innerException)
        : base(HttpStatusCode.Unauthorized, message, innerException)
    {
    }
}