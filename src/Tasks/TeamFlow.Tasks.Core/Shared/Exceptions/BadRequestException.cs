using System.Net;
using TeamFlow.Tasks.Core.Shared.Exceptions.Base;

namespace TeamFlow.Tasks.Core.Shared.Exceptions;

public class BadRequestException : HttpException
{
    public BadRequestException() : base(HttpStatusCode.BadRequest)
    {
    }

    public BadRequestException(string message) : base(HttpStatusCode.BadRequest, message)
    {
    }
    
    public BadRequestException(string message, Exception innerException)
        : base(HttpStatusCode.BadRequest, message, innerException)
    {
    }
}