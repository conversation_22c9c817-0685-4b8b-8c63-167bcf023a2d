using System.Linq.Expressions;
using TeamFlow.Shared.Contracts.Enums;
using TeamFlow.Shared.Contracts.Sorting;

namespace TeamFlow.Tasks.Core.Extensions;

/// <summary>
/// Класс с методами расширения для работы с сортировкой сущностей.
/// </summary>
public static class SortExpressionExtensions
{
    /// <summary>
    /// Создать <see cref="OrderByExpression{TEntity}"/> с направлением Ascending.
    /// </summary>
    /// <typeparam name="T">Тип сущности, для которой применяется сортировка.</typeparam>
    /// <param name="keySelector">Лямбда-выражение, определяющее ключ сортировки.</param>
    /// <returns>Новый экземпляр <see cref="OrderByExpression{TEntity}"/> с направлением сортировки по возрастанию.</returns>
    public static OrderByExpression<T> Asc<T>(
        this Expression<Func<T, object>> keySelector) where T : class
    {
        return new OrderByExpression<T>(keySelector, SortDirection.Ascending);
    }

    /// <summary>
    /// Создать OrderByExpression с направлением Descending.
    /// </summary>
    /// <typeparam name="T">Тип сущности, для которой применяется сортировка.</typeparam>
    /// <param name="keySelector">Лямбда-выражение, определяющее ключ сортировки.</param>
    /// <returns>Новый экземпляр <see cref="OrderByExpression{TEntity}"/> с направлением сортировки по убыванию.</returns>
    public static OrderByExpression<T> Desc<T>(
        this Expression<Func<T, object>> keySelector) where T : class
    {
        return new OrderByExpression<T>(keySelector, SortDirection.Descending);
    }

    /// <summary>
    /// Начать цепочку: первый OrderByExpression превращается в список.
    /// </summary>
    /// <typeparam name="T">Тип сущности, для которой применяется сортировка.</typeparam>
    /// <param name="first">Первое выражение сортировки.</param>
    /// <returns>Список выражений сортировки, содержащий переданное выражение.</returns>
    public static List<OrderByExpression<T>> ToSortList<T>(
        this OrderByExpression<T> first) where T : class
    {
        return [first];
    }

    /// <summary>
    /// Добавить последующую сортировку к уже существующему списку.
    /// </summary>
    /// <typeparam name="T">Тип сущности, для которой применяется сортировка.</typeparam>
    /// <param name="existing">Существующий список выражений сортировки.</param>
    /// <param name="keySelector">Лямбда-выражение, определяющее ключ сортировки.</param>
    /// <param name="direction">Направление сортировки (по умолчанию - по возрастанию).</param>
    /// <returns>Обновленный список выражений сортировки с добавленным новым выражением.</returns>
    public static List<OrderByExpression<T>> ThenBy<T>(
        this List<OrderByExpression<T>> existing,
        Expression<Func<T, object>> keySelector,
        SortDirection direction = SortDirection.Ascending) where T : class
    {
        existing.Add(new OrderByExpression<T>(keySelector, direction));
        return existing;
    }

    /// <summary>
    /// Сокращённая версия ThenBy с направлением Ascending.
    /// </summary>
    /// <typeparam name="T">Тип сущности, для которой применяется сортировка.</typeparam>
    /// <param name="existing">Существующий список выражений сортировки.</param>
    /// <param name="keySelector">Лямбда-выражение, определяющее ключ сортировки.</param>
    /// <returns>Обновленный список выражений сортировки с добавленным новым выражением по возрастанию.</returns>
    public static List<OrderByExpression<T>> ThenAsc<T>(
        this List<OrderByExpression<T>> existing,
        Expression<Func<T, object>> keySelector) where T : class => existing.ThenBy(keySelector);

    /// <summary>
    /// Сокращённая версия ThenBy с направлением Descending.
    /// </summary>
    /// <typeparam name="T">Тип сущности, для которой применяется сортировка.</typeparam>
    /// <param name="existing">Существующий список выражений сортировки.</param>
    /// <param name="keySelector">Лямбда-выражение, определяющее ключ сортировки.</param>
    /// <returns>Обновленный список выражений сортировки с добавленным новым выражением по убыванию.</returns>
    public static List<OrderByExpression<T>> ThenDesc<T>(
        this List<OrderByExpression<T>> existing,
        Expression<Func<T, object>> keySelector) where T : class => existing.ThenBy(keySelector, SortDirection.Descending);

    /// <summary>
    /// Инвертирует направление сортировки в выражении.
    /// </summary>
    /// <typeparam name="T">Тип сущности, для которой применяется сортировка.</typeparam>
    /// <param name="expression">Исходное выражение сортировки.</param>
    /// <returns>Новое выражение сортировки с инвертированным направлением.</returns>
    public static OrderByExpression<T> Invert<T>(this OrderByExpression<T> expression) where T : class
    {
        var newDirection = expression.Direction == SortDirection.Ascending 
            ? SortDirection.Descending 
            : SortDirection.Ascending;
    
        return new OrderByExpression<T>(expression.KeySelector, newDirection);
    }
    
    /// <summary>
    /// Создает копию списка выражений сортировки.
    /// </summary>
    /// <typeparam name="T">Тип сущности, для которой применяется сортировка.</typeparam>
    /// <param name="sorts">Исходный список выражений сортировки.</param>
    /// <returns>Новый список, содержащий те же выражения сортировки.</returns>
    public static List<OrderByExpression<T>> Clone<T>(this IEnumerable<OrderByExpression<T>> sorts) where T : class
    {
        return sorts.ToList();
    }

    /// <summary>
    /// Применить весь набор сортировок к <see cref="IQueryable"/>>.
    /// (OrderBy + ThenBy цепочка)
    /// </summary>
    /// <typeparam name="T">Тип сущности, для которой применяется сортировка.</typeparam>
    /// <param name="source">Исходный запрос IQueryable.</param>
    /// <param name="sorts">Список выражений сортировки для применения.</param>
    /// <returns>Отсортированный IOrderedQueryable согласно указанным выражениям сортировки.</returns>
    /// <exception cref="ArgumentException">Выбрасывается, если список сортировок пуст или равен null.</exception>
    public static IOrderedQueryable<T> ApplyTo<T>(
        this IQueryable<T> source,
        IReadOnlyList<OrderByExpression<T>> sorts) where T : class
    {
        if (sorts == null || sorts.Count == 0)
            throw new ArgumentException("Необходимо хотя бы одно выражение сортировки", nameof(sorts));

        // Первичная сортировка
        var first = sorts[0];
        var ordered = first.Direction == SortDirection.Ascending
            ? source.OrderBy(first.KeySelector)
            : source.OrderByDescending(first.KeySelector);

        // Остальные ThenBy

        return sorts.Skip(1)
            .Aggregate(ordered, (current, s) => s.Direction == SortDirection.Ascending
                ? current.ThenBy(s.KeySelector)
                : current.ThenByDescending(s.KeySelector));
    }
}