using TeamFlow.Identity.Api.Extensions;
using TeamFlow.Identity.Application.Extensions;
using TeamFlow.Identity.Application.Services.Interfaces;
using Grpc.Core;
using MediatR;
using Microsoft.AspNetCore.Authorization;
using TeamFlow.Identity.Application.Contracts.Features.Queries.User;
using TeamFlow.Shared.Contracts.Pagination;
using VanId;

namespace TeamFlow.Identity.Api.Grpc.User;

[Authorize]
public class GetUsersPagedGrpcService : GrpcUserService.GrpcUserServiceBase
{
    private readonly ISender _sender;

    public GetUsersPagedGrpcService(ISender sender)
    {
        ArgumentNullException.ThrowIfNull(sender);
        _sender = sender;
    }

    public override async Task<GetUsersPagedResponse> GetUsersPaged(GetUsersPagedRequest request,
        ServerCallContext context)
    {
        var users = await _sender.Send(
            new GetUsersQuery(
                new Pageable(request.PageNumber, request.PageSize),
                null,
                null));

        var response = new GetUsersPagedResponse
        {
            TotalCount = users.Value.TotalCount,
            TotalPages = users.Value.TotalPages
        };

        response.Users.AddRange(users.Value.Elements?.Select(u => u.ToGrpcUser()));

        return response;
    }
}