using Grpc.Core;
using MediatR;
using Microsoft.AspNetCore.Authorization;
using TeamFlow.Identity.Api.Extensions;
using TeamFlow.Identity.Application.Contracts.Features.Queries.User;
using VanId;

namespace TeamFlow.Identity.Api.Grpc.User;

[Authorize]
public class GetUserByGuidGrpcService : GrpcUserService.GrpcUserServiceBase
{
    private readonly ISender _sender;

    public GetUserByGuidGrpcService(ISender sender)
    {
        _sender = sender;
    }

    public override async Task<VanId.User> GetUserByGuid(GetUserByGuidRequest request, ServerCallContext context)
    {
        if (!Guid.TryParse(request.Uuid, out var id))
            throw new RpcException(new Status(StatusCode.InvalidArgument, "Invalid UUID."));

        var result = await _sender.Send(new GetUserByIdQuery(id));

        if (result.IsFailed)
        {
            throw new RpcException(new Status(StatusCode.NotFound, "User not found."));
        }

        var response = result.Value.ToGrpcUser();

        return response;
    }
}