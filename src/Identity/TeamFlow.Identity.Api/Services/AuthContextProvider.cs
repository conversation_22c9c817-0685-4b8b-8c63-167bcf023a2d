using System.Net;
using TeamFlow.Identity.Application.Contracts.Dto.Client;
using TeamFlow.Identity.Application.Contracts.Services;
using TeamFlow.Identity.Core.Enums;
using UAParser;

namespace TeamFlow.Identity.Api.Services;

public class AuthContextProvider : IAuthContextProvider
{
    private readonly IHttpContextAccessor _httpContextAccessor;
    private readonly ILogger<AuthContextProvider> _logger;
    //TODO Здесь можно добавить сервис для определения геолокации по IP

    public AuthContextProvider(IHttpContextAccessor httpContextAccessor, ILogger<AuthContextProvider> logger)
    {
        _httpContextAccessor = httpContextAccessor;
        _logger = logger;
    }

    public async Task<AuthContextInfo> GetAuthContextAsync()
    {
        var context = _httpContextAccessor.HttpContext;
        if (context == null)
        {
            return new AuthContextInfo
            {
                IpAddress = IPAddress.None,
                UserAgent = "Unknown",
                DeviceInfo = new ClientDeviceInfo()
            };
        }

        var ipAddressStr = GetClientIpAddress(context);
        var ipAddress = IPAddress.TryParse(ipAddressStr, out var parsedIp) ? parsedIp : IPAddress.None;

        var userAgent = context.Request.Headers.UserAgent.ToString();
        var parser = Parser.GetDefault();
        var clientInfo = parser.Parse(userAgent);

        var deviceInfo = new ClientDeviceInfo
        {
            DeviceType = ResolveDeviceKind(clientInfo.Device.Family),
            DeviceName = $"{clientInfo.Device.Family} on {clientInfo.OS.Family}",
            OsName = clientInfo.OS.Family,
            OsVersion = BuildVersion(clientInfo.OS.Major, clientInfo.OS.Minor),
            BrowserName = clientInfo.UA.Family,
            BrowserVersion = BuildVersion(clientInfo.UA.Major, clientInfo.UA.Minor),
            // TimeZoneIana = context.Request.Headers["Time-Zone"] // если передаётся с фронта
        };

        var (country, city) = await GetLocationAsync(ipAddress.ToString());

        return new AuthContextInfo
        {
            IpAddress = ipAddress,
            UserAgent = userAgent,
            DeviceInfo = deviceInfo,
            Country = country,
            City = city,
            RequestTime = DateTime.UtcNow
        };
    }
    
    private static string? BuildVersion(string? major, string? minor)
        => major != null ? $"{major}.{minor ?? "0"}" : null;

    private static DeviceKind ResolveDeviceKind(string family)
    {
        var name = family.ToLowerInvariant();

        if (name.Contains("iphone") || name.Contains("android") || name.Contains("mobile"))
            return DeviceKind.Mobile;
        if (name.Contains("ipad") || name.Contains("tablet"))
            return DeviceKind.Tablet;
        if (name.Contains("windows") || name.Contains("mac") || name.Contains("linux") || name.Contains("desktop"))
            return DeviceKind.Desktop;

        return DeviceKind.Unknown;
    }

    public async Task<(string? Country, string? City)> GetLocationAsync(string ipAddress)
    {
        try
        {
            //TODO Здесь интеграция с MaxMind/IPStack/etc
            return (null, null);
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Failed to get location for IP: {IpAddress}", ipAddress);
            return (null, null);
        }
    }

    private static string GetClientIpAddress(HttpContext context)
    {
        var forwarded = context.Request.Headers["X-Forwarded-For"].FirstOrDefault();
        if (!string.IsNullOrEmpty(forwarded))
            return forwarded.Split(',')[0].Trim();

        var realIp = context.Request.Headers["X-Real-IP"].FirstOrDefault();
        if (!string.IsNullOrEmpty(realIp))
            return realIp;

        return context.Connection.RemoteIpAddress?.ToString() ?? "127.0.0.1";
    }
}