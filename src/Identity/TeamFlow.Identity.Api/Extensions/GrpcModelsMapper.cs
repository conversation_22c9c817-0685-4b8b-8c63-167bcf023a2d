using TeamFlow.Identity.Application.Contracts.Dto.ViewModels;
using TeamFlow.Identity.Core.Entities;

namespace TeamFlow.Identity.Api.Extensions;

public static class GrpcModelsMapper
{
    public static VanId.User ToGrpcUser(this User user)
    {
        return new VanId.User
        {
            Id = user.Id.ToString(),
            Login = user.Login,
            Email = user.Email,
            AvatarUrl = user.AvatarUrl,
            Role = (VanId.UserRole)user.Role,
            PositionId = user.PositionId.ToString()
        };
    }

    public static VanId.User ToGrpcUser(this UserViewModel userViewModel)
    {
        return new VanId.User
        {
            Id = userViewModel.Id.ToString(),
            Login = userViewModel.Login,
            Email = userViewModel.Email,
            AvatarUrl = userViewModel.AvatarUrl,
            Role = (VanId.UserRole)userViewModel.Role,
            PositionId = userViewModel.PositionId.ToString()
        };
    }

    public static VanId.Position ToGrpcPosition(this Position position)
    {
        return new VanId.Position
        {
            Id = position.Id.ToString(),
            Name = position.Name
        };
    }
    
    public static VanId.Position ToGrpcPosition(this PositionViewModel position)
    {
        return new VanId.Position
        {
            Id = position.Id.ToString(),
            Name = position.Name
        };
    }
}