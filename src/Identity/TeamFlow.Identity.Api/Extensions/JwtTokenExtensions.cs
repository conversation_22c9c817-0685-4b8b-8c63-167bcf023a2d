using System.IdentityModel.Tokens.Jwt;
using System.Security.Claims;
using Microsoft.IdentityModel.Tokens;

namespace TeamFlow.Identity.Api.Extensions;

public static class JwtTokenExtensions
{
    public static ClaimsPrincipal ValidateJwtToken(
        this string token, 
        TokenValidationParameters validationParameters, 
        JwtSecurityTokenHandler tokenHandler)
    {
        if (string.IsNullOrWhiteSpace(token)) 
            throw new ArgumentException($"JWT token cannot be empty {nameof(token)}");
        
        try
        {
            return tokenHandler.ValidateToken(token, validationParameters, out _);
        }
        catch (Exception)
        {
            // return null;
            throw new SecurityTokenException("Token validation failed");
        }
    }
}