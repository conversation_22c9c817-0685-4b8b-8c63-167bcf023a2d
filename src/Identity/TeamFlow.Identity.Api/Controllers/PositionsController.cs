using FluentResults.Extensions.AspNetCore;
using MediatR;
using TeamFlow.Identity.Application.Contracts.Dto.Position;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using TeamFlow.Identity.Api.Api.Filtering.Positions;
using TeamFlow.Identity.Api.Api.Responses.Base;
using TeamFlow.Identity.Application.Contracts.Features.Commands.Position;
using TeamFlow.Identity.Application.Contracts.Features.Queries.Position;
using TeamFlow.Identity.Application.Contracts.Filtering.Positions;
using TeamFlow.Shared.Contracts.Enums;
using TeamFlow.Shared.Contracts.Pagination;

namespace TeamFlow.Identity.Api.Controllers;

[Authorize]
[ApiController]
[Route("/api/[controller]")]
public sealed class PositionsController : ControllerBase
{
    private readonly ISender _sender;
    private const string AdminRole = nameof(UserRole.Admin);

    public PositionsController(ISender sender)
    {
        ArgumentNullException.ThrowIfNull(sender);
        _sender = sender;
    }

    [HttpGet("{id:guid}")]
    public async Task<IActionResult> GetByIdAsync(Guid id, CancellationToken cancellationToken = default)
    {
        var position = await _sender.Send(new GetPositionByIdQuery(id), cancellationToken);
        return position.ToActionResult();
    }

    [HttpGet("name/{name}")]
    public async Task<IActionResult> GetByNameAsync(string name, CancellationToken cancellationToken = default)
    {
        var result = await _sender.Send(new GetPositionByNameQuery(name), cancellationToken);
        return result.ToActionResult();
    }

    [HttpGet]
    public async Task<IActionResult> GetManyAsync(
        [FromQuery] PositionFilteringQueryParameters filterParameters,
        [FromQuery] PositionSortingQueryParameters sortingParameters,
        [FromQuery] string? searchTerm = null,
        [FromQuery] int pageNumber = 1,
        [FromQuery] int pageSize = 10, 
        CancellationToken cancellationToken = default)
    {
        var pageable = new Pageable(pageNumber, pageSize);
        var filterCriteria = new PositionFilterCriteria(filterParameters.Name, searchTerm);
        var sortingCriteria = new PositionSortingCriteria(sortingParameters.SortField, sortingParameters.Direction);

        var result = await _sender.Send(new GetPositionsQuery(pageable, filterCriteria, sortingCriteria), cancellationToken);
        return result.ToActionResult();
    }

    [Authorize(Roles = AdminRole)]
    [HttpPost]
    public async Task<IActionResult> CreateAsync(
        [FromBody] CreatePositionCommand createPositionRequest,
        CancellationToken cancellationToken = default)
    {
        var result = await _sender.Send(createPositionRequest, cancellationToken);
        return result.ToActionResult();
    }

    [Authorize(Roles = AdminRole)]
    [HttpPatch("{id:guid}")]
    public async Task<IActionResult> UpdateAsync(
        Guid id,
        [FromBody] UpdatePositionRequest updatePositionRequest,
        CancellationToken cancellationToken = default)
    {
        var result = await _sender.Send(new UpdatePositionCommand(id, updatePositionRequest.Name), cancellationToken);
        return result.ToActionResult();
    }

    [Authorize(Roles = AdminRole)]
    [HttpDelete("{id:guid}")]
    public async Task<IActionResult> DeleteAsync(Guid id, CancellationToken cancellationToken = default)
    {
        var result = await _sender.Send(new DeletePositionCommand(id), cancellationToken);
        return result.ToActionResult();
    }
}