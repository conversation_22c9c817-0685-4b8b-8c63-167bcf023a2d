syntax = "proto3";

import "google/protobuf/empty.proto";

option csharp_namespace = "VanId";

package Grpc.User;

service GrpcUserService {
  rpc GetUserByGuid(GetUserByGuidRequest) returns (User);
  rpc GetUserByLogin(GetUserByLoginRequest) returns (User);
  rpc GetUserByEmail(GetUserByEmailRequest) returns (User);
  rpc GetUsersPaged(GetUsersPagedRequest) returns (GetUsersPagedResponse);
}

message CreateUserRequest {
  string login = 1;
  string email = 2;
  string password = 3;
  UserRole role = 4;
  string positionId = 5;
}

message GetUserByGuidRequest {
  string uuid = 1;
}

message GetUserByLoginRequest {
  string login = 1;
}

message GetUserByEmailRequest {
  string email = 1;
}

message GetUsersPagedRequest {
  int32 pageNumber = 1;
  int32 pageSize = 2;
}

message GetUsersResponse {
  repeated User users = 1;
}

message GetUsersPagedResponse {
  int64 totalCount = 1;
  double totalPages = 2;
  repeated User users = 3;
}

message User {
  string id = 1;
  string login = 2;
  string email = 3;
//  string passwordHash = ;
//  string refreshTokenHash = ;
  string avatarUrl = 4;
  UserRole role = 5;
  string positionId = 6;
}

enum UserRole {
    REGULAR = 0;
    ADMIN = 1;
}
