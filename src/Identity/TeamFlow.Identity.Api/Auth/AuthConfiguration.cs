using System.Text;
using Microsoft.IdentityModel.Tokens;

namespace TeamFlow.Identity.Api.Auth;

public class AuthConfiguration
{
    private readonly AuthOptions _authOptions;

    public AuthConfiguration(AuthOptions authOptions)
    {
        _authOptions = authOptions;
    }
    
    public string Audience => _authOptions.Audience!;
    
    public string Issuer => _authOptions.Issuer!;
    
    public SymmetricSecurityKey GetSymmetricSecurityKey()
    {
        if (string.IsNullOrWhiteSpace(_authOptions.Key))
            throw new ArgumentException($"Please provide a security key. {nameof(_authOptions.Key)}");
        
        return new SymmetricSecurityKey(Encoding.UTF8.GetBytes(_authOptions.Key));
    }
    
    public int AccessTokenLifetime => _authOptions.AccessTokenLifetime;
    
    public int RefreshTokenLifetime => _authOptions.RefreshTokenLifetime;
}