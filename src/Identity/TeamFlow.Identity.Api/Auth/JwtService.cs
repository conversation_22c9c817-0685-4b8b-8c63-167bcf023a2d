using System.IdentityModel.Tokens.Jwt;
using System.Security.Claims;
using TeamFlow.Identity.Api.Extensions;
using TeamFlow.Identity.Application.Services.Interfaces;
using TeamFlow.Identity.Core.Enums;
using Microsoft.IdentityModel.Tokens;
using TeamFlow.Identity.Application.Contracts.Models;
using TeamFlow.Identity.Application.Contracts.Services;

namespace TeamFlow.Identity.Api.Auth;

public class JwtService : IJwtService
{
    private readonly AuthConfiguration _authConfiguration;

    private readonly JwtSecurityTokenHandler _tokenHandler;

    public JwtService(AuthConfiguration authConfiguration, JwtSecurityTokenHandler tokenHandler)
    {
        ArgumentNullException.ThrowIfNull(authConfiguration, nameof(authConfiguration));
        ArgumentNullException.ThrowIfNull(tokenHandler, nameof(tokenHandler));

        _authConfiguration = authConfiguration;
        _tokenHandler = tokenHandler;
    }

    public string GenerateAccessToken(TokensPayload payload)
    {
        var expires = DateTime.UtcNow.AddMinutes(_authConfiguration.AccessTokenLifetime);
        return GenerateJwtToken(payload, nameof(JwtClaimValue.Access), expires);
    }

    public string GenerateRefreshToken(TokensPayload payload)
    {
        var expires = DateTime.UtcNow.AddDays(_authConfiguration.RefreshTokenLifetime);
        return GenerateJwtToken(payload, nameof(JwtClaimValue.Refresh), expires);
    }

    public ClaimsPrincipal? ValidateToken(string token)
    {
        if (string.IsNullOrWhiteSpace(token)) 
            throw new ArgumentException($"JWT token cannot be empty {nameof(token)}");
        
        try
        {
            return token.ValidateJwtToken(
                _authConfiguration.GetTokenValidationParameters(), 
                _tokenHandler);
        }
        catch
        {
            return null;
        }
    }

    private string GenerateJwtToken(TokensPayload payload, string tokenTypeClaimValue, DateTime expires)
    {
        ArgumentNullException.ThrowIfNull(payload);

        List<Claim> claims =
        [
            new(nameof(JwtClaimTypes.Sub), payload.Id.ToString()),
            new(ClaimTypes.Name, payload.Login),
            new(ClaimTypes.Role, payload.Role.ToString()),
            new(nameof(JwtClaimTypes.TokenType), tokenTypeClaimValue)
        ];

        var jwt = new JwtSecurityToken(
            issuer: _authConfiguration.Issuer,
            audience: _authConfiguration.Audience,
            claims: claims,
            expires: expires,
            signingCredentials: new SigningCredentials(_authConfiguration.GetSymmetricSecurityKey(),
                SecurityAlgorithms.HmacSha256));

        return _tokenHandler.WriteToken(jwt);
    }
}