namespace TeamFlow.Identity.Api.Api.Responses.Base;

//TODO Need to edit
public abstract class ApiResponseBase(bool isSuccess, int statusCode, string? message, string? error)
{
    public bool IsSuccess { get; } = isSuccess;

    public int StatusCode { get; } = statusCode;

    public string? Message { get; } = message;

    public string? Error { get; } = error;
}

public class ApiResponse(
    bool isSuccess, 
    int statusCode, 
    string? message, 
    string? error) : ApiResponseBase(isSuccess, statusCode, message, error)
{
    public static ApiResponse Success(string? message, int statusCode = 200) 
        => new(true, statusCode, message, null);

    public static ApiResponse Failure(string error, int statusCode = 400) 
        => new(false, statusCode, null, error); }

public class ApiResponse<T> : ApiResponseBase
{
    public T? Data { get; }

    private ApiResponse(
        bool isSuccess, 
        int statusCode, 
        string? message,  
        T? data, 
        string? error) : base(isSuccess, statusCode, message, error)
    {
        Data = data;
    }

    public static ApiResponse<T> Success(T data, string? message, int statusCode = 200) 
        => new(true, statusCode, message, data, null);

    public static ApiResponse<T> Failure(string error, int statusCode = 400) 
        => new(false, statusCode, null, default, error);

}