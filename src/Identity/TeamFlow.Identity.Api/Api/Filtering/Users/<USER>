using TeamFlow.Identity.Api.Api.Filtering.Common;
using TeamFlow.Identity.Application.Contracts.Filtering.Users;
using TeamFlow.Shared.Contracts.Enums;

namespace TeamFlow.Identity.Api.Api.Filtering.Users;

public record UserSortingQueryParameters(
    UsersSortField SortField = UsersSortField.CreatedAt,
    SortDirection Direction = SortDirection.Descending)
    : AbstractSortingQueryParameters<UsersSortField>(SortField, Direction);