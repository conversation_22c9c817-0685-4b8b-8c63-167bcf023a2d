using TeamFlow.Identity.Api.Api.Filtering.Common;
using TeamFlow.Identity.Application.Contracts.Filtering.Positions;
using TeamFlow.Shared.Contracts.Enums;

namespace TeamFlow.Identity.Api.Api.Filtering.Positions;

public record PositionSortingQueryParameters(
    PositionsSortField SortField = PositionsSortField.CreatedAt,
    SortDirection Direction = SortDirection.Descending)
    : AbstractSortingQueryParameters<PositionsSortField>(SortField, Direction);