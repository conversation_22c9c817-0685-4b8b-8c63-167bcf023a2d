using TeamFlow.Identity.Api.Utils.Interfaces;

namespace TeamFlow.Identity.Api.Utils;

/*
 * TODO
 * Вынести настройки кук с помощью паттерна Options
 */
public class CookieUtils : ICookieUtils
{
    private readonly IConfiguration _configuration;

    private const string SectionName = "Cookie";
    
    public CookieUtils(IConfiguration configuration)
    {
        ArgumentNullException.ThrowIfNull(configuration);
        _configuration = configuration;
    }

    public void SetRefreshTokenCookie(HttpResponse response, string refreshToken) 
        => response.Cookies.Append("refreshToken", refreshToken, GetCookieOptions());

    public string? GetRefreshTokenFromCookie(HttpRequest request) 
        => request.Cookies["refreshToken"];

    public void RemoveRefreshTokenFromCookie(HttpResponse response) 
        => response.Cookies.Delete("refreshToken");

    private CookieOptions GetCookieOptions() => new()
    {
        HttpOnly = _configuration.GetSection(SectionName).GetValue<bool>("HttpOnly"),
        Secure = _configuration.GetSection(SectionName).GetValue<bool>("Secure"),
        SameSite = Enum.Parse<SameSiteMode>(_configuration.GetSection(SectionName).GetValue<string>("SameSite")!),
        Expires = (DateTimeOffset)DateTime.UtcNow.AddDays(_configuration.GetSection(SectionName).GetValue<int>("Expires")),
        Domain = _configuration.GetSection(SectionName).GetValue<string>("Domain")
    };
}