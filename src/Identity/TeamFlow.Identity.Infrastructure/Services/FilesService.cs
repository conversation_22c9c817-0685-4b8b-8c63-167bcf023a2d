using Amazon.S3;
using Amazon.S3.Model;
using TeamFlow.Identity.Application.Services.Interfaces;
using Microsoft.AspNetCore.Http;
using TeamFlow.Identity.Core.Services;
using TeamFlow.Identity.Infrastructure.Aws;

namespace TeamFlow.Identity.Infrastructure.Services;

//TODO Configure s3 options
//TODO rewrite, but use s3 anyway
[Obsolete]
public class FilesService : IFileService
{
    private readonly IAmazonS3 _s3Client;

    private readonly AwsS3Options _options;

    private const string Prefix = "avatars";

    public FilesService(IAmazonS3 s3Client, AwsS3Options options)
    {
        ArgumentNullException.ThrowIfNull(s3Client, nameof(s3Client));
        _s3Client = s3Client;
        _options = options;
    }

    public async Task<string> UploadFileAsync(IFormFile file, CancellationToken cancellationToken = default)
    {
        if (file.Length == 0)
        {
            throw new ArgumentException("No file uploaded", nameof(file));
        }
    
        await using var stream = file.OpenReadStream();
    
        var key = Guid.CreateVersion7();
    
        var request = new PutObjectRequest
        {
            BucketName = _options.BucketName,
            Key = $"{Prefix}/{key}",
            InputStream = stream,
            ContentType = file.ContentType,
            Metadata =
            {
                ["file-name"] = file.FileName,
            }
        };
    
        await _s3Client.PutObjectAsync(request, cancellationToken);
    
        return key.ToString();
    }
    
    // public async Task<string> UploadFileAsync(IFormFile file, CancellationToken cancellationToken = default)
    // {
    //     try
    //     {
    //         var key = Guid.CreateVersion7();
    //
    //         var request = new GetPreSignedUrlRequest
    //         {
    //             BucketName = _options.BucketName,
    //             Key = $"{Prefix}/{key}",
    //             Verb = HttpVerb.PUT,
    //             ContentType = file.ContentType,
    //             Metadata =
    //             {
    //                 ["file-name"] = file.FileName,
    //             }
    //         };
    //         
    //         var url = await _s3Client.GetPreSignedURLAsync(request);
    //
    //         return url;
    //     }
    //     catch (AmazonS3Exception ex)
    //     {
    //         Console.WriteLine(ex.Message);
    //         throw;
    //     }
    // }

    public Task<string> UploadFileAsync(Stream fileStream, CancellationToken cancellationToken = default)
    {
        throw new NotImplementedException();
    }

    public async Task<string> GetFileAsync(string key, CancellationToken cancellationToken = default)
    {
        var request = new GetPreSignedUrlRequest
        {
            BucketName = _options.BucketName,
            Key = $"{Prefix}/{key}",
            Verb = HttpVerb.GET,
        };

        var url = await _s3Client.GetPreSignedURLAsync(request);

        if (string.IsNullOrWhiteSpace(url)) throw new AmazonS3Exception($"S3 error getting pre-signed url: {key}");

        return url;
    }

    public async Task DeleteFileAsync(string key, CancellationToken cancellationToken = default)
    {
        var request = new DeleteObjectRequest
        {
            BucketName = _options.BucketName,
            Key = $"{Prefix}/{key}"
        };

        await _s3Client.DeleteObjectAsync(request, cancellationToken);
    }
}