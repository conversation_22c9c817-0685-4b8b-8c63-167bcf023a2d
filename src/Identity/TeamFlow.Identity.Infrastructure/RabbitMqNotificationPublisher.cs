using TeamFlow.Identity.Application.Abstractions;
using TeamFlow.Identity.Application.Contracts.Dto.Notifications;

namespace TeamFlow.Identity.Infrastructure;

public class RabbitMqNotificationPublisher : ISendNotification
{
    public RabbitMqNotificationPublisher()
    {
        
    }   
    
    public Task SendNotificationAsync(string title, object content, CancellationToken cancellationToken = default)
    {
        ArgumentNullException.ThrowIfNull(content);

        if (string.IsNullOrWhiteSpace(title)) throw new ArgumentNullException(nameof(title));
        
        if (content is not EmailNotification) throw new ArgumentException($"{nameof(content)} must be a {nameof(EmailNotification)}");
        
        return Task.CompletedTask;
    }
}