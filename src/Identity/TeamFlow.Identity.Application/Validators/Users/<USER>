using FluentValidation;
using TeamFlow.Identity.Application.Contracts.Features.Commands.User;
using TeamFlow.Identity.Core.Entities;

namespace TeamFlow.Identity.Application.Validators.Users;

public class ChangePasswordCommandValidator : AbstractValidator<ChangePasswordCommand>
{
    public ChangePasswordCommandValidator()
    {
        RuleFor(x => x.UserId)
            .NotEmpty()
            .WithMessage("User ID is required");

        RuleFor(x => x.OldPassword)
            .NotEmpty()
            .WithMessage("Old password is required");

        RuleFor(x => x.NewPassword)
            .NotEmpty()
            .WithMessage("New password is required")
            .Length(User.MinPasswordLength, User.MaxPasswordLength)
            .WithMessage($"New password must be between {User.MinPasswordLength} and {User.MaxPasswordLength} characters")
            .Matches(@"^(?!.*\s)[\x21-\x7E]+$")
            .WithMessage("New password contains invalid characters");

        RuleFor(x => x.NewPasswordRepeat)
            .NotEmpty()
            .WithMessage("Password confirmation is required")
            .Equal(x => x.NewPassword)
            .WithMessage("Password confirmation does not match new password");

        RuleFor(x => x.NewPassword)
            .NotEqual(x => x.OldPassword)
            .WithMessage("New password must be different from old password")
            .When(x => !string.IsNullOrWhiteSpace(x.OldPassword) && !string.IsNullOrWhiteSpace(x.NewPassword));
    }
}
