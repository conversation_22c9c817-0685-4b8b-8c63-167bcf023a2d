using FluentValidation;
using TeamFlow.Identity.Application.Contracts.Features.Commands.User;

namespace TeamFlow.Identity.Application.Validators.Users;

public class UpdateUserPositionCommandValidator : AbstractValidator<UpdateUserPositionCommand>
{
    public UpdateUserPositionCommandValidator()
    {
        RuleFor(u => u.PositionId)
            .NotEmpty()
            .WithMessage("You don't specified position. Please select position id.");
    }
}