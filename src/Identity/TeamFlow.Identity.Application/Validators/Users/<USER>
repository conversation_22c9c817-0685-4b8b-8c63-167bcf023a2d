using FluentValidation;
using TeamFlow.Identity.Application.Contracts.Dto.User;

namespace TeamFlow.Identity.Application.Validators.Users;

public class UpdateUserAvatarRequestValidator : AbstractValidator<UpdateUserAvatarRequest>
{
    public UpdateUserAvatarRequestValidator()
    {
        RuleFor(u => u.FileStream)
            .NotNull()
            .WithMessage("File is required, but was not provided. Please select file and try again.");
    }
}