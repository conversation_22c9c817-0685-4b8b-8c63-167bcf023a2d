using FluentValidation;
using TeamFlow.Identity.Application.Contracts.Features.Commands.User;

namespace TeamFlow.Identity.Application.Validators.Users;

public class UpdateUserOnlineStatusCommandValidator : AbstractValidator<UpdateUserOnlineStatusCommand>
{
    public UpdateUserOnlineStatusCommandValidator()
    {
        RuleFor(u => u.OnlineStatus)
            .NotNull()
            .IsInEnum()
            .WithMessage("An online status is required. Please ensure you select one of the available options before submitting.");
    }
}