using FluentValidation;
using TeamFlow.Identity.Application.Contracts.Features.Commands.Auth;
using TeamFlow.Identity.Core.Entities;

namespace TeamFlow.Identity.Application.Validators.Auth;

public class AuthorizeCommandValidator : AbstractValidator<AuthenticateCommand>
{
    public AuthorizeCommandValidator()
    {
        RuleFor(a => a.<PERSON>)
            .NotEmpty()
            .When(a => !string.IsNullOrWhiteSpace(a.<PERSON>gin))
            .Length(User.<PERSON>, User.MaxLoginLength)
            .When(a => !string.IsNullOrWhiteSpace(a.<PERSON>gin));
        
        RuleFor(a => a.Email)
            .NotEmpty()
            .When(a => !string.IsNullOrWhiteSpace(a.Email))
            .EmailAddress()
            .When(a => !string.IsNullOrWhiteSpace(a.Email))
            .MaximumLength(User.MaxEmailLength)
            .When(a => !string.IsNullOrWhiteSpace(a.Email));

        RuleFor(a => a.Password)
            .NotEmpty()
            .Length(User.<PERSON>, User.Max<PERSON>asswordLength);
        
        RuleFor(x => x)
            .Must(x => 
                !string.IsNullOrWhiteSpace(x.Login) || !string.IsNullOrWhiteSpace(x.Email))
            .WithMessage("Either login or email must be provided.");
    }
}