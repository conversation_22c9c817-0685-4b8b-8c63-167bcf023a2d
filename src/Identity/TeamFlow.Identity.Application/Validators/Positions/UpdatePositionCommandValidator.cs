using FluentValidation;
using TeamFlow.Identity.Application.Contracts.Dto.Position;
using TeamFlow.Identity.Application.Contracts.Features.Commands.Position;
using TeamFlow.Identity.Core.Entities;

namespace TeamFlow.Identity.Application.Validators.PositionValidators;

public class UpdatePositionCommandValidator : AbstractValidator<UpdatePositionCommand>
{
    public UpdatePositionCommandValidator()
    {
        RuleFor(p => p.Name)
            .NotEmpty()
            .Length(Position.MinNameLength, Position.MaxNameLength);
    }
}