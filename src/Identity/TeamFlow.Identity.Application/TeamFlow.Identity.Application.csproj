<Project Sdk="Microsoft.NET.Sdk">

    <PropertyGroup>
        <TargetFramework>net9.0</TargetFramework>
        <ImplicitUsings>enable</ImplicitUsings>
        <Nullable>enable</Nullable>
    </PropertyGroup>

    <ItemGroup>
      <PackageReference Include="BCrypt.Net-Next" Version="4.0.3" />
      <PackageReference Include="FluentValidation" Version="11.11.0" />
      <PackageReference Include="FluentValidation.AspNetCore" Version="11.3.0" />
      <PackageReference Include="MediatR" Version="12.4.1" />
      <PackageReference Include="MediatR.Contracts" Version="2.0.1" />
      <PackageReference Include="MediatR.Extensions.FluentValidation.AspNetCore" Version="5.1.0" />
    </ItemGroup>

    <ItemGroup>
      <ProjectReference Include="..\..\..\TeamFlow.Identity.Application.Contracts\TeamFlow.Identity.Application.Contracts.csproj" />
      <ProjectReference Include="..\..\Tasks\TeamFlow.Tasks.Core\TeamFlow.Tasks.Core.csproj" />
    </ItemGroup>

    <ItemGroup>
      <Folder Include="Features\" />
    </ItemGroup>

</Project>
