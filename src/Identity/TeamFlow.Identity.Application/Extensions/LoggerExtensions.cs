using FluentValidation.Results;
using Microsoft.Extensions.Logging;

namespace TeamFlow.Identity.Application.Extensions;

public static class LoggerExtensions
{
    public static void LogValidationErrors(this ILogger logger, 
        IEnumerable<ValidationFailure> errors, 
        string context)
    {
        if (!logger.IsEnabled(LogLevel.Warning)) return;
        var errorMessages = errors.Select(e => e.ErrorMessage);
        logger.LogWarning("Ошибка валидации при {Context}: {Errors}", 
            context, 
            string.Join(", ", errorMessages));
    }
}