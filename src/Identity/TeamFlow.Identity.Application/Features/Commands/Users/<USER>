using System.Linq.Expressions;
using FluentResults;
using FluentValidation;
using MediatR;
using Microsoft.Extensions.Logging;
using TeamFlow.Identity.Application.Contracts.Features.Commands.User;
using TeamFlow.Identity.Application.Errors;
using TeamFlow.Identity.Application.Services.Interfaces;
using TeamFlow.Identity.Core.Repositories;
using TeamFlow.Identity.Core.Utils;

namespace TeamFlow.Identity.Application.Features.Commands.Users;

public class UpdateUserCommandHandler : IRequestHandler<UpdateUserCommand, Result>
{
    private readonly IGenericRepository<Core.Entities.User, Guid> _usersRepository;
    private readonly IGenericRepository<Core.Entities.Position, Guid> _positionsRepository;
    private readonly IValidator<UpdateUserCommand> _validator;
    private readonly ILogger<UpdateUserCommandHandler> _logger;
    private readonly IHashDataService _hashDataService;

    public UpdateUserCommandHandler(IGenericRepository<Core.Entities.User, Guid> usersRepository,
        IGenericRepository<Core.Entities.Position, Guid> positionsRepository, IValidator<UpdateUserCommand> validator,
        ILogger<UpdateUserCommandHandler> logger, IHashDataService hashDataService)
    {
        _usersRepository = usersRepository;
        _positionsRepository = positionsRepository;
        _validator = validator;
        _logger = logger;
        _hashDataService = hashDataService;
    }

    public async Task<Result> Handle(UpdateUserCommand request, CancellationToken cancellationToken)
    {
        var validationResult = await _validator.ValidateAsync(request, cancellationToken);
        if (!validationResult.IsValid)
        {
            return Result.Fail(ErrorsFactory.FromValidationResult(nameof(Core.Entities.User), validationResult));
        }

        var user = await _usersRepository.GetOneAsync(x => x.Id == request.UserId, cancellationToken);
        if (user is null)
        {
            return Result.Fail(ErrorsFactory.NotFound(nameof(Core.Entities.User), request.UserId));
        }

        if (request.PositionId.HasValue)
        {
            var position =
                await _positionsRepository.GetOneAsync(x => x.Id == request.PositionId!.Value, cancellationToken);
            if (position is null)
            {
                return Result.Fail(ErrorsFactory.NotFound(nameof(Core.Entities.Position), request.UserId));
            }

            user.PositionId = request.PositionId.Value;
        }

        var isLoginUpdated = ValueComparer.IsUpdated(user.Login, request.Login);
        var isEmailUpdated = ValueComparer.IsUpdated(user.Email, request.Email);

        if (isLoginUpdated || isEmailUpdated)
        {
            Expression<Func<Core.Entities.User, bool>> predicate =
                u => ((isLoginUpdated && u.Login == request.Login)
                      || (isEmailUpdated && u.Email == request.Email))
                     && u.Id != request.UserId;

            var exists = await _usersRepository.FindAnyAsync(predicate, cancellationToken);

            if (exists)
            {
                //TODO need to fix
                var value = request.Login ?? request.Email;
                return Result.Fail(ErrorsFactory.AlreadyExists(nameof(Core.Entities.User), nameof(request.Login),
                    value!));
            }
        }

        user.Login = request.Login ?? user.Login;
        user.Email = request.Email ?? user.Email;
        user.PasswordHash = request.Password is not null
            ? _hashDataService.HashData(request.Password)
            : user.PasswordHash;
        user.Role = request.Role ?? user.Role;
        user.UpdatedAt = DateTime.UtcNow;

        await _usersRepository.UpdateAsync(user, cancellationToken);

        return Result.Ok();
    }
}