using FluentResults;
using FluentValidation;
using MediatR;
using Microsoft.Extensions.Logging;
using TeamFlow.Identity.Application.Contracts.Features.Commands.User;
using TeamFlow.Identity.Application.Errors;
using TeamFlow.Identity.Core.Repositories;

namespace TeamFlow.Identity.Application.Features.Commands.Users;

public class DeleteUserCommandHandler : IRequestHandler<DeleteUserCommand, Result>
{
    private readonly IGenericRepository<Core.Entities.User, Guid> _usersRepository;
    private readonly IValidator<DeleteUserCommand> _validator;
    private readonly ILogger<DeleteUserCommandHandler> _logger;

    public DeleteUserCommandHandler(IGenericRepository<Core.Entities.User, Guid> usersRepository,
        IValidator<DeleteUserCommand> validator, ILogger<DeleteUserCommandHandler> logger)
    {
        _usersRepository = usersRepository;
        _validator = validator;
        _logger = logger;
    }

    public async Task<Result> Handle(DeleteUserCommand request, CancellationToken cancellationToken)
    {
        var validationResult = await _validator.ValidateAsync(request, cancellationToken);
        if (!validationResult.IsValid)
        {
            return Result.Fail(ErrorsFactory.FromValidationResult(nameof(Core.Entities.User), validationResult));
        }

        var user = await _usersRepository.GetOneAsync(x => x.Id == request.Id, cancellationToken);

        if (user is null)
        {
            return Result.Fail(ErrorsFactory.NotFound(nameof(Core.Entities.User), request.Id));
        }

        await _usersRepository.RemoveAsync(user, cancellationToken);

        return Result.Ok();
    }
}