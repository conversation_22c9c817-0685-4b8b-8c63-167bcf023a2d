using FluentResults;
using FluentValidation;
using MediatR;
using Microsoft.Extensions.Logging;
using TeamFlow.Identity.Application.Contracts.Features.Commands.User;
using TeamFlow.Identity.Application.Errors;
using TeamFlow.Identity.Core.Entities;
using TeamFlow.Identity.Core.Repositories;

namespace TeamFlow.Identity.Application.Features.Commands.Users;

public sealed class UpdatePositionCommandHandler : IRequestHandler<UpdateUserPositionCommand, Result<Guid>>
{
    private readonly IGenericRepository<Core.Entities.User, Guid> _usersRepository;
    private readonly IGenericRepository<Core.Entities.Position, Guid> _positionsRepository;
    private readonly IValidator<UpdateUserPositionCommand> _validator;
    private readonly ILogger<UpdatePositionCommandHandler> _logger;

    public UpdatePositionCommandHandler(IGenericRepository<User, Guid> usersRepository, IGenericRepository<Core.Entities.Position, Guid> positionsRepository, IValidator<UpdateUserPositionCommand> validator, ILogger<UpdatePositionCommandHandler> logger)
    {
        _usersRepository = usersRepository;
        _positionsRepository = positionsRepository;
        _validator = validator;
        _logger = logger;
    }

    public async Task<Result<Guid>> Handle(UpdateUserPositionCommand request, CancellationToken cancellationToken)
    {
        var validationResult = await _validator.ValidateAsync(request, cancellationToken);
        if (!validationResult.IsValid)
        {
            return Result.Fail(ErrorsFactory.FromValidationResult(nameof(Core.Entities.User), validationResult));
        }
        
        var userTask = _usersRepository.GetOneAsync(x => x.Id == request.UserId, cancellationToken);
        var positionTask = _positionsRepository.GetOneAsync(x => x.Id == request.PositionId, cancellationToken);
        
        await Task.WhenAll(userTask, positionTask);
        
        var (user, position) = (userTask.Result, positionTask.Result);

        if (user is null)
        {
            return Result.Fail(ErrorsFactory.NotFound(nameof(User), request.UserId));
        }

        if (position is null)
        {
            return Result.Fail(ErrorsFactory.NotFound(nameof(Core.Entities.Position), request.PositionId));
        }

        user.PositionId = position.Id;
        user.Position = position;
        user.UpdatedAt = DateTime.UtcNow;
        
        await _usersRepository.UpdateAsync(user, cancellationToken);
        return user.Id;
    }
}