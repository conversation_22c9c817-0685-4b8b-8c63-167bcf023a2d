using FluentResults;
using FluentValidation;
using MediatR;
using Microsoft.Extensions.Logging;
using TeamFlow.Identity.Application.Contracts.Features.Commands.User;
using TeamFlow.Identity.Application.Errors;
using TeamFlow.Identity.Core.Entities;
using TeamFlow.Identity.Core.Repositories;

namespace TeamFlow.Identity.Application.Features.Commands.Users;

public class UpdateOnlineStatusCommandHandler : IRequestHandler<UpdateUserOnlineStatusCommand, Result>
{
    private readonly IGenericRepository<Core.Entities.User, Guid> _usersRepository;
    private readonly IValidator<UpdateUserOnlineStatusCommand> _validator;
    private readonly ILogger<UpdateOnlineStatusCommandHandler> _logger;

    public UpdateOnlineStatusCommandHandler(IGenericRepository<Core.Entities.User, Guid> usersRepository,
        IValidator<UpdateUserOnlineStatusCommand> validator, ILogger<UpdateOnlineStatusCommandHandler> logger)
    {
        _usersRepository = usersRepository;
        _validator = validator;
        _logger = logger;
    }

    public async Task<Result> Handle(UpdateUserOnlineStatusCommand request, CancellationToken cancellationToken)
    {
        var validationResult = await _validator.ValidateAsync(request, cancellationToken);
        if (!validationResult.IsValid)
        {
            return Result.Fail(ErrorsFactory.FromValidationResult(nameof(User), validationResult));
        }

        var user = await _usersRepository.GetOneAsync(x => x.Id == request.UserId, cancellationToken);

        if (user is null)
        {
            return Result.Fail(ErrorsFactory.NotFound(nameof(User), request.UserId));
        }

        user.OnlineStatus = request.OnlineStatus;
        user.UpdatedAt = DateTime.UtcNow;

        await _usersRepository.UpdateAsync(user, cancellationToken);

        return Result.Ok();
    }
}