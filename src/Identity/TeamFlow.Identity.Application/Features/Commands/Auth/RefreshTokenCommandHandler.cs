using FluentResults;
using FluentValidation;
using MediatR;
using TeamFlow.Identity.Application.Contracts.Features.Commands.Auth;
using TeamFlow.Identity.Application.Contracts.Models;
using TeamFlow.Identity.Application.Contracts.Services;
using TeamFlow.Identity.Application.Errors;
using TeamFlow.Identity.Application.Services.Interfaces;
using TeamFlow.Identity.Core.Entities;
using TeamFlow.Identity.Core.Repositories;

namespace TeamFlow.Identity.Application.Features.Commands.Auth;

public sealed class RefreshTokenCommandHandler : IRequestHandler<RefreshTokenCommand, Result<JwtTokens>>
{
    private readonly IGenericRepository<User, Guid> _usersRepository;
    private readonly IHashDataService _hashDataService;
    private readonly IJwtService _jwtService;
    private readonly IRefreshTokenHelper _refreshTokenHelper;
    private readonly IValidator<RefreshTokenCommand> _validator;

    public RefreshTokenCommandHandler(IGenericRepository<User, Guid> usersRepository, IHashDataService hashDataService,
        IJwtService jwtService, IValidator<RefreshTokenCommand> validator, IRefreshTokenHelper refreshTokenHelper)
    {
        _usersRepository = usersRepository;
        _hashDataService = hashDataService;
        _jwtService = jwtService;
        _validator = validator;
        _refreshTokenHelper = refreshTokenHelper;
    }

    public async Task<Result<JwtTokens>> Handle(RefreshTokenCommand request, CancellationToken cancellationToken)
    {
        var validationResult = await _validator.ValidateAsync(request, cancellationToken);
        if (!validationResult.IsValid)
        {
            return Result.Fail(ErrorsFactory.FromValidationResult(nameof(User), validationResult));
        }

        var userResult = await _refreshTokenHelper
            .GetUserFromRefreshTokenAsync(request.RefreshToken, cancellationToken);

        if (userResult.IsFailed)
        {
            return Result.Fail(userResult.Errors);
        }

        var user = userResult.Value;

        var payload = new TokensPayload(user.Id, user.Login, user.Role, user.PositionId);

        var tokens = new JwtTokens(
            _jwtService.GenerateAccessToken(payload),
            _jwtService.GenerateRefreshToken(payload));

        user.RefreshTokenHash = _hashDataService.HashData(tokens.RefreshToken);
        user.UpdatedAt = DateTime.UtcNow;
        await _usersRepository.UpdateAsync(user, cancellationToken);

        return tokens;
    }
}