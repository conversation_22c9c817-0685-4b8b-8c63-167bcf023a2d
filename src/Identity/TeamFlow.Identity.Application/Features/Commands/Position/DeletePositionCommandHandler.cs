using FluentResults;
using FluentValidation;
using MediatR;
using Microsoft.Extensions.Logging;
using TeamFlow.Identity.Application.Contracts.Features.Commands.Position;
using TeamFlow.Identity.Application.Errors;
using TeamFlow.Identity.Core.Repositories;

namespace TeamFlow.Identity.Application.Features.Commands.Position;

public class DeletePositionCommandHandler : IRequestHandler<DeletePositionCommand, Result>
{
    private readonly IGenericRepository<Core.Entities.Position, Guid> _positionsRepository;
    private readonly IValidator<DeletePositionCommand> _validator;
    private readonly ILogger<DeletePositionCommandHandler> _logger;


    public DeletePositionCommandHandler(IGenericRepository<Core.Entities.Position, Guid> positionsRepository, IValidator<DeletePositionCommand> validator, ILogger<DeletePositionCommandHandler> logger)
    {
        _positionsRepository = positionsRepository;
        _validator = validator;
        _logger = logger;
    }

    public async Task<Result> Handle(DeletePositionCommand request, CancellationToken cancellationToken)
    {
        var validationResult = await _validator.ValidateAsync(request, cancellationToken);
        if (!validationResult.IsValid)
        {
            return Result.Fail(ErrorsFactory.FromValidationResult(nameof(Position), validationResult));
        }
        
        var position = await _positionsRepository.GetOneAsync(x => x.Id == request.Id, cancellationToken);

        if (position is null)
        {
            return Result.Fail(ErrorsFactory.NotFound(nameof(Position), request.Id));
        }
        
        await _positionsRepository.RemoveAsync(position, cancellationToken);
        
        return Result.Ok();
    }
}