using FluentResults;
using FluentValidation;
using MediatR;
using Microsoft.Extensions.Logging;
using TeamFlow.Identity.Application.Contracts.Features.Commands.Position;
using TeamFlow.Identity.Application.Errors;
using TeamFlow.Identity.Core.Exceptions;
using TeamFlow.Identity.Core.Repositories;
using TeamFlow.Identity.Core.Utils;

namespace TeamFlow.Identity.Application.Features.Commands.Position;

public class UpdatePositionCommandHandler : IRequestHandler<UpdatePositionCommand, Result>
{
    private readonly IGenericRepository<Core.Entities.Position, Guid> _positionsRepository;
    private readonly IValidator<UpdatePositionCommand> _validator;
    private readonly ILogger<UpdatePositionCommandHandler> _logger;


    public UpdatePositionCommandHandler(IGenericRepository<Core.Entities.Position, Guid> positionsRepository, IValidator<UpdatePositionCommand> validator, ILogger<UpdatePositionCommandHandler> logger)
    {
        _positionsRepository = positionsRepository;
        _validator = validator;
        _logger = logger;
    }

    public async Task<Result> Handle(UpdatePositionCommand request, CancellationToken cancellationToken)
    {
        var validationResult = await _validator.ValidateAsync(request, cancellationToken);
        if (!validationResult.IsValid)
        {
            return Result.Fail(ErrorsFactory.FromValidationResult(nameof(Position), validationResult));
        }
        
        var position = await _positionsRepository.GetOneAsync(x => x.Id == request.PositionId, cancellationToken);

        if (position is null) throw new NotFoundException(nameof(Position), request.PositionId);
        
        var isNameUpdated = ValueComparer.IsUpdated(request.Name, position.Name);

        if (isNameUpdated)
        {
            var idDuplicatePosition = await _positionsRepository
                .FindAnyAsync(p => p.Name == request.Name, cancellationToken);

            if (idDuplicatePosition)
            {
                return Result.Fail(ErrorsFactory.AlreadyExists(nameof(Position), nameof(request.Name), request.Name));
            }
        }

        position.Name = request.Name;
        position.UpdatedAt = DateTime.UtcNow;

        await _positionsRepository.UpdateAsync(position, cancellationToken);

        return Result.Ok();
    }
}