using FluentResults;
using FluentValidation;
using MediatR;
using Microsoft.Extensions.Logging;
using TeamFlow.Identity.Application.Contracts.Features.Commands.Position;
using TeamFlow.Identity.Application.Errors;
using TeamFlow.Identity.Core.Repositories;

namespace TeamFlow.Identity.Application.Features.Commands.Position;

public class CreatePositionCommandHandler : IRequestHandler<CreatePositionCommand, Result<Guid>>
{
    private readonly IGenericRepository<Core.Entities.Position, Guid> _positionsRepository;
    private readonly IValidator<CreatePositionCommand> _validator;
    private readonly ILogger<CreatePositionCommandHandler> _logger;

    public CreatePositionCommandHandler(IGenericRepository<Core.Entities.Position, Guid> positionsRepository, IValidator<CreatePositionCommand> validator, ILogger<CreatePositionCommandHandler> logger)
    {
        ArgumentNullException.ThrowIfNull(positionsRepository, nameof(positionsRepository));
        _positionsRepository = positionsRepository;
        _validator = validator;
        _logger = logger;
    }

    public async Task<Result<Guid>> Handle(CreatePositionCommand request, CancellationToken cancellationToken)
    {
        var validationResult = await _validator.ValidateAsync(request, cancellationToken);
        if (!validationResult.IsValid)
        {
            return Result.Fail(ErrorsFactory.FromValidationResult(nameof(Position), validationResult));
        }
        
        var isPositionExists = await _positionsRepository
            .FindAnyAsync(p => p.Name == request.Name, cancellationToken);

        if (isPositionExists)
        {
            return Result.Fail(ErrorsFactory.AlreadyExists(nameof(Position), nameof(request.Name), request.Name));
        }

        var position = new Core.Entities.Position
        {
            Id = Guid.CreateVersion7(),
            Name = request.Name,
        };
        
        await _positionsRepository.AddAsync(position, cancellationToken);

        return Result.Ok(position.Id);
    }
}