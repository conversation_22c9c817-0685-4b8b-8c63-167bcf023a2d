using FluentResults;
using FluentValidation;
using MediatR;
using Microsoft.Extensions.Logging;
using TeamFlow.Identity.Application.Contracts.Dto.ViewModels;
using TeamFlow.Identity.Application.Contracts.Features.Queries.User;
using TeamFlow.Identity.Application.Errors;
using TeamFlow.Identity.Application.Mapping;
using TeamFlow.Identity.Core.Repositories;

namespace TeamFlow.Identity.Application.Features.Queries.User;

public class GetUserByEmailQueryHandler : IRequestHandler<GetUserByEmailQuery, Result<UserViewModel>>
{
    private readonly IGenericRepository<Core.Entities.User, Guid> _usersRepository;
    private readonly IValidator<GetUserByEmailQuery> _validator;
    private readonly ILogger<GetUserByEmailQueryHandler> _logger;

    public GetUserByEmailQueryHandler(IGenericRepository<Core.Entities.User, Guid> usersRepository, IValidator<GetUserByEmailQuery> validator, ILogger<GetUserByEmailQueryHandler> logger)
    {
        _usersRepository = usersRepository;
        _validator = validator;
        _logger = logger;
    }

    public async Task<Result<UserViewModel>> Handle(GetUserByEmailQuery request, CancellationToken cancellationToken)
    {
        var validationResult = await _validator.ValidateAsync(request, cancellationToken);
        if (!validationResult.IsValid)
        {
            return Result.Fail(ErrorsFactory.FromValidationResult(nameof(Core.Entities.User), validationResult));
        }

        var user = await _usersRepository.GetOneAsync(x => x.Login == request.Email, cancellationToken);

        return user is null 
            ? Result.Fail(ErrorsFactory.NotFound(nameof(Core.Entities.User), request.Email)) 
            : Result.Ok(user.ToViewModel());
    }
}