using FluentResults;
using FluentValidation;
using MediatR;
using Microsoft.Extensions.Logging;
using TeamFlow.Identity.Application.Contracts.Dto.ViewModels;
using TeamFlow.Identity.Application.Contracts.Features.Queries.User;
using TeamFlow.Identity.Application.Errors;
using TeamFlow.Identity.Application.Mapping;
using TeamFlow.Identity.Core.Repositories;

namespace TeamFlow.Identity.Application.Features.Queries.User;

public class GetUserByLoginQueryHandler : IRequestHandler<GetUserByLoginQuery, Result<UserViewModel>>
{
    private readonly IGenericRepository<Core.Entities.User, Guid> _usersRepository;
    private readonly IValidator<GetUserByLoginQuery> _validator;
    private readonly ILogger<GetUserByLoginQueryHandler> _logger;

    public GetUserByLoginQueryHandler(IGenericRepository<Core.Entities.User, Guid> usersRepository, IValidator<GetUserByLoginQuery> validator, ILogger<GetUserByLoginQueryHandler> logger)
    {
        _usersRepository = usersRepository;
        _validator = validator;
        _logger = logger;
    }

    public async Task<Result<UserViewModel>> Handle(GetUserByLoginQuery request, CancellationToken cancellationToken)
    {
        var validationResult = await _validator.ValidateAsync(request, cancellationToken);
        if (!validationResult.IsValid)
        {
            return Result.Fail(ErrorsFactory.FromValidationResult(nameof(Core.Entities.User), validationResult));
        }
        
        var user = await _usersRepository.GetOneAsync(x => x.Login == request.Login, cancellationToken);

        if (user is null)
        {
            return Result.Fail(ErrorsFactory.NotFound(nameof(Core.Entities.User), request.Login));
        }
        return Result.Ok(user.ToViewModel());
    }
}