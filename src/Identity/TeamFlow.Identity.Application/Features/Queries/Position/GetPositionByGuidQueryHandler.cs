using FluentResults;
using FluentValidation;
using MediatR;
using Microsoft.Extensions.Logging;
using TeamFlow.Identity.Application.Contracts.Dto.ViewModels;
using TeamFlow.Identity.Application.Contracts.Features.Queries.Position;
using TeamFlow.Identity.Application.Errors;
using TeamFlow.Identity.Application.Mapping;
using TeamFlow.Identity.Core.Repositories;

namespace TeamFlow.Identity.Application.Features.Queries.Position;

public class GetPositionByGuidQueryHandler : IRequestHandler<GetPositionByIdQuery, Result<PositionViewModel>>
{
    private readonly IGenericRepository<Core.Entities.Position, Guid> _positionsRepository;
    private readonly IValidator<GetPositionByIdQuery> _validator;
    private readonly ILogger<GetPositionByGuidQueryHandler> _logger;

    public GetPositionByGuidQueryHandler(IGenericRepository<Core.Entities.Position, Guid> positionsRepository, IValidator<GetPositionByIdQuery> validator, ILogger<GetPositionByGuidQueryHandler> logger)
    {
        _positionsRepository = positionsRepository;
        _validator = validator;
        _logger = logger;
    }

    public async Task<Result<PositionViewModel>> Handle(GetPositionByIdQuery request, CancellationToken cancellationToken)
    {
        var validationResult = await _validator.ValidateAsync(request, cancellationToken);
        if (!validationResult.IsValid)
        {
            return Result.Fail(ErrorsFactory.FromValidationResult(nameof(Core.Entities.Position), validationResult));
        }
        
        var position = await _positionsRepository.GetOneAsync(x => x.Id == request.PositionId, cancellationToken);

        if (position is null)
        {
            return Result.Fail(ErrorsFactory.NotFound(nameof(Core.Entities.Position), request.PositionId));
        }

        return Result.Ok(position.ToViewModel());
    }
}