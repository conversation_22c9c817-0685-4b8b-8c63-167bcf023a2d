using FluentResults;
using MediatR;
using Microsoft.Extensions.Logging;
using TeamFlow.Identity.Application.Contracts.Dto.ViewModels;
using TeamFlow.Identity.Application.Contracts.Features.Queries.Position;
using TeamFlow.Identity.Application.Errors;
using TeamFlow.Identity.Application.Mapping;
using TeamFlow.Identity.Core.Repositories;
using TeamFlow.Shared.Contracts.Pagination;
using TeamFlow.Shared.Contracts.Sorting;

namespace TeamFlow.Identity.Application.Features.Queries.Position;

public class GetPositionsQueryHandler : IRequestHandler<GetPositionsQuery, Result<Page<PositionViewModel>>>
{
    private readonly IGenericRepository<Core.Entities.Position, Guid> _positionsRepository;
    private readonly ILogger<GetPositionsQueryHandler> _logger;

    public GetPositionsQueryHandler(IGenericRepository<Core.Entities.Position, Guid> positionsRepository,
        ILogger<GetPositionsQueryHandler> logger)
    {
        _positionsRepository = positionsRepository;
        _logger = logger;
    }

    public async Task<Result<Page<PositionViewModel>>> Handle(GetPositionsQuery request,
        CancellationToken cancellationToken)
    {
        var filter = request.Filter?.ToPredicate();

        var sort = request.Sorting is not null
            ? request.Sorting.ToOrderByExpression()
            : new OrderByExpression<Core.Entities.Position>(t => t.CreatedAt);

        var pagedResult = await _positionsRepository.GetManyAsync(request.Pageable, filter, sort, cancellationToken);
        
        if (pagedResult.Elements is null)
        {
            return Result.Fail(ErrorsFactory.InternalServerError("Ошибка при получении данных"));
        }
        
        // var attachments = _mapper.MapToModel(pagedResult.Elements);
        var positions = pagedResult.Elements.Select(x => x.ToViewModel()).ToList();

        if (positions.Count == 0)
        {
            return Result.Fail(ErrorsFactory.NotFound(nameof(Position), "Должности не найдены"));
        }

        return Result.Ok(new Page<PositionViewModel>(positions, pagedResult.Count, request.Pageable));
    }
}