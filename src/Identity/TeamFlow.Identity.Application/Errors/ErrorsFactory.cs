using System.Buffers;
using System.Net;
using FluentResults;
using FluentValidation.Results;
using TeamFlow.Tasks.Core.Shared.Errors;
using TeamFlow.Tasks.Core.Shared.Errors.Base;
using TeamFlow.Tasks.Core.Shared.Exceptions;

namespace TeamFlow.Identity.Application.Errors;

/// <summary>
/// Универсальная фабрика для создания типизированных ошибок
/// </summary>
public static class ErrorsFactory
{
    /// <summary>
    /// Создает ошибку "Не найдено" для указанной сущности
    /// </summary>
    /// <param name="entityType">Тип сущности</param>
    /// <param name="id">Идентификатор сущности</param>
    /// <returns>Ошибка типа NotFound</returns>
    public static Error NotFound(string entityType, object id) =>
        new NotFoundError($"{entityType} с идентификатором {id} не найден")
            .WithMetadata("EntityId", id.ToString())
            .WithMetadata("EntityType", entityType)
            .WithMetadata("ErrorCode", "NotFound");
    
    /// <summary>
    /// Создает ошибку "Не найдено" с произвольным сообщением
    /// </summary>
    /// <param name="entityType">Тип сущности</param>
    /// <param name="message">Сообщение об ошибке</param>
    /// <returns>Ошибка типа NotFound</returns>
    public static Error NotFound(string entityType, string message) =>
        new NotFoundError(message)
            .WithMetadata("EntityType", entityType)
            .WithMetadata("ErrorCode", "NotFound");
    
    /// <summary>
    /// Создает ошибку "Конфликт" для случаев, когда сущность уже существует
    /// </summary>
    /// <param name="entityType">Тип сущности</param>
    /// <param name="propertyName">Название свойства, вызвавшего конфликт</param>
    /// <param name="propertyValue">Значение свойства, вызвавшего конфликт</param>
    /// <returns>Ошибка типа Conflict</returns>
    public static Error AlreadyExists(string entityType, string propertyName, string propertyValue) =>
        new ConflictError($"{entityType} с {propertyName} '{propertyValue}' уже существует")
            .WithMetadata(propertyName, propertyValue)
            .WithMetadata("EntityType", entityType)
            .WithMetadata("ErrorCode", "Conflict");
    
    /// <summary>
    /// Создает ошибку "Конфликт" с произвольным сообщением
    /// </summary>
    /// <param name="entityType">Тип сущности</param>
    /// <param name="message">Сообщение об ошибке</param>
    /// <param name="metadata">Дополнительные метаданные (ключ-значение)</param>
    /// <returns>Ошибка типа Conflict</returns>
    public static Error Conflict(string entityType, string message, params (string Key, string Value)[] metadata)
    {
        var error = new ConflictError(message)
            .WithMetadata("EntityType", entityType)
            .WithMetadata("ErrorCode", "Conflict");
            
        foreach (var (key, value) in metadata)
        {
            error = error.WithMetadata(key, value);
        }
        
        return error;
    }
    
    /// <summary>
    /// Создает ошибку валидации для указанного свойства
    /// </summary>
    /// <param name="entityType">Тип сущности</param>
    /// <param name="propertyName">Название свойства</param>
    /// <param name="message">Сообщение об ошибке</param>
    /// <returns>Ошибка типа Validation</returns>
    public static Error ValidationError(string entityType, string propertyName, string message) =>
        new ValidationError(propertyName, message)
            .WithMetadata("EntityType", entityType)
            .WithMetadata("ErrorCode", "BadRequest");
    
    /// <summary>
    /// Преобразует ошибку FluentValidation в ошибку FluentResults
    /// </summary>
    /// <param name="entityType">Тип сущности</param>
    /// <param name="failure">Ошибка FluentValidation</param>
    /// <returns>Ошибка типа Validation</returns>
    public static Error FromValidationFailure(string entityType, ValidationFailure failure) =>
        new ValidationError(failure.PropertyName, failure.ErrorMessage)
            .WithMetadata("EntityType", entityType)
            .WithMetadata("AttemptedValue", failure.AttemptedValue)
            .WithMetadata("ErrorCode", "BadRequest");
    
    /// <summary>
    /// Преобразует результат валидации FluentValidation в список ошибок FluentResults
    /// </summary>
    /// <param name="entityType">Тип сущности</param>
    /// <param name="validationResult">Результат валидации</param>
    /// <returns>Список ошибок</returns>
    public static List<Error> FromValidationResult(string entityType, ValidationResult validationResult) =>
        validationResult.Errors
            .Select(failure => FromValidationFailure(entityType, failure))
            .ToList();
    
    /// <summary>
    /// Создает ошибку "Неверный запрос"
    /// </summary>
    /// <param name="entityType">Тип сущности</param>
    /// <param name="message">Сообщение об ошибке</param>
    /// <returns>Ошибка типа BadRequest</returns>
    public static Error BadRequest(string entityType, string message) =>
        new BadRequestError(message)
            .WithMetadata("EntityType", entityType)
            .WithMetadata("ErrorCode", "BadRequest");

    public static Error Unauthorized(string message)
        => new UnauthorizedError(message).WithMetadata("EntityType", "User")
            .WithMetadata("ErrorCode", "Unauthorized");
    
    public static Error InternalServerError(string message, params (string Key, string Value)[] metadata)
    {
        var error = new Error(message)
            .WithMetadata("StatusCode", (int)HttpStatusCode.InternalServerError)
            .WithMetadata("ErrorCode", "InternalServerError");

        foreach (var (key, value) in metadata)
        {
            error = error.WithMetadata(key, value);
        }
        
        return error;
    }
    
    public static Error InternalServerError(ReadOnlySpan<char> message, params (string Key, string Value)[] metadata)
    {
        // Арендуем буфер для временного хранения message
        var buffer = ArrayPool<char>.Shared.Rent(1024); // Размер можно настроить
        try
        {
            var span = buffer.AsSpan();
            var offset = 0;

            // Копируем message в буфер
            message.CopyTo(span.Slice(offset));
            var messageSpan = span.Slice(offset, message.Length);
            offset += message.Length + 1; // +1 для разделителя

            // Создаем Error с message
            var error = new Error(messageSpan.ToString())
                .WithMetadata("StatusCode", ((int)HttpStatusCode.InternalServerError).ToString())
                .WithMetadata("ErrorCode", "InternalServerError");

            // Добавляем метаданные
            foreach (var (key, value) in metadata)
            {
                error = error.WithMetadata(key, value);
            }

            return error;
        }
        finally
        {
            ArrayPool<char>.Shared.Return(buffer);
        }
    }

    /// <summary>
    /// Создает произвольную ошибку с указанным HTTP-статусом
    /// </summary>
    /// <param name="message">Сообщение об ошибке</param>
    /// <param name="statusCode">HTTP-статус</param>
    /// <param name="metadata">Дополнительные метаданные (ключ-значение)</param>
    /// <returns>Ошибка</returns>
    public static Error Custom(string message, HttpStatusCode statusCode, params (string Key, string Value)[] metadata)
    {
        var error = new Error(message)
            .WithMetadata("StatusCode", (int)statusCode)
            .WithMetadata("ErrorCode", statusCode.ToString());
            
        foreach (var (key, value) in metadata)
        {
            error = error.WithMetadata(key, value);
        }
        
        return error;
    }
}