using TeamFlow.Identity.Application.Contracts.Dto.ViewModels;
using TeamFlow.Identity.Core.Entities;

namespace TeamFlow.Identity.Application.Mapping;

public static class UserQueryMapper
{
    public static UserViewModel ToViewModel(this User user)
    {
        List<Guid> skillsIds = [];

        if (user.UserSkills != null && user.UserSkills.Count != 0)
        {
            // skills.AddRange(user.UserSkills.Select(skill => new UserSkillViewModel
            // {
            //     SkillId = skill.SkillId, 
            //     Name = skill.Skill.Name,
            //     Category = skill.Skill.Category,
            //     Level = skill.Level, 
            //     YearsExperience = skill.YearsExperience,
            //     IsPrimary = skill.IsPrimary
            // }));
            skillsIds.AddRange(user.UserSkills.Select(x => x.SkillId));
        }


        return new UserViewModel
        {
            Id = user.Id,
            Login = user.Login,
            Email = user.Email,
            FirstName = user.FirstName,
            LastName = user.LastName,
            AvatarUrl = user.AvatarUrl,
            OnlineStatus = user.OnlineStatus,
            Role = user.Role,
            LastLoginAt = user.LastLoginAt,
            CreatedAt = user.CreatedAt,
            UpdatedAt = user.UpdatedAt,
            PositionId = user.PositionId,
            SkillIds = skillsIds
        };
    }
}