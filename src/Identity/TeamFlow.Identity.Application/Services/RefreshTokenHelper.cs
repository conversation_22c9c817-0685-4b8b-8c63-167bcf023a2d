using System.Security.Claims;
using FluentResults;
using TeamFlow.Identity.Application.Contracts.Services;
using TeamFlow.Identity.Application.Errors;
using TeamFlow.Identity.Application.Services.Interfaces;
using TeamFlow.Identity.Core.Entities;
using TeamFlow.Identity.Core.Errors;
using TeamFlow.Identity.Core.Repositories;
using TeamFlow.Shared.Contracts.Enums;
using TeamFlow.Shared.Contracts.Extensions;

namespace TeamFlow.Identity.Application.Services;

public class RefreshTokenHelper : IRefreshTokenHelper
{
    private readonly IJwtService _jwtService;
    private readonly IHashDataService _hashDataService;
    private readonly IGenericRepository<User, Guid> _usersRepository;

    public RefreshTokenHelper(
        IJwtService jwtService,
        IHashDataService hashDataService,
        IGenericRepository<User, Guid> usersRepository)
    {
        _jwtService = jwtService;
        _hashDataService = hashDataService;
        _usersRepository = usersRepository;
    }

    public async Task<Result<User>> GetUserFromRefreshTokenAsync(string refreshToken,
        CancellationToken cancellationToken = default)
    {
        if (string.IsNullOrWhiteSpace(refreshToken))
            return Result.Fail(AuthErrors.InvalidRefreshToken);

        var principalResult = ValidateTokenAndCheckType(refreshToken);
        if (principalResult.IsFailed)
            return Result.Fail(principalResult.Errors);

        var userId = principalResult.Value.GetUserId();

        var user = await _usersRepository.GetOneAsync(x => x.Id == userId, cancellationToken);
        if (user is null)
            return Result.Fail(ErrorsFactory.NotFound(nameof(User), userId));

        var sessionResult = ValidateUserSessionByToken(user, refreshToken);
        return sessionResult.IsFailed ? Result.Fail(sessionResult.Errors) : Result.Ok(user);
    }

    public async Task<Result<UserSession>> GetUserSessionByTokenAsync(Guid userId, string token,
        CancellationToken cancellationToken = default)
    {
        if (string.IsNullOrWhiteSpace(token))
            return Result.Fail(AuthErrors.InvalidRefreshToken);

        var principalResult = ValidateToken(token);
        if (principalResult.IsFailed)
            return Result.Fail(principalResult.Errors);

        var user = await _usersRepository.GetOneAsync(x => x.Id == userId, cancellationToken);
        if (user is null)
            return Result.Fail(ErrorsFactory.NotFound(nameof(User), userId));

        var sessionResult = ValidateUserSessionByToken(user, token);
        return sessionResult.IsFailed ? Result.Fail(sessionResult.Errors) : Result.Ok(sessionResult.Value);
    }

    private Result<ClaimsPrincipal> ValidateToken(string token)
    {
        var principal = _jwtService.ValidateToken(token);
        return principal is null ? Result.Fail(AuthErrors.InvalidRefreshToken) : Result.Ok(principal);
    }

    private Result<ClaimsPrincipal> ValidateTokenAndCheckType(string token)
    {
        var principalResult = ValidateToken(token);
        if (principalResult.IsFailed)
            return principalResult;

        var principal = principalResult.Value;
        var tokenType = principal.GetTokenType();

        return tokenType != nameof(JwtClaimValue.Refresh) ? Result.Fail(ErrorsFactory.Unauthorized(AuthErrors.InvalidRefreshToken)) : Result.Ok(principal);
    }

    private Result<UserSession> ValidateUserSessionByToken(User user, string token)
    {
        foreach (var session in user.Sessions.Where(session => _hashDataService.VerifyData(token, session.HashedToken)))
        {
            return Result.Ok(session);
        }

        return Result.Fail(ErrorsFactory.Unauthorized(AuthErrors.InvalidRefreshToken));
    }
}