using TeamFlow.Identity.Application.Contracts.Services;
using TeamFlow.Identity.Application.Services.Interfaces;

namespace TeamFlow.Identity.Application.Services;

public class HashDataService : IHashDataService
{
    private readonly int _workFactor;

    public HashDataService(int workFactor)
    {
        if (workFactor <=  0) throw new ArgumentException("Work factor must be greater than 0");

        _workFactor = workFactor;
    }
    
    public string HashData(string data)
    {
        ArgumentNullException.ThrowIfNull(data);

        if (string.IsNullOrWhiteSpace(data)) throw new ArgumentException($"{nameof(data)} is required");

        return BCrypt.Net.BCrypt.HashPassword(data, _workFactor);
    }

    public string EnhanceHashData(string data)
    {
        ArgumentNullException.ThrowIfNull(data);

        if (string.IsNullOrWhiteSpace(data)) throw new ArgumentException($"{nameof(data)} is required");

        return BCrypt.Net.BCrypt.EnhancedHashPassword(data, _workFactor);
    }

    public bool VerifyData(string data, string hash)
    {
        ArgumentNullException.ThrowIfNull(data);
        ArgumentNullException.ThrowIfNull(hash);

        return BCrypt.Net.BCrypt.Verify(data, hash);
    }

    public bool VerifyEnhancedData(string data, string hash)
    {
        ArgumentNullException.ThrowIfNull(data);
        ArgumentNullException.ThrowIfNull(hash);

        return BCrypt.Net.BCrypt.EnhancedVerify(data, hash);
    }
}