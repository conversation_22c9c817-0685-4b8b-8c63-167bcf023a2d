<Project Sdk="Microsoft.NET.Sdk">

    <PropertyGroup>
        <TargetFramework>net9.0</TargetFramework>
        <ImplicitUsings>enable</ImplicitUsings>
        <Nullable>enable</Nullable>
    </PropertyGroup>

    <ItemGroup>
      <PackageReference Include="Microsoft.EntityFrameworkCore" Version="9.0.2" />
      <PackageReference Include="Microsoft.EntityFrameworkCore.Tools" Version="9.0.2">
        <PrivateAssets>all</PrivateAssets>
        <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
      </PackageReference>
      <PackageReference Include="MongoDB.Driver" Version="3.2.0" />
      <PackageReference Include="Npgsql.EntityFrameworkCore.PostgreSQL" Version="9.0.3" />
    </ItemGroup>

    <ItemGroup>
      <ProjectReference Include="..\TeamFlow.Identity.Application\TeamFlow.Identity.Application.csproj" />
    </ItemGroup>

</Project>
