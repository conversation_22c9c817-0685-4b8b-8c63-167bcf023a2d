using Microsoft.EntityFrameworkCore.Storage;
using TeamFlow.Shared.Repositories.Repositories.Common;

namespace TeamFlow.Identity.Persistence.Database;

public class Transaction : ITransaction
{
    private readonly IDbContextTransaction _dbContextTransaction;
    private bool _disposed = false;

    public Transaction(IDbContextTransaction dbContextTransaction)
    {
        ArgumentNullException.ThrowIfNull(dbContextTransaction);
        _dbContextTransaction = dbContextTransaction;
    }

    public async Task CommitAsync(CancellationToken cancellationToken = default)
        => await _dbContextTransaction.CommitAsync(cancellationToken);


    public async Task RollbackAsync(CancellationToken cancellationToken = default)
        => await _dbContextTransaction.RollbackAsync(cancellationToken);

    public void Dispose() => _dbContextTransaction.Dispose();

    public async ValueTask DisposeAsync() => await _dbContextTransaction.DisposeAsync();
}