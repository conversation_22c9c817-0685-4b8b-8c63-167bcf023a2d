using Microsoft.EntityFrameworkCore;
using TeamFlow.Identity.Core.Entities;
using TeamFlow.Identity.Core.Enums;
using TeamFlow.Identity.Core.Enums.Settings;
using TeamFlow.Identity.Persistence.Database.Config;
using TeamFlow.Shared.Repositories.Entities;
using UserRole = TeamFlow.Shared.Contracts.Enums.UserRole;

namespace TeamFlow.Identity.Persistence.Database;

/*
 * TODO [PERSISTENCE]
 * Нужно добавить HasMaxLenght и прочие настройки в конфигах.
 * Для прокадшена
 */
public class ApplicationDbContext : DbContext
{
    public DbSet<User> Users { get; private set; }

    public DbSet<Position> Positions { get; private set; }

    public ApplicationDbContext(DbContextOptions<ApplicationDbContext> options) : base(options)
    {
    }

    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {
        modelBuilder.ApplyConfiguration(new UsersConfiguration());
        modelBuilder.ApplyConfiguration(new PositionsConfiguration());
        modelBuilder.ApplyConfiguration(new UserSettingsConfiguration());
        modelBuilder.ApplyConfiguration(new SkillsConfiguration());
        modelBuilder.ApplyConfiguration(new UserSkillConfiguration());
        
        var systemAdminGuid = new Guid("11111111-1111-1111-1111-111111111111");
        var userAdminGuid = new Guid("*************-2222-2222-************");
        var defaultSkillGuid = new Guid("01974584-eb10-7505-82a2-b11585b61a74");

        var adminAvatar = $"https://api.dicebear.com/7.x/avataaars/svg?seed={systemAdminGuid}";

        var createdAt = new DateTime(2025, 2, 21, 0, 0, 0, DateTimeKind.Utc);
        
        modelBuilder.Entity<Position>().HasData(new Position
        {
            Id = systemAdminGuid,
            Name = "System Administrator",
            CreatedAt = createdAt,
        });

        modelBuilder.Entity<Skill>().HasData(new Skill
        {
            Id = defaultSkillGuid,
            Name = "Admin",
            Category = "Admin",
            CreatedAt = createdAt
        });
        
        /*
         * The Original Password is admin12345
         * Hashed with BCrypt work factor 9
         */
        const string adminPasswordHash = "$2a$09$jIqGzmvhNyBGmqJgs.qcAuuntFF8jmK1sacrwbmvmLm4jwv0GITuC";

        modelBuilder.Entity<User>().HasData(new User
        {
            Id = userAdminGuid,
            Login = "rootwraith",
            FirstName = "Systemd",
            LastName = "Daemon",
            PasswordHash = adminPasswordHash,
            Role = UserRole.Admin,
            Email = "<EMAIL>",
            CreatedAt = createdAt,
            PositionId = systemAdminGuid,
            AvatarUrl = adminAvatar
        });

        modelBuilder.Entity<UserSkill>().HasData(new UserSkill
        {
            UserId = userAdminGuid,
            SkillId = defaultSkillGuid,
            Level = SkillLevel.Expert,
            YearsExperience = 10000,
            IsPrimary = true
        });

        modelBuilder.Entity<UserSettings>().HasData(new UserSettings
        {
            UserId = userAdminGuid,
            // Id = new Guid("0197455d-3d19-7eae-8924-2db4967e25d0"),
            EmailNotification = false,
            SystemNotification = false,
            EventReminder = false,
            PanelMode = SidebarPanelMode.Extended,
            UiTheme = UiTheme.System,
            Language = Language.Ru
            // CreatedAt = createdAt,
        });
    }

    // protected override void OnConfiguring(DbContextOptionsBuilder optionsBuilder) =>
    //     optionsBuilder.UseNpgsql(_connectionString);

    public override int SaveChanges()
    {
        UpdateTimeStamps();
        return base.SaveChanges();
    }

    public override async Task<int> SaveChangesAsync(CancellationToken cancellationToken = default)
    {
        UpdateTimeStamps();
        return await base.SaveChangesAsync(cancellationToken);
    }

    private void UpdateTimeStamps()
    {
        var entries = ChangeTracker
            .Entries<ITrackable>()
            .Where(e => 
                e.State is EntityState.Modified or EntityState.Added);

        foreach (var entry in entries)
        {
            //Wtf?
            // if (entry.State is EntityState.Added)
            // {
            //     entry.Entity.CreatedAt = DateTime.UtcNow;
            // }

            entry.Entity.UpdatedAt = DateTime.UtcNow;
        }
    }
}