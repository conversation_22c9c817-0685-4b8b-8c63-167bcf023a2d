using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using TeamFlow.Identity.Core.Entities;
using TeamFlow.Identity.Persistence.Extensions;

namespace TeamFlow.Identity.Persistence.Database.Config;

internal class UsersConfiguration : IEntityTypeConfiguration<User>
{
    public void Configure(EntityTypeBuilder<User> builder)
    {
        builder.HasKey(u => u.Id);

        builder
            .HasOne<Position>()
            .WithMany(p => p.Users)
            .HasForeignKey(u => u.PositionId);
        
        builder.HasOne(u => u.Settings)
            .WithOne(s => s.User)
            .HasForeignKey<UserSettings>(s => s.UserId)
            .OnDelete(DeleteBehavior.Cascade);

        builder.HasIndex(u => u.Role);

        builder.Property(u => u.Login)
            .HasMaxLength(User.MaxLoginLength)
            .IsRequired();

        builder.Property(u => u.FirstName).IsRequired();
        builder.Property(u => u.LastName).IsRequired();

        builder.Property(u => u.Email)
            .HasMaxLength(User.MaxEmailLength)
            .IsRequired();

        builder.Property(u => u.Role)
            .IsRequired();

        builder.HasIndex(u => u.Role);
        builder.HasIndex(u => u.FirstName);
        builder.HasIndex(u => u.LastName);
        builder.HasIndex(u => u.PositionId);
        builder.HasIndex(u => u.CreatedAt);

        builder
            .HasIndex(u => u.Login)
            .IsUnique();

        builder
            .HasIndex(u => u.Email)
            .IsUnique();

        builder.Property(u => u.Id)
            .HasValueGenerator<GuidV7ValueGenerator>();
    }
}