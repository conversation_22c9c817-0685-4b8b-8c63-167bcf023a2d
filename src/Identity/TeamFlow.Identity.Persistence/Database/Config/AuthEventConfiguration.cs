using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using TeamFlow.Identity.Core.Entities;

namespace TeamFlow.Identity.Persistence.Database.Config;

public class AuthEventConfiguration : IEntityTypeConfiguration<AuthEvent>
{
    public void Configure(EntityTypeBuilder<AuthEvent> builder)
    {
        builder.HasKey(e => e.Id);
            
        builder.HasIndex(e => e.SessionId);
        // builder.HasIndex(e => new { e.UserId, e.LastActivity });
            
        builder.Property(e => e.IpAddress)
            .HasMaxLength(AuthEvent.MaxIpAddressLength)
            .IsRequired();
                
        builder.Property(e => e.Country)
            .HasMaxLength(AuthEvent.MaxCountryLength);
                
        builder.Property(e => e.City)
            .HasMaxLength(AuthEvent.MaxCityLength);
                
        // Связь с User
        builder.HasOne(e => e.User)
            .WithMany(u => u.AuthEvents)
            .HasForeignKey(e => e.UserId)
            .OnDelete(DeleteBehavior.Cascade);
    }
}