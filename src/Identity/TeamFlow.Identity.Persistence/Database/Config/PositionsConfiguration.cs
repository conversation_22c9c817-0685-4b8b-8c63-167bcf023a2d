using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using TeamFlow.Identity.Core.Entities;

namespace TeamFlow.Identity.Persistence.Database.Config;

internal class PositionsConfiguration : IEntityTypeConfiguration<Position>
{
    public void Configure(EntityTypeBuilder<Position> builder)
    {
        builder.<PERSON><PERSON>ey(p => p.Id);
            
        builder
            .HasMany(p => p.Users)
            .WithOne(u => u.Position)
            .HasForeignKey(u => u.PositionId)
            .OnDelete(DeleteBehavior.SetNull);

        builder
            .Property(p => p.Name)
            .HasMaxLength(Position.MaxNameLength)
            .IsRequired();

        builder
            .HasIndex(p => p.Name)
            .IsUnique();

        builder.HasIndex(p => p.CreatedAt);
    }
}