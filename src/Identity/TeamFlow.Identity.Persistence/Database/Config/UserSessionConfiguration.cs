using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using TeamFlow.Identity.Core.Entities;

namespace TeamFlow.Identity.Persistence.Database.Config;

public class UserSessionConfiguration : IEntityTypeConfiguration<UserSession>
{
    public void Configure(EntityTypeBuilder<UserSession> builder)
    {
        builder.<PERSON><PERSON><PERSON>(e => e.Id);
            
        builder.HasIndex(e => e.UserId);
        builder.HasIndex(e => e.ExpiresAt);
        builder.HasIndex(e => e.HashedToken).IsUnique();
            
        builder.Property(e => e.HashedToken).IsRequired();
            
        // Связь с User
        builder.HasMany(e => e.AuthEvents)
            .WithOne(u => u.Session)
            .HasForeignKey(e => e.SessionId)
            .OnDelete(DeleteBehavior.Cascade);
    }
}