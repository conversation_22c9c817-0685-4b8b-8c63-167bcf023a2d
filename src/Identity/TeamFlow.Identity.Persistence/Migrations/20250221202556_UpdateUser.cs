using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace TeamFlow.Identity.Persistence.Migrations
{
    /// <inheritdoc />
    public partial class UpdateUser : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.UpdateData(
                table: "Users",
                keyColumn: "Sub",
                keyValue: new Guid("*************-2222-2222-************"),
                column: "PasswordHash",
                value: "$2a$09$jIqGzmvhNyBGmqJgs.qcAuuntFF8jmK1sacrwbmvmLm4jwv0GITuC");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.UpdateData(
                table: "Users",
                keyColumn: "Sub",
                keyValue: new Guid("*************-2222-2222-************"),
                column: "PasswordHash",
                value: "$2a$12$apke/jEzk<PERSON>ysKUtgVBt87e2CeGsK434K8T/sk8JhlDa0JBewfP5mm");
        }
    }
}
