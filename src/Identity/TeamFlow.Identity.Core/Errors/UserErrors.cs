namespace TeamFlow.Identity.Core.Errors;

public static class UserErrors
{
    public const string AlreadyExists = "User already exists";
    public const string NotFound = "User not found";
    public const string InvalidCredentials = "Invalid credentials";
    public const string EmailAlreadyInUse = "Email already in use";
    public const string LoginOrEmailAlreadyTaken = "The login or email is already taken.";
}