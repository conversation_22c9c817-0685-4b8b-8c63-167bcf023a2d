namespace TeamFlow.Identity.Core.Errors;

public static class AuthErrors
{
    public const string InvalidPassword = "Invalid password";
    public const string InvalidRefreshToken = "Invalid refresh token";
    public const string InvalidUserIdInToken = "Invalid user id in token";
	
	public static void Test(object a, object b)
	{
		if (a == b)
		{
			Console.WriteLine("equals");
		}
		
		if (a == b)
			Console.WriteLine("equals");
	}
}