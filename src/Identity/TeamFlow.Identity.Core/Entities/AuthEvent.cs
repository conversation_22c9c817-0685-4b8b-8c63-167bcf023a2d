using System.ComponentModel.DataAnnotations;
using TeamFlow.Identity.Core.Enums.Jwt;
using TeamFlow.Shared.Repositories.Entities;

namespace TeamFlow.Identity.Core.Entities;

public class AuthEvent : Entity<Guid>
{
    public const byte MaxIpAddressLength = 45;
    
    public Guid UserId { get; set; }
    
    public AuthEvenType EventType { get; set; }
    
    // IPv6 поддержка
    public string IpAddress { get; set; }
    
    [MaxLength(2)]
    public string? Country { get; set; } // RU, US
    
    [MaxLength(100)]
    public string? City { get; set; }
    
    public string? UserAgent { get; set; } // Полный User-Agent

     
    [MaxLength(50)]
    public string? DeviceType { get; set; } // mobile, desktop, tablet
    
    [MaxLength(100)]
    public string? DeviceName { get; set; } // "iPhone 15", "Chrome on Windows"
    
    [MaxLength(50)]
    public string? OsName { get; set; } // iOS, Windows, Android, macOS
    
    [MaxLength(20)]
    public string? OsVersion { get; set; } // 17.0, Windows 11
    
    [MaxLength(50)]
    public string? BrowserName { get; set; } // Chrome, Safari, Firefox
    
    [MaxLength(20)]
    public string? BrowserVersion { get; set; } // 120
    
    // === Безопасность ===
    public bool IsSuspicious { get; set; } = false; // Подозрительная активность
    
    // [MaxLength(50)]
    // public string? LoginMethod { get; set; } // password, oauth_google, 2fa
    
    public DateTime LastActivity { get; set; }
    public bool IsActive { get; set; }
}