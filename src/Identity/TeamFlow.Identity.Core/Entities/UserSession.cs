using TeamFlow.Identity.Core.Enums;
using TeamFlow.Shared.Repositories.Entities;

namespace TeamFlow.Identity.Core.Entities;

public class UserSession : Entity<Guid>
{
    public Guid UserId { get; init; }
    
    public required string HashedToken { get; set; }
    public DateTime ExpiresAt { get; set; }

    public bool IsActive { get; set; } = true;
    public DateTime? LastActivity { get; set; } = DateTime.UtcNow;
    public DateTime? TerminatedAt { get; set; }
    public SessionTerminationReason TerminationReason { get; set; }
    
    public User User { get; init; } = null!;
    public List<AuthEvent> AuthEvents { get; set; } = [];
}