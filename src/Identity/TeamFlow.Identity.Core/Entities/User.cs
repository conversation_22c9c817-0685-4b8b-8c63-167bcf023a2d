using TeamFlow.Identity.Core.Enums;
using TeamFlow.Identity.Core.Enums.Users;
using TeamFlow.Shared.Repositories.Entities;
using UserRole = TeamFlow.Shared.Contracts.Enums.UserRole;

namespace TeamFlow.Identity.Core.Entities;

public class User : Entity<Guid>
{
    public const byte MaxEmailLength = 255;
    
    public const byte MaxLoginLength = 50;

    public const byte MinLoginLength = 5;
    
    public const byte MinPasswordLength = 8;
    
    public const byte MaxPasswordLength = 30;

    public required string Login { get; set; }
    
    public required string FirstName { get; set; }
    
    public required string LastName { get; set; }

    public required string Email { get; set; }

    public required string PasswordHash { get; set; }

    public string? AvatarUrl { get; set; }

    public UserOnlineStatus OnlineStatus { get; set; }

    public required UserRole Role { get; set; }
    
    public DateTime? LastPasswordChangeAt { get; set; }

    public Guid PositionId { get; set; }
    
    public Position Position { get; set; }
    public UserSettings? Settings { get; set; }
    public List<UserSkill>? UserSkills { get; set; } = [];
    
    public List<AuthEvent> AuthEvents { get; set; } = [];
    public List<UserSession> Tokens { get; set; } = [];
}