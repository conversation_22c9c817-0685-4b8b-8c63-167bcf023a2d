<Project Sdk="Microsoft.NET.Sdk">

    <PropertyGroup>
        <TargetFramework>net9.0</TargetFramework>
        <ImplicitUsings>enable</ImplicitUsings>
        <Nullable>enable</Nullable>
    </PropertyGroup>

    <ItemGroup>
      <PackageReference Include="FluentResults" Version="3.16.0" />
    </ItemGroup>

    <ItemGroup>
      <ProjectReference Include="..\..\..\TeamFlow.Shared.Contracts\TeamFlow.Shared.Contracts.csproj" />
      <ProjectReference Include="..\..\..\TeamFlow.Shared.Repositories\TeamFlow.Shared.Repositories.csproj" />
      <ProjectReference Include="..\..\..\TeamFlow.Shared.Utils\TeamFlow.Shared.Utils.csproj" />
    </ItemGroup>

</Project>
