namespace TeamFlow.Tasks.Application.Contracts.Contracts.ViewModels;

public record AttachmentViewModel
{
    public Guid TaskId { get; init; }

    public string? FileName { get; init; }

    public string? OriginalFileName { get; init; }

    public string ContentType { get; init; } = string.Empty;

    public Guid UploaderId { get; init; }

    public ulong FileSize { get; init; } = ulong.MinValue;

    public DateTime CreatedAt { get; init; }

    public DateTime UpdatedAt { get; init; }
}