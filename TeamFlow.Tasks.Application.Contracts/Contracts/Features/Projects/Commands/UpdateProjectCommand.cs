using FluentResults;
using MediatR;
using TeamFlow.Tasks.Core.Shared.Enums;

namespace TeamFlow.Tasks.Application.Contracts.Contracts.Features.Projects.Commands;

public record UpdateProjectCommand(
    Guid Id,
    string? Name,
    string? Description,
    Guid? OwnerId,
    DateTime? StartDate,
    DateTime? EndDate,
    PriorityLevel? PriorityLevel,
    ProjectStatues? Status)
    : IRequest<Result<Guid>>;