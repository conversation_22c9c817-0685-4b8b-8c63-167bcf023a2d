using FluentResults;
using MediatR;
using TeamFlow.Shared.Contracts.Pagination;
using TeamFlow.Tasks.Application.Contracts.Contracts.ViewModels;

namespace TeamFlow.Tasks.Application.Contracts.Contracts.Features.Comment.Queries;

public record GetCommentsByTaskIdQuery(Guid TaskId) : IRequest<Result<IReadOnlyCollection<CommentViewModel>>>;

public record GetCommentsByTaskIdPageableQuery(Pageable Pageable, Guid TaskId) : IRequest<Result<Page<CommentViewModel>>>;