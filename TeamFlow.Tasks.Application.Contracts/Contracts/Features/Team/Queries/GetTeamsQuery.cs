using FluentResults;
using MediatR;
using TeamFlow.Shared.Contracts.Pagination;
using TeamFlow.Tasks.Application.Contracts.Contracts.Filtering.Team;
using TeamFlow.Tasks.Application.Contracts.Contracts.ViewModels;

namespace TeamFlow.Tasks.Application.Contracts.Contracts.Features.Team.Queries;

public record GetTeamsQuery(Pageable Pageable, TeamFilterCriteria? Filter, TeamSortingCriteria? Sorting) : IRequest<Result<Page<TeamViewModel>>>;