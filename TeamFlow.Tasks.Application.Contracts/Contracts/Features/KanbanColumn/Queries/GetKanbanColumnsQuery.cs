using FluentResults;
using MediatR;
using TeamFlow.Shared.Contracts.Pagination;
using TeamFlow.Tasks.Application.Contracts.Contracts.Filtering.KanbanColumn;
using TeamFlow.Tasks.Application.Contracts.Contracts.ViewModels;

namespace TeamFlow.Tasks.Application.Contracts.Contracts.Features.KanbanColumn.Queries;

public record GetKanbanColumnsQuery(
    Pageable Pageable,
    KanbanColumnFilterCriteria? Filter,
    KanbanColumnSortingCriteria? Sorting) : IRequest<Result<Page<KanbanColumnViewModel>>>;