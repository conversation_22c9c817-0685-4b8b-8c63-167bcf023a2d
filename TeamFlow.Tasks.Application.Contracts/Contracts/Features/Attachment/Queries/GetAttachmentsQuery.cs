using FluentResults;
using MediatR;
using TeamFlow.Shared.Contracts.Pagination;
using TeamFlow.Tasks.Application.Contracts.Contracts.Filtering.Attachment;
using TeamFlow.Tasks.Application.Contracts.Contracts.ViewModels;

namespace TeamFlow.Tasks.Application.Contracts.Contracts.Features.Attachment.Queries;

public record GetAttachmentsQuery(Pageable Pageable, AttachmentFilterCriteria? Filter, AttachmentSortingCriteria? Sorting) : IRequest<Result<Page<AttachmentViewModel>>>;