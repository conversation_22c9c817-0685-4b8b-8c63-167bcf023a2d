using System.Linq.Expressions;
using TeamFlow.Shared.Contracts.Enums;
using TeamFlow.Shared.Contracts.Sorting;

namespace TeamFlow.Tasks.Application.Contracts.Contracts.Filtering.KanbanColumn;

public record KanbanColumnSortingCriteria(KanbanColumnSortField SortField, SortDirection SortDirection) : ISortingCriteria<Core.Entities.KanbanColumn>
{
    public OrderByExpression<Core.Entities.KanbanColumn> ToOrderByExpression()
    {
        Expression<Func<Core.Entities.KanbanColumn, object>> keySelector = SortField switch
        {
            KanbanColumnSortField.CreatedAt => x => x.CreatedAt,
            KanbanColumnSortField.Name => x => x.Name,
            KanbanColumnSortField.ProjectId => x => x.ProjectId,
            KanbanColumnSortField.Order => x => x.Order,
            _ => t => t.Id
            // _ => throw new ArgumentOutOfRangeException(nameof(SortField), "Неизвестное поле для сортировки")
        };

        return new OrderByExpression<Core.Entities.KanbanColumn>(keySelector, SortDirection);
    }
}