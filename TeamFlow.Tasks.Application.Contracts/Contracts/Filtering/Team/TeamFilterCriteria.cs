using System.Linq.Expressions;
using TeamFlow.Shared.Contracts.Filtering;
using TeamFlow.Shared.Utils;

namespace TeamFlow.Tasks.Application.Contracts.Contracts.Filtering.Team;

public record TeamFilterCriteria(
    string? Name,
    Guid? TeamLeadId,
    string? SearchTerm) : IFilterCriteria<Core.Entities.Team>
{
    public string? SearchTerm { get; } = SearchTerm;

    public Expression<Func<Core.Entities.Team, bool>> ToPredicate()
    {
        var parameter = Expression.Parameter(typeof(Core.Entities.Team), "t");

        List<Expression?> predicates =
        [
            ExpressionBuilder.BuildStringContains<Core.Entities.Team>(parameter, nameof(Core.Entities.Team.Name), Name),
            ExpressionBuilder.BuildEqual<Core.Entities.Team, Guid?>(parameter, nameof(Core.Entities.Team.TeamLeadId), TeamLeadId)
        ];
        
        if (!string.IsNullOrWhiteSpace(SearchTerm))
        {
            var nameSearch = ExpressionBuilder.BuildStringContains<Core.Entities.Team>(
                parameter, nameof(Core.Entities.Team.Name), SearchTerm, ignoreCase: true);
                
            predicates.Add(nameSearch);
        }

        var body = ExpressionBuilder.CombineWithAnd(predicates.ToArray());
        
        return Expression.Lambda<Func<Core.Entities.Team, bool>>(body, parameter);
    }
}