using System.Linq.Expressions;
using TeamFlow.Shared.Contracts.Filtering;
using TeamFlow.Shared.Utils;
using CommentEntity = TeamFlow.Tasks.Core.Entities.Comment;

namespace TeamFlow.Tasks.Application.Contracts.Contracts.Filtering.Comment;

public record CommentFilterCriteria(
    Guid? PublisherId,
    Guid? TaskId,
    Guid? MentionedUserId,
    Guid? ParentCommentId,
    DateTime? CreatedAtFrom,
    DateTime? CreatedAtTo,
    string? SearchTerm) : IFilterCriteria<CommentEntity>
{
    public string? SearchTerm { get; } = SearchTerm;

    public Expression<Func<CommentEntity, bool>> ToPredicate()
    {
        var parameter = Expression.Parameter(typeof(CommentEntity), "c");

        List<Expression?> predicates =
        [
            ExpressionBuilder.BuildEqual<CommentEntity, Guid?>(parameter, nameof(CommentEntity.PublisherId),
                PublisherId),
            ExpressionBuilder.BuildEqual<CommentEntity, Guid?>(parameter, nameof(CommentEntity.TaskId), TaskId),
            ExpressionBuilder.BuildContains<CommentEntity, Guid?>(parameter,
                nameof(CommentEntity.MentionedUserIds),
                MentionedUserId),
            ExpressionBuilder.BuildEqual<CommentEntity, Guid?>(parameter, nameof(CommentEntity.ParentCommentId),
                ParentCommentId),
            ExpressionBuilder.BuildGreaterThanOrEqual<CommentEntity, DateTime>(parameter,
                nameof(CommentEntity.CreatedAt), CreatedAtFrom),
            ExpressionBuilder.BuildLessThanOrEqual<CommentEntity, DateTime>(parameter,
                nameof(CommentEntity.CreatedAt),
                CreatedAtTo)
        ];

        if (!string.IsNullOrWhiteSpace(SearchTerm))
        {
            var contentSearch = ExpressionBuilder.BuildStringContains<CommentEntity>(parameter,
                nameof(CommentEntity.Content),
                SearchTerm, true);

            predicates.Add(contentSearch);
        }

        var body = ExpressionBuilder.CombineWithAnd(predicates.ToArray());

        return Expression.Lambda<Func<CommentEntity, bool>>(body, parameter);
    }
}