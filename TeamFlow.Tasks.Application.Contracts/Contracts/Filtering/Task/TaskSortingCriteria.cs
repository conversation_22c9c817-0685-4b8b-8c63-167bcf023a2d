using System.Linq.Expressions;
using TeamFlow.Shared.Contracts.Enums;
using TeamFlow.Shared.Contracts.Sorting;

namespace TeamFlow.Tasks.Application.Contracts.Contracts.Filtering.Task;

public record TaskSortingCriteria(TaskSortField SortField, SortDirection SortDirection) : ISortingCriteria<Core.Entities.Task>
{
    public OrderByExpression<Core.Entities.Task> ToOrderByExpression()
    {
        Expression<Func<Core.Entities.Task, object>> keySelector = SortField switch
        {
            TaskSortField.Title => t => t.Title,
            TaskSortField.DueDate => t => t.DueDate!,
            TaskSortField.CompletedAt => t => t.CompletedAt!,
            TaskSortField.Status => t => t.Status,
            TaskSortField.Priority => t => t.Priority,
            TaskSortField.Type => t => t.Type,
            TaskSortField.EstimatedTime => t => t.EstimatedTime!,
            TaskSortField.StatusChangedAt => t => t.StatusChangedAt,
            TaskSortField.ActualTime => t => t.ActualTime!,
            TaskSortField.StoryPoints => t => t.StoryPoints!,
            TaskSortField.KanbanOrder => t => t.KanbanOrder!,
            TaskSortField.CreatedAt => t => t.CreatedAt,
            TaskSortField.UpdatedAt => t => t.UpdatedAt,
            _ => t => t.Id
            // _ => throw new ArgumentOutOfRangeException(nameof(SortField), "Неизвестное поле для сортировки")
        };

        return new OrderByExpression<Core.Entities.Task>(keySelector, SortDirection);
    }
}