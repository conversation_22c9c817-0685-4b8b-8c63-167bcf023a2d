using System.Linq.Expressions;
using TeamFlow.Shared.Contracts.Filtering;
using TeamFlow.Shared.Utils;
using TeamFlow.Tasks.Core.Shared.Enums;

namespace TeamFlow.Tasks.Application.Contracts.Contracts.Filtering.Task;

public record TaskFilterCriteria(
    string? Title, 
    Guid? ProjectId, 
    Guid? AssigneeId, 
    Guid? ReporterId, 
    Guid? KanbanColumnId,
    DateTime? DueDate, 
    DateTime? CompletedAt,
    TimeSpan? EstimatedTime,
    TimeSpan? ActualTime,
    TaskStatuses? Status,
    TaskType? Type,
    PriorityLevel? PriorityLevel,
    decimal? StoryPoints,
    int? Order,
    string? Tag,
    string? SearchTerm) : IFilterCriteria<Core.Entities.Task>
{
    public string? SearchTerm { get; } = SearchTerm;

    public Expression<Func<Core.Entities.Task, bool>> ToPredicate()
    {
        var parameter = Expression.Parameter(typeof(Core.Entities.Task), "t");

        List<Expression?> predicates =
        [
            ExpressionBuilder.BuildStringContains<Core.Entities.Task>(parameter, nameof(Core.Entities.Task.Title), Title),
            ExpressionBuilder.BuildEqual<Core.Entities.Task, Guid?>(parameter, nameof(Core.Entities.Task.ProjectId), ProjectId),
            ExpressionBuilder.BuildEqual<Core.Entities.Task, Guid?>(parameter, nameof(Core.Entities.Task.AssignedId), AssigneeId),
            ExpressionBuilder.BuildEqual<Core.Entities.Task, Guid?>(parameter, nameof(Core.Entities.Task.ReporterId), ReporterId),
            ExpressionBuilder.BuildEqual<Core.Entities.Task, Guid?>(parameter, nameof(Core.Entities.Task.KanbanColumnId),
                KanbanColumnId),
            ExpressionBuilder.BuildEqual<Core.Entities.Task, PriorityLevel?>(parameter, nameof(Core.Entities.Task.Priority), PriorityLevel),
            ExpressionBuilder.BuildEqual<Core.Entities.Task, DateTime?>(parameter, nameof(Core.Entities.Task.DueDate), DueDate),
            ExpressionBuilder.BuildEqual<Core.Entities.Task, DateTime?>(parameter, nameof(Core.Entities.Task.CompletedAt), CompletedAt),
            ExpressionBuilder.BuildEqual<Core.Entities.Task, TimeSpan?>(parameter, nameof(Core.Entities.Task.EstimatedTime),
                EstimatedTime),
            ExpressionBuilder.BuildEqual<Core.Entities.Task, TimeSpan?>(parameter, nameof(Core.Entities.Task.ActualTime), ActualTime),
            ExpressionBuilder.BuildEqual<Core.Entities.Task, TaskStatuses?>(parameter, nameof(Core.Entities.Task.Status), Status),
            ExpressionBuilder.BuildEqual<Core.Entities.Task, TaskType?>(parameter, nameof(Core.Entities.Task.Type), Type),
            ExpressionBuilder.BuildEqual<Core.Entities.Task, decimal?>(parameter, nameof(Core.Entities.Task.StoryPoints), StoryPoints),
            ExpressionBuilder.BuildEqual<Core.Entities.Task, int?>(parameter, nameof(Core.Entities.Task.KanbanOrder), Order),
            ExpressionBuilder.BuildContains<Core.Entities.Task, string>(parameter, nameof(Core.Entities.Task.Tags), Tag)
        ];
        
        if (!string.IsNullOrWhiteSpace(SearchTerm))
        {
            var titleSearch = ExpressionBuilder.BuildStringContains<Core.Entities.Task>(
                parameter, nameof(Core.Entities.Task.Title), SearchTerm, ignoreCase: true);
                
            var descriptionSearch = ExpressionBuilder.BuildStringContains<Core.Entities.Task>(
                parameter, nameof(Core.Entities.Task.Description), SearchTerm, ignoreCase: true);
                
            var searchPredicate = ExpressionBuilder.CombineWithOr(titleSearch, descriptionSearch);
            predicates.Add(searchPredicate);
        }

        var body = ExpressionBuilder.CombineWithAnd(predicates.ToArray());
        
        return Expression.Lambda<Func<Core.Entities.Task, bool>>(body, parameter);
    }
}