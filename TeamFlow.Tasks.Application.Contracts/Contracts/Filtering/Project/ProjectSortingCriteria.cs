using System.Linq.Expressions;
using TeamFlow.Shared.Contracts.Enums;
using TeamFlow.Shared.Contracts.Sorting;

namespace TeamFlow.Tasks.Application.Contracts.Contracts.Filtering.Project;

public record ProjectSortingCriteria(ProjectSortField SortField, SortDirection SortDirection) : ISortingCriteria<Core.Entities.Project>
{
    public OrderByExpression<Core.Entities.Project> ToOrderByExpression()
    {
        Expression<Func<Core.Entities.Project, object>> keySelector = SortField switch
        {
            ProjectSortField.CreatedAt => x => x.CreatedAt,
            ProjectSortField.Name => x => x.Name,
            ProjectSortField.OwnerId => x => x.OwnerId,
            ProjectSortField.StartDate => x => x.StartDate,
            ProjectSortField.EndDate => x => x.EndDate,
            ProjectSortField.PriorityLevel => x => x.PriorityLevel,
            ProjectSortField.Status => x => x.Status,
            _ => t => t.Id
            // _ => throw new ArgumentOutOfRangeException(nameof(SortField), "Неизвестное поле для сортировки")
        };

        return new OrderByExpression<Core.Entities.Project>(keySelector, SortDirection);
    }
}