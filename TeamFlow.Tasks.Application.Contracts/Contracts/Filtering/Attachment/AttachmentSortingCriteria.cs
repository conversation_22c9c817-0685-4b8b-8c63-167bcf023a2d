using System.Linq.Expressions;
using TeamFlow.Shared.Contracts.Enums;
using TeamFlow.Shared.Contracts.Sorting;

namespace TeamFlow.Tasks.Application.Contracts.Contracts.Filtering.Attachment;

public record AttachmentSortingCriteria(AttachmentsSortField SortField, SortDirection SortDirection) : ISortingCriteria<Core.Entities.Attachment>
{
    public OrderByExpression<Core.Entities.Attachment> ToOrderByExpression()
    {
        Expression<Func<Core.Entities.Attachment, object>> keySelector = SortField switch
        {
            // AttachmentsSortField.FileName => x => x.FileName,
            AttachmentsSortField.OriginalFileName => x => x.OriginalFileName,
            AttachmentsSortField.CreatedAt => x => x.CreatedAt,
            AttachmentsSortField.UploaderId => x => x.UploaderId,
            AttachmentsSortField.FileSize => x => x.FileSize,
            AttachmentsSortField.ContentType => x => x.ContentType,
            AttachmentsSortField.UpdatedAt => x => x.UpdatedAt,
            AttachmentsSortField.TaskId => x => x.TaskId,
            _ => x => x.Id,
        };
        
        return new OrderByExpression<Core.Entities.Attachment>(keySelector, SortDirection);
    }
}