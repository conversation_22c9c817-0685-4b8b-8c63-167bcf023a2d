using TeamFlow.Shared.Contracts.Enums;

namespace TeamFlow.Tasks.Application.Contracts.Contracts.External;

public class UserDto
{
    /// <summary>
    /// Unique identifier of the user.
    /// </summary>
    public Guid Id { get; set; }

    /// <summary>
    /// User's login name.
    /// </summary>
    public string Login { get; set; }

    /// <summary>
    /// User's email address.
    /// </summary>
    public string Email { get; set; }

    /// <summary>
    /// URL to the user's avatar image.
    /// </summary>
    public string AvatarUrl { get; set; }

    /// <summary>
    /// Role of the user.
    /// </summary>
    public UserRole Role { get; set; }

    /// <summary>
    /// Identifier of the user's position.
    /// </summary>
    public Guid PositionId { get; set; }
}