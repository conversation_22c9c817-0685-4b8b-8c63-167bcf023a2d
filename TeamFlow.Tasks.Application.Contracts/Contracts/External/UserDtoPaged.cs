namespace TeamFlow.Tasks.Application.Contracts.Contracts.External;

public class UserDtoPaged
{
    /// <summary>
    /// Общее число пользователей во внешнем сервисе.
    /// </summary>
    public long TotalCount { get; set; }

    /// <summary>
    /// Общее число страниц (рассчитывается как TotalCount / PageSize).
    /// </summary>
    public double TotalPages { get; set; }

    /// <summary>
    /// Список пользователей на текущей странице.
    /// </summary>
    public IEnumerable<Application.Contracts.Contracts.External.UserDto>? Users { get; set; }

    /// <summary>
    /// Номер текущей страницы
    /// </summary>
    public int PageNumber { get; set; }

    /// <summary>
    /// Размер текущей страницы
    /// </summary>
    public int PageSize { get; set; }
    
    public UserDtoPaged()
    {
    }

    public UserDtoPaged(int pageNumber, int pageSize, int totalCount, int totalPages, IEnumerable<Application.Contracts.Contracts.External.UserDto> users)
    {
        Users = users;
        TotalCount = totalCount;
        TotalPages = totalPages;
    }
}