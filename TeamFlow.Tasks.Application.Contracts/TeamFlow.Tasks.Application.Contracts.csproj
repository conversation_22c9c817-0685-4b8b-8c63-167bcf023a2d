<Project Sdk="Microsoft.NET.Sdk">

    <PropertyGroup>
        <TargetFramework>net9.0</TargetFramework>
        <ImplicitUsings>enable</ImplicitUsings>
        <Nullable>enable</Nullable>
    </PropertyGroup>

    <ItemGroup>
      <ProjectReference Include="..\src\Tasks\TeamFlow.Tasks.Core\TeamFlow.Tasks.Core.csproj" />
    </ItemGroup>

    <ItemGroup>
      <PackageReference Include="MediatR.Contracts" Version="2.0.1" />
    </ItemGroup>

    <ItemGroup>
      <Folder Include="Contracts\Features\Files\" />
    </ItemGroup>

</Project>
