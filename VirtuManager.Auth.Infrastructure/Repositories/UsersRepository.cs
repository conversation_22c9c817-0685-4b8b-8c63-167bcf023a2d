using VirtuManager.Auth.Domain.Entities;
using VirtuManager.Auth.Domain.Repositories;
using VirtuManager.Auth.Infrastructure.Database;

namespace VirtuManager.Auth.Infrastructure.Repositories;

public class UsersRepository : GenericRepository<User, Guid>, IUsersRepository
{
    public UsersRepository(ApplicationDbContext context) : base(context)
    {
    }

    public Task<User?> GetByLoginAsync(string login, CancellationToken cancellationToken = default)
    {
        throw new NotImplementedException();
    }
}