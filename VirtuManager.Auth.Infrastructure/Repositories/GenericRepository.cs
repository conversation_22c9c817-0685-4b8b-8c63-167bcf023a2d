using VirtuManager.Auth.Domain.Entities.Base;
using VirtuManager.Auth.Domain.Pagination;
using VirtuManager.Auth.Domain.Repositories.Abstractions;
using VirtuManager.Auth.Infrastructure.Database;

namespace VirtuManager.Auth.Infrastructure.Repositories;

public class GenericRepository<TEntity, TKey> : IRepository<TEntity, TKey> where TKey : IEquatable<TKey> where TEntity : class, IEntity<TKey>
{
    protected readonly ApplicationDbContext _context;
    
    public GenericRepository(ApplicationDbContext context)
    {
        ArgumentNullException.ThrowIfNull(context);
        _context = context;
    }
    
    public virtual Task AddAsync(TEntity entity, CancellationToken cancellationToken = default)
    {
        throw new NotImplementedException();
    }

    public virtual Task RemoveAsync(TEntity entity, CancellationToken cancellationToken = default)
    {
        throw new NotImplementedException();
    }

    public virtual Task UpdateRange(IEnumerable<TEntity> entities)
    {
        throw new NotImplementedException();
    }

    public virtual Task UpdateAsync(TEntity entity, CancellationToken cancellationToken = default)
    {
        throw new NotImplementedException();
    }

    public virtual Task<TEntity?> GetByIdAsync(TKey id, CancellationToken cancellationToken = default)
    {
        throw new NotImplementedException();
    }

    public virtual Task<List<TEntity>?> GetAllAsync(CancellationToken cancellationToken = default)
    {
        throw new NotImplementedException();
    }

    public virtual Task<PagedResult<TEntity>> GetPagedAsync(Pageable pageable, CancellationToken cancellationToken = default)
    {
        throw new NotImplementedException();
    }
}