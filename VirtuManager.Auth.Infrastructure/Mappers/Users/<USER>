using VirtuManager.Auth.Application.ViewModels;
using VirtuManager.Auth.Domain.Entities;
using VirtuManager.Auth.Domain.Mapping;

namespace VirtuManager.Auth.Infrastructure.Mappers.Users;

public class UserMapper : IMapper<UserViewModel, User>
{
    private readonly UserMappingProfile _mapper;

    public UserMapper(UserMappingProfile mapper)
    {
        ArgumentNullException.ThrowIfNull(mapper, nameof(mapper));
        _mapper = mapper;
    }

    public UserViewModel MapToModel(User source) => _mapper.MapToViewModel(source);

    public List<UserViewModel> MapToModel(IEnumerable<User> sources)
        => sources.Select(x => _mapper.MapToViewModel(x)).ToList();
}