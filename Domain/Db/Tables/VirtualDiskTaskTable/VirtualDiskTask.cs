using System.ComponentModel.DataAnnotations;

namespace VirtuManagerBackend.Domain.Db.Tables.VirtualDiskTaskTable;

public class VirtualDiskTask
{
    public int Id { get; set; }
    
    public string Payload { get; set; }
    
    public string? Error { get; set; }

    [EnumDataType(typeof(TaskType))]
    public TaskType Type { get; set; }

    [EnumDataType(typeof(TaskState))]
    public TaskState Status { get; set; }

    public DateTime CreatedAt { get; set; }

    public DateTime? EndedAt { get; set; }
}