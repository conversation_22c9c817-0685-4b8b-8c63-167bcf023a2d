using MongoDB.Bson;
using LogLevel = VirtuManagerBackend.Domain.Db.Tables.LogEntryTable.LogLevel;

namespace VirtuManagerBackend.Domain.Db.Tables.LogEntryTable;

public class LogEntry
{
    public ObjectId Id { get; set; }

    public DateTime Time { get; set; }

    public LogLevel Level { get; set; }

    public string Message { get; set; }

    public string? Exception { get; set; }

    public LogType Type { get; set; }
}