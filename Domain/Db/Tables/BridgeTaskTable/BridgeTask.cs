using System.ComponentModel.DataAnnotations;
using VirtuManagerBackend.Infrastructure.Kafka.Interfaces;

namespace VirtuManagerBackend.Domain.Db.Tables.BridgeTaskTable;

public class BridgeTask : IKafkaMessage
{
    public int Id { get; set; }

    public string Payload { get; set; }
    
    public string? Error { get; set; }

    [EnumDataType(typeof(TaskType))]
    public TaskType Type { get; set; }

    [EnumDataType(typeof(TaskState))]
    public TaskState Status { get; set; }

    public DateTime CreatedAt { get; set; }

    public DateTime? EndedAt { get; set; }
}