using System.ComponentModel.DataAnnotations;

namespace VirtuManagerBackend.Domain.Db.Tables.UserTable;

public class User 
{
	public int Id { get; set; }
	
	public string Login { get; set; }

	public string Email { get; set; }
	
	public string Password { get; set; }
	
	public DateTime CreatedAt { get; set; }
	
	public DateTime? LastAuthorization { get; set; }

	[EnumDataType(typeof(UserRole))]
	public UserRole Role { get; set; }
}