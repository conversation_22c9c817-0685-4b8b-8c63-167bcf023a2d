using VirtuManagerBackend.Domain.Db.Tables.NodeTable;
using VirtuManagerBackend.Domain.Db.Tables.VirtualMachineSettingsTable;

namespace VirtuManagerBackend.Domain.Db.Tables.VirtualMachineTable;

public class VirtualMachine
{
    public int Id { get; set; }
    
    public string Title { get; set; }
    
    public ulong? CpuLoad { get; set; }

    public string Description { get; set; }
    
    public ulong? AvailableMemory { get; set; }
    
    public ulong? UsedMemory { get; set; }
    
    public ulong? TotalMemory { get; set; }

    public ulong? Uptime { get; set; }
    
    public ulong? VncPort { get; set; }

    public DateTime? StartDate { get; set; }

    public DateTime? StopDate { get; set; }

    public string? State { get; set; }
    
    public int NodeId { get; set; }
    
    public Node Node { get; set; }
    
    public VirtualMachineSettings VirtualMachineSettings { get; set; }
}