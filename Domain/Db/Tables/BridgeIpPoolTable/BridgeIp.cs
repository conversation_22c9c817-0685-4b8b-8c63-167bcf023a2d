using System.Text.Json.Serialization;
using VirtuManagerBackend.Domain.Db.Tables.BridgesTable;
using VirtuManagerBackend.Domain.Db.Tables.VirtualMachineTable;

namespace VirtuManagerBackend.Domain.Db.Tables.BridgeIpPoolTable;

public class BridgeIp
{
    public int Id { get; set; }
    
    public string Ip { get; set; }
    
    public string? MacAddress { get; set; }

    public int BridgeId { get; set; }
    
    [JsonIgnore]
    public Bridge Bridge { get; set; }

    public int? VirtualMachineId { get; set; }
    
    [JsonIgnore]
    public VirtualMachine? VirtualMachine { get; set; }
}