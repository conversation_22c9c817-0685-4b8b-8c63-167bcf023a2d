using System.Text.Json.Serialization;
using VirtuManagerBackend.Domain.Db.Tables.BridgeIpPoolTable;
using VirtuManagerBackend.Domain.Db.Tables.NodeTable;

namespace VirtuManagerBackend.Domain.Db.Tables.BridgesTable;

public class Bridge
{
    public int Id { get; set; }
    
    public string Mask { get; set; }
    
    public string Name { get; set; }
    
    public string Network { get; set; }
    
    public bool UseNat { get; set; }
    
    public string DhcpStart { get; set; }
    
    public string DhcpEnd { get; set; }
    
    public int NodeId { get; set; }
    
    [JsonIgnore]
    public Node Node { get; set; }

    public IEnumerable<BridgeIp> BridgeIps { get; set; }
}