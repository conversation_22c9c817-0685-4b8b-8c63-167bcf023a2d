using System.Text.Json.Serialization;
using VirtuManagerBackend.Domain.Db.Tables.VirtualDiskTable;
using VirtuManagerBackend.Domain.Db.Tables.VirtualMachineTable;

namespace VirtuManagerBackend.Domain.Db.Tables.VirtualMachineSettingsTable;

public class VirtualMachineSettings
{
    public int Id { get; set; }
    
    public string UserName { get; set; }
    
    public string Password { get; set; }
    
    public ulong Ram { get; set; }
    
    public int CpuCores { get; set; }
    
    public string Sudo { get; set; }
    
    public string Shell { get; set; }

    public bool LockPassword { get; set; }
    
    public string HostName { get; set; }

    public string InstalledOs { get; set; }
    
    public IEnumerable<VirtualDisk> VirtualDisks { get; set; }
    
    public int VirtualMachineId { get; set; }

    [JsonIgnore]    
    public VirtualMachine VirtualMachine { get; set; }
}