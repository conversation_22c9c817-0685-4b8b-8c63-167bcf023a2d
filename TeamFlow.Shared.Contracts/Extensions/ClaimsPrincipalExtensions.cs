using System.Security.Claims;
using TeamFlow.Shared.Contracts.Enums;

namespace TeamFlow.Shared.Contracts.Extensions;

public static class ClaimsPrincipalExtensions
{
    public static Guid GetUserId(this ClaimsPrincipal? principal)
    {
        string? userId = principal?.FindFirst(nameof(JwtClaimTypes.Sub))?.Value;
        
        return Guid.TryParse(userId, out var parsedGuid) 
            ? parsedGuid
            : throw new ArgumentException("User identifier is unavailable");
    }
    
    public static UserRole GetUserRole(this ClaimsPrincipal? principal)
    {
        string? roleString = principal?.FindFirst(ClaimTypes.Role)?.Value;
        
        return Enum.TryParse<UserRole>(roleString, out var role) 
            ? role
            : throw new ArgumentException("User role is unavailable");
    }

    public static string GetTokenType(this ClaimsPrincipal? principal)
    {
        string? tokenType = principal?.FindFirst(nameof(JwtClaimTypes.TokenType))?.Value;
        
        return tokenType ?? throw new ArgumentException("Token type is unavailable");
    }
}