using System.Linq.Expressions;
using TeamFlow.Shared.Contracts.Enums;

namespace TeamFlow.Shared.Contracts.Sorting;

/// <summary>
/// Класс, представляющий выражение для сортировки сущностей.
/// </summary>
/// <typeparam name="TEntity">Тип сущности, для которой применяется сортировка.</typeparam>
public class OrderByExpression<TEntity> where TEntity : class
{
    /// <summary>
    /// Публичный конструктор для создания объекта.
    /// </summary>
    /// <param name="keySelector">Лямбда-выражение, определяющее ключ сортировки.</param>
    /// <param name="direction">Направление сортировки (по умолчанию - по возрастанию).</param>
    public OrderByExpression(Expression<Func<TEntity, object>> keySelector, SortDirection direction = SortDirection.Ascending)
    {
        ArgumentNullException.ThrowIfNull(keySelector, nameof(keySelector));

        if (keySelector.Body.Type != typeof(TEntity)
            && !keySelector.Body.Type.IsSubclassOf(typeof(TEntity))
            && !(typeof(TEntity).IsInterface
                 && keySelector.Body.Type.GetInterfaces().Contains(typeof(TEntity))))
        {
            throw new ArgumentException(
                $"Тип выражения ({keySelector.Body.Type.Name}) не соответствует ожидаемому типу сущности ({typeof(TEntity).Name}).",
                nameof(keySelector));
        }

        KeySelector = keySelector;
        Direction = direction;
    }

    /// <summary>
    /// Лямбда-выражение, определяющее ключ сортировки.
    /// </summary>
    public Expression<Func<TEntity, object>> KeySelector { get; }

    /// <summary>
    /// Указывает, должна ли сортировка быть по убыванию.
    /// </summary>
    public SortDirection? Direction { get; }
}