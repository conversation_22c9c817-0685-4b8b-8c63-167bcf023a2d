# VirtuManager.Auth.Contracts

This project contains the gRPC contracts (proto files) for the VirtuManager Auth Service.

## Structure

- **Protos/common.proto** - Shared messages, enums, and common types
- **Protos/auth.proto** - Authentication and authorization service contracts
- **Protos/users.proto** - User management service contracts

## Usage

This package can be referenced by:
- The Auth Service implementation (VirtuManager.Auth.Api)
- Client applications that need to consume the Auth Service
- Other microservices in the VirtuManager ecosystem

## Building

The project automatically generates C# classes from proto files during build.

## Versioning

Follow semantic versioning when making changes to contracts:
- **Major**: Breaking changes to existing services
- **Minor**: Adding new services or optional fields
- **Patch**: Documentation updates, non-breaking fixes

## Guidelines

1. **Backward Compatibility**: Always maintain backward compatibility
2. **Field Numbers**: Never reuse field numbers in proto messages
3. **Naming**: Use snake_case for field names, PascalCase for message names
4. **Documentation**: Document all services and important fields
