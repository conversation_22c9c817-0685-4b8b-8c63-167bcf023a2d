syntax = "proto3";

import "google/protobuf/timestamp.proto";
import "google/protobuf/empty.proto";

option csharp_namespace = "VirtuManager.Auth.Contracts.Common";

package VirtuManager.Auth.Common;

// Common enums
enum UserRole {
  USER_ROLE_UNSPECIFIED = 0;
  USER_ROLE_REGULAR = 1;
  USER_ROLE_DEVELOPER = 2;
  USER_ROLE_ADMIN = 3;
}

// Common messages
message PaginationRequest {
  int32 page_number = 1;
  int32 page_size = 2;
}

message PaginationResponse {
  int64 total_count = 1;
  int32 page_number = 2;
  int32 page_size = 3;
  int32 total_pages = 4;
}

// Error handling
message ErrorResponse {
  string code = 1;
  string message = 2;
  repeated string details = 3;
}
