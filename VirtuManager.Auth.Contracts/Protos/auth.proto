syntax = "proto3";

import "google/protobuf/timestamp.proto";
import "google/protobuf/empty.proto";

option csharp_namespace = "VirtuManager.Auth.Contracts.Auth";

package VirtuManager.Auth.Auth;

// Authentication service
service AuthService {
  rpc Login(LoginRequest) returns (LoginResponse);
  rpc RefreshToken(RefreshTokenRequest) returns (RefreshTokenResponse);
  rpc Logout(LogoutRequest) returns (google.protobuf.Empty);
  rpc ValidateToken(ValidateTokenRequest) returns (ValidateTokenResponse);
  rpc RevokeToken(RevokeTokenRequest) returns (google.protobuf.Empty);
}

// Request messages
message LoginRequest {
  string login = 1;
  string password = 2;
}

message RefreshTokenRequest {
  string refresh_token = 1;
}

message LogoutRequest {
  string refresh_token = 1;
}

message ValidateTokenRequest {
  string access_token = 1;
}

message RevokeTokenRequest {
  string refresh_token = 1;
}

// Response messages
message LoginResponse {
  string access_token = 1;
  string refresh_token = 2;
  google.protobuf.Timestamp expires_at = 3;
  UserInfo user = 4;
}

message RefreshTokenResponse {
  string access_token = 1;
  string refresh_token = 2;
  google.protobuf.Timestamp expires_at = 3;
}

message ValidateTokenResponse {
  bool is_valid = 1;
  UserInfo user = 2;
  google.protobuf.Timestamp expires_at = 3;
}

// Data models
message UserInfo {
  string user_id = 1;
  string login = 2;
  string role = 3;
}
