syntax = "proto3";

import "google/protobuf/timestamp.proto";
import "google/protobuf/empty.proto";
import "Protos/common.proto";

option csharp_namespace = "VirtuManager.Auth.Contracts.Users";

package VirtuManager.Auth.Users;

// User management service
service UserService {
  rpc GetUserById(GetUserByIdRequest) returns (User);
  rpc GetUserByLogin(GetUserByLoginRequest) returns (User);
  rpc GetUsers(GetUsersRequest) returns (GetUsersResponse);
  rpc GetUsersPaged(GetUsersPagedRequest) returns (GetUsersPagedResponse);
  rpc CreateUser(CreateUserRequest) returns (CreateUserResponse);
  rpc UpdateUser(UpdateUserRequest) returns (User);
  rpc DeleteUser(DeleteUserRequest) returns (google.protobuf.Empty);
}

// Request messages
message GetUserByIdRequest {
  string user_id = 1;
}

message GetUserByLoginRequest {
  string login = 1;
}

message GetUsersRequest {
  repeated string user_ids = 1;
}

message GetUsersPagedRequest {
  VirtuManager.Auth.Common.PaginationRequest pagination = 1;
  string search_term = 2;
  VirtuManager.Auth.Common.UserRole role_filter = 3;
}

message CreateUserRequest {
  string login = 1;
  string email = 2;
  string password = 3;
  VirtuManager.Auth.Common.UserRole role = 4;
}

message UpdateUserRequest {
  string user_id = 1;
  string login = 2;
  string email = 3;
  VirtuManager.Auth.Common.UserRole role = 4;
}

message DeleteUserRequest {
  string user_id = 1;
}

// Response messages
message GetUsersResponse {
  repeated User users = 1;
}

message GetUsersPagedResponse {
  VirtuManager.Auth.Common.PaginationResponse pagination = 1;
  repeated User users = 2;
}

message CreateUserResponse {
  User user = 1;
  string temporary_password = 2;
}

// Data models
message User {
  string id = 1;
  string login = 2;
  string email = 3;
  VirtuManager.Auth.Common.UserRole role = 4;
  google.protobuf.Timestamp created_at = 5;
  google.protobuf.Timestamp updated_at = 6;
  google.protobuf.Timestamp last_login_at = 7;
}
