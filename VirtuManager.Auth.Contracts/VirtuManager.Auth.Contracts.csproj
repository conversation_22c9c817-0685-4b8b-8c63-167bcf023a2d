<Project Sdk="Microsoft.NET.Sdk">

    <PropertyGroup>
        <TargetFramework>net8.0</TargetFramework>
        <ImplicitUsings>enable</ImplicitUsings>
        <Nullable>enable</Nullable>
        <GeneratePackageOnBuild>true</GeneratePackageOnBuild>
        <PackageId>VirtuManager.Auth.Contracts</PackageId>
        <PackageVersion>1.0.0</PackageVersion>
        <Authors>VirtuManager Team</Authors>
        <Description>gRPC contracts for VirtuManager Auth Service</Description>
    </PropertyGroup>

    <ItemGroup>
        <PackageReference Include="Grpc.AspNetCore" Version="2.57.0" />
        <PackageReference Include="Google.Protobuf" Version="3.25.1" />
        <PackageReference Include="Grpc.Tools" Version="2.59.0" PrivateAssets="All" />
    </ItemGroup>

    <ItemGroup>
        <Protobuf Include="Protos\**\*.proto" GrpcServices="Both" />
    </ItemGroup>

</Project>
