// <auto-generated>
//     Generated by the protocol buffer compiler.  DO NOT EDIT!
//     source: Protos/users.proto
// </auto-generated>
#pragma warning disable 0414, 1591, 8981, 0612
#region Designer generated code

using grpc = global::Grpc.Core;

namespace VirtuManager.Auth.Contracts.Users {
  /// <summary>
  /// User management service
  /// </summary>
  public static partial class UserService
  {
    static readonly string __ServiceName = "VirtuManager.Auth.Users.UserService";

    [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
    static void __Helper_SerializeMessage(global::Google.Protobuf.IMessage message, grpc::SerializationContext context)
    {
      #if !GRPC_DISABLE_PROTOBUF_BUFFER_SERIALIZATION
      if (message is global::Google.Protobuf.IBufferMessage)
      {
        context.SetPayloadLength(message.CalculateSize());
        global::Google.Protobuf.MessageExtensions.WriteTo(message, context.GetBufferWriter());
        context.Complete();
        return;
      }
      #endif
      context.Complete(global::Google.Protobuf.MessageExtensions.ToByteArray(message));
    }

    [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
    static class __Helper_MessageCache<T>
    {
      public static readonly bool IsBufferMessage = global::System.Reflection.IntrospectionExtensions.GetTypeInfo(typeof(global::Google.Protobuf.IBufferMessage)).IsAssignableFrom(typeof(T));
    }

    [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
    static T __Helper_DeserializeMessage<T>(grpc::DeserializationContext context, global::Google.Protobuf.MessageParser<T> parser) where T : global::Google.Protobuf.IMessage<T>
    {
      #if !GRPC_DISABLE_PROTOBUF_BUFFER_SERIALIZATION
      if (__Helper_MessageCache<T>.IsBufferMessage)
      {
        return parser.ParseFrom(context.PayloadAsReadOnlySequence());
      }
      #endif
      return parser.ParseFrom(context.PayloadAsNewBuffer());
    }

    [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
    static readonly grpc::Marshaller<global::VirtuManager.Auth.Contracts.Users.GetUserByIdRequest> __Marshaller_VirtuManager_Auth_Users_GetUserByIdRequest = grpc::Marshallers.Create(__Helper_SerializeMessage, context => __Helper_DeserializeMessage(context, global::VirtuManager.Auth.Contracts.Users.GetUserByIdRequest.Parser));
    [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
    static readonly grpc::Marshaller<global::VirtuManager.Auth.Contracts.Users.User> __Marshaller_VirtuManager_Auth_Users_User = grpc::Marshallers.Create(__Helper_SerializeMessage, context => __Helper_DeserializeMessage(context, global::VirtuManager.Auth.Contracts.Users.User.Parser));
    [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
    static readonly grpc::Marshaller<global::VirtuManager.Auth.Contracts.Users.GetUserByLoginRequest> __Marshaller_VirtuManager_Auth_Users_GetUserByLoginRequest = grpc::Marshallers.Create(__Helper_SerializeMessage, context => __Helper_DeserializeMessage(context, global::VirtuManager.Auth.Contracts.Users.GetUserByLoginRequest.Parser));
    [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
    static readonly grpc::Marshaller<global::VirtuManager.Auth.Contracts.Users.GetUsersRequest> __Marshaller_VirtuManager_Auth_Users_GetUsersRequest = grpc::Marshallers.Create(__Helper_SerializeMessage, context => __Helper_DeserializeMessage(context, global::VirtuManager.Auth.Contracts.Users.GetUsersRequest.Parser));
    [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
    static readonly grpc::Marshaller<global::VirtuManager.Auth.Contracts.Users.GetUsersResponse> __Marshaller_VirtuManager_Auth_Users_GetUsersResponse = grpc::Marshallers.Create(__Helper_SerializeMessage, context => __Helper_DeserializeMessage(context, global::VirtuManager.Auth.Contracts.Users.GetUsersResponse.Parser));
    [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
    static readonly grpc::Marshaller<global::VirtuManager.Auth.Contracts.Users.GetUsersPagedRequest> __Marshaller_VirtuManager_Auth_Users_GetUsersPagedRequest = grpc::Marshallers.Create(__Helper_SerializeMessage, context => __Helper_DeserializeMessage(context, global::VirtuManager.Auth.Contracts.Users.GetUsersPagedRequest.Parser));
    [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
    static readonly grpc::Marshaller<global::VirtuManager.Auth.Contracts.Users.GetUsersPagedResponse> __Marshaller_VirtuManager_Auth_Users_GetUsersPagedResponse = grpc::Marshallers.Create(__Helper_SerializeMessage, context => __Helper_DeserializeMessage(context, global::VirtuManager.Auth.Contracts.Users.GetUsersPagedResponse.Parser));
    [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
    static readonly grpc::Marshaller<global::VirtuManager.Auth.Contracts.Users.CreateUserRequest> __Marshaller_VirtuManager_Auth_Users_CreateUserRequest = grpc::Marshallers.Create(__Helper_SerializeMessage, context => __Helper_DeserializeMessage(context, global::VirtuManager.Auth.Contracts.Users.CreateUserRequest.Parser));
    [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
    static readonly grpc::Marshaller<global::VirtuManager.Auth.Contracts.Users.CreateUserResponse> __Marshaller_VirtuManager_Auth_Users_CreateUserResponse = grpc::Marshallers.Create(__Helper_SerializeMessage, context => __Helper_DeserializeMessage(context, global::VirtuManager.Auth.Contracts.Users.CreateUserResponse.Parser));
    [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
    static readonly grpc::Marshaller<global::VirtuManager.Auth.Contracts.Users.UpdateUserRequest> __Marshaller_VirtuManager_Auth_Users_UpdateUserRequest = grpc::Marshallers.Create(__Helper_SerializeMessage, context => __Helper_DeserializeMessage(context, global::VirtuManager.Auth.Contracts.Users.UpdateUserRequest.Parser));
    [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
    static readonly grpc::Marshaller<global::VirtuManager.Auth.Contracts.Users.DeleteUserRequest> __Marshaller_VirtuManager_Auth_Users_DeleteUserRequest = grpc::Marshallers.Create(__Helper_SerializeMessage, context => __Helper_DeserializeMessage(context, global::VirtuManager.Auth.Contracts.Users.DeleteUserRequest.Parser));
    [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
    static readonly grpc::Marshaller<global::Google.Protobuf.WellKnownTypes.Empty> __Marshaller_google_protobuf_Empty = grpc::Marshallers.Create(__Helper_SerializeMessage, context => __Helper_DeserializeMessage(context, global::Google.Protobuf.WellKnownTypes.Empty.Parser));

    [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
    static readonly grpc::Method<global::VirtuManager.Auth.Contracts.Users.GetUserByIdRequest, global::VirtuManager.Auth.Contracts.Users.User> __Method_GetUserById = new grpc::Method<global::VirtuManager.Auth.Contracts.Users.GetUserByIdRequest, global::VirtuManager.Auth.Contracts.Users.User>(
        grpc::MethodType.Unary,
        __ServiceName,
        "GetUserById",
        __Marshaller_VirtuManager_Auth_Users_GetUserByIdRequest,
        __Marshaller_VirtuManager_Auth_Users_User);

    [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
    static readonly grpc::Method<global::VirtuManager.Auth.Contracts.Users.GetUserByLoginRequest, global::VirtuManager.Auth.Contracts.Users.User> __Method_GetUserByLogin = new grpc::Method<global::VirtuManager.Auth.Contracts.Users.GetUserByLoginRequest, global::VirtuManager.Auth.Contracts.Users.User>(
        grpc::MethodType.Unary,
        __ServiceName,
        "GetUserByLogin",
        __Marshaller_VirtuManager_Auth_Users_GetUserByLoginRequest,
        __Marshaller_VirtuManager_Auth_Users_User);

    [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
    static readonly grpc::Method<global::VirtuManager.Auth.Contracts.Users.GetUsersRequest, global::VirtuManager.Auth.Contracts.Users.GetUsersResponse> __Method_GetUsers = new grpc::Method<global::VirtuManager.Auth.Contracts.Users.GetUsersRequest, global::VirtuManager.Auth.Contracts.Users.GetUsersResponse>(
        grpc::MethodType.Unary,
        __ServiceName,
        "GetUsers",
        __Marshaller_VirtuManager_Auth_Users_GetUsersRequest,
        __Marshaller_VirtuManager_Auth_Users_GetUsersResponse);

    [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
    static readonly grpc::Method<global::VirtuManager.Auth.Contracts.Users.GetUsersPagedRequest, global::VirtuManager.Auth.Contracts.Users.GetUsersPagedResponse> __Method_GetUsersPaged = new grpc::Method<global::VirtuManager.Auth.Contracts.Users.GetUsersPagedRequest, global::VirtuManager.Auth.Contracts.Users.GetUsersPagedResponse>(
        grpc::MethodType.Unary,
        __ServiceName,
        "GetUsersPaged",
        __Marshaller_VirtuManager_Auth_Users_GetUsersPagedRequest,
        __Marshaller_VirtuManager_Auth_Users_GetUsersPagedResponse);

    [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
    static readonly grpc::Method<global::VirtuManager.Auth.Contracts.Users.CreateUserRequest, global::VirtuManager.Auth.Contracts.Users.CreateUserResponse> __Method_CreateUser = new grpc::Method<global::VirtuManager.Auth.Contracts.Users.CreateUserRequest, global::VirtuManager.Auth.Contracts.Users.CreateUserResponse>(
        grpc::MethodType.Unary,
        __ServiceName,
        "CreateUser",
        __Marshaller_VirtuManager_Auth_Users_CreateUserRequest,
        __Marshaller_VirtuManager_Auth_Users_CreateUserResponse);

    [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
    static readonly grpc::Method<global::VirtuManager.Auth.Contracts.Users.UpdateUserRequest, global::VirtuManager.Auth.Contracts.Users.User> __Method_UpdateUser = new grpc::Method<global::VirtuManager.Auth.Contracts.Users.UpdateUserRequest, global::VirtuManager.Auth.Contracts.Users.User>(
        grpc::MethodType.Unary,
        __ServiceName,
        "UpdateUser",
        __Marshaller_VirtuManager_Auth_Users_UpdateUserRequest,
        __Marshaller_VirtuManager_Auth_Users_User);

    [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
    static readonly grpc::Method<global::VirtuManager.Auth.Contracts.Users.DeleteUserRequest, global::Google.Protobuf.WellKnownTypes.Empty> __Method_DeleteUser = new grpc::Method<global::VirtuManager.Auth.Contracts.Users.DeleteUserRequest, global::Google.Protobuf.WellKnownTypes.Empty>(
        grpc::MethodType.Unary,
        __ServiceName,
        "DeleteUser",
        __Marshaller_VirtuManager_Auth_Users_DeleteUserRequest,
        __Marshaller_google_protobuf_Empty);

    /// <summary>Service descriptor</summary>
    public static global::Google.Protobuf.Reflection.ServiceDescriptor Descriptor
    {
      get { return global::VirtuManager.Auth.Contracts.Users.UsersReflection.Descriptor.Services[0]; }
    }

    /// <summary>Base class for server-side implementations of UserService</summary>
    [grpc::BindServiceMethod(typeof(UserService), "BindService")]
    public abstract partial class UserServiceBase
    {
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      public virtual global::System.Threading.Tasks.Task<global::VirtuManager.Auth.Contracts.Users.User> GetUserById(global::VirtuManager.Auth.Contracts.Users.GetUserByIdRequest request, grpc::ServerCallContext context)
      {
        throw new grpc::RpcException(new grpc::Status(grpc::StatusCode.Unimplemented, ""));
      }

      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      public virtual global::System.Threading.Tasks.Task<global::VirtuManager.Auth.Contracts.Users.User> GetUserByLogin(global::VirtuManager.Auth.Contracts.Users.GetUserByLoginRequest request, grpc::ServerCallContext context)
      {
        throw new grpc::RpcException(new grpc::Status(grpc::StatusCode.Unimplemented, ""));
      }

      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      public virtual global::System.Threading.Tasks.Task<global::VirtuManager.Auth.Contracts.Users.GetUsersResponse> GetUsers(global::VirtuManager.Auth.Contracts.Users.GetUsersRequest request, grpc::ServerCallContext context)
      {
        throw new grpc::RpcException(new grpc::Status(grpc::StatusCode.Unimplemented, ""));
      }

      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      public virtual global::System.Threading.Tasks.Task<global::VirtuManager.Auth.Contracts.Users.GetUsersPagedResponse> GetUsersPaged(global::VirtuManager.Auth.Contracts.Users.GetUsersPagedRequest request, grpc::ServerCallContext context)
      {
        throw new grpc::RpcException(new grpc::Status(grpc::StatusCode.Unimplemented, ""));
      }

      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      public virtual global::System.Threading.Tasks.Task<global::VirtuManager.Auth.Contracts.Users.CreateUserResponse> CreateUser(global::VirtuManager.Auth.Contracts.Users.CreateUserRequest request, grpc::ServerCallContext context)
      {
        throw new grpc::RpcException(new grpc::Status(grpc::StatusCode.Unimplemented, ""));
      }

      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      public virtual global::System.Threading.Tasks.Task<global::VirtuManager.Auth.Contracts.Users.User> UpdateUser(global::VirtuManager.Auth.Contracts.Users.UpdateUserRequest request, grpc::ServerCallContext context)
      {
        throw new grpc::RpcException(new grpc::Status(grpc::StatusCode.Unimplemented, ""));
      }

      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      public virtual global::System.Threading.Tasks.Task<global::Google.Protobuf.WellKnownTypes.Empty> DeleteUser(global::VirtuManager.Auth.Contracts.Users.DeleteUserRequest request, grpc::ServerCallContext context)
      {
        throw new grpc::RpcException(new grpc::Status(grpc::StatusCode.Unimplemented, ""));
      }

    }

    /// <summary>Client for UserService</summary>
    public partial class UserServiceClient : grpc::ClientBase<UserServiceClient>
    {
      /// <summary>Creates a new client for UserService</summary>
      /// <param name="channel">The channel to use to make remote calls.</param>
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      public UserServiceClient(grpc::ChannelBase channel) : base(channel)
      {
      }
      /// <summary>Creates a new client for UserService that uses a custom <c>CallInvoker</c>.</summary>
      /// <param name="callInvoker">The callInvoker to use to make remote calls.</param>
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      public UserServiceClient(grpc::CallInvoker callInvoker) : base(callInvoker)
      {
      }
      /// <summary>Protected parameterless constructor to allow creation of test doubles.</summary>
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      protected UserServiceClient() : base()
      {
      }
      /// <summary>Protected constructor to allow creation of configured clients.</summary>
      /// <param name="configuration">The client configuration.</param>
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      protected UserServiceClient(ClientBaseConfiguration configuration) : base(configuration)
      {
      }

      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      public virtual global::VirtuManager.Auth.Contracts.Users.User GetUserById(global::VirtuManager.Auth.Contracts.Users.GetUserByIdRequest request, grpc::Metadata headers = null, global::System.DateTime? deadline = null, global::System.Threading.CancellationToken cancellationToken = default(global::System.Threading.CancellationToken))
      {
        return GetUserById(request, new grpc::CallOptions(headers, deadline, cancellationToken));
      }
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      public virtual global::VirtuManager.Auth.Contracts.Users.User GetUserById(global::VirtuManager.Auth.Contracts.Users.GetUserByIdRequest request, grpc::CallOptions options)
      {
        return CallInvoker.BlockingUnaryCall(__Method_GetUserById, null, options, request);
      }
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      public virtual grpc::AsyncUnaryCall<global::VirtuManager.Auth.Contracts.Users.User> GetUserByIdAsync(global::VirtuManager.Auth.Contracts.Users.GetUserByIdRequest request, grpc::Metadata headers = null, global::System.DateTime? deadline = null, global::System.Threading.CancellationToken cancellationToken = default(global::System.Threading.CancellationToken))
      {
        return GetUserByIdAsync(request, new grpc::CallOptions(headers, deadline, cancellationToken));
      }
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      public virtual grpc::AsyncUnaryCall<global::VirtuManager.Auth.Contracts.Users.User> GetUserByIdAsync(global::VirtuManager.Auth.Contracts.Users.GetUserByIdRequest request, grpc::CallOptions options)
      {
        return CallInvoker.AsyncUnaryCall(__Method_GetUserById, null, options, request);
      }
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      public virtual global::VirtuManager.Auth.Contracts.Users.User GetUserByLogin(global::VirtuManager.Auth.Contracts.Users.GetUserByLoginRequest request, grpc::Metadata headers = null, global::System.DateTime? deadline = null, global::System.Threading.CancellationToken cancellationToken = default(global::System.Threading.CancellationToken))
      {
        return GetUserByLogin(request, new grpc::CallOptions(headers, deadline, cancellationToken));
      }
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      public virtual global::VirtuManager.Auth.Contracts.Users.User GetUserByLogin(global::VirtuManager.Auth.Contracts.Users.GetUserByLoginRequest request, grpc::CallOptions options)
      {
        return CallInvoker.BlockingUnaryCall(__Method_GetUserByLogin, null, options, request);
      }
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      public virtual grpc::AsyncUnaryCall<global::VirtuManager.Auth.Contracts.Users.User> GetUserByLoginAsync(global::VirtuManager.Auth.Contracts.Users.GetUserByLoginRequest request, grpc::Metadata headers = null, global::System.DateTime? deadline = null, global::System.Threading.CancellationToken cancellationToken = default(global::System.Threading.CancellationToken))
      {
        return GetUserByLoginAsync(request, new grpc::CallOptions(headers, deadline, cancellationToken));
      }
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      public virtual grpc::AsyncUnaryCall<global::VirtuManager.Auth.Contracts.Users.User> GetUserByLoginAsync(global::VirtuManager.Auth.Contracts.Users.GetUserByLoginRequest request, grpc::CallOptions options)
      {
        return CallInvoker.AsyncUnaryCall(__Method_GetUserByLogin, null, options, request);
      }
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      public virtual global::VirtuManager.Auth.Contracts.Users.GetUsersResponse GetUsers(global::VirtuManager.Auth.Contracts.Users.GetUsersRequest request, grpc::Metadata headers = null, global::System.DateTime? deadline = null, global::System.Threading.CancellationToken cancellationToken = default(global::System.Threading.CancellationToken))
      {
        return GetUsers(request, new grpc::CallOptions(headers, deadline, cancellationToken));
      }
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      public virtual global::VirtuManager.Auth.Contracts.Users.GetUsersResponse GetUsers(global::VirtuManager.Auth.Contracts.Users.GetUsersRequest request, grpc::CallOptions options)
      {
        return CallInvoker.BlockingUnaryCall(__Method_GetUsers, null, options, request);
      }
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      public virtual grpc::AsyncUnaryCall<global::VirtuManager.Auth.Contracts.Users.GetUsersResponse> GetUsersAsync(global::VirtuManager.Auth.Contracts.Users.GetUsersRequest request, grpc::Metadata headers = null, global::System.DateTime? deadline = null, global::System.Threading.CancellationToken cancellationToken = default(global::System.Threading.CancellationToken))
      {
        return GetUsersAsync(request, new grpc::CallOptions(headers, deadline, cancellationToken));
      }
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      public virtual grpc::AsyncUnaryCall<global::VirtuManager.Auth.Contracts.Users.GetUsersResponse> GetUsersAsync(global::VirtuManager.Auth.Contracts.Users.GetUsersRequest request, grpc::CallOptions options)
      {
        return CallInvoker.AsyncUnaryCall(__Method_GetUsers, null, options, request);
      }
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      public virtual global::VirtuManager.Auth.Contracts.Users.GetUsersPagedResponse GetUsersPaged(global::VirtuManager.Auth.Contracts.Users.GetUsersPagedRequest request, grpc::Metadata headers = null, global::System.DateTime? deadline = null, global::System.Threading.CancellationToken cancellationToken = default(global::System.Threading.CancellationToken))
      {
        return GetUsersPaged(request, new grpc::CallOptions(headers, deadline, cancellationToken));
      }
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      public virtual global::VirtuManager.Auth.Contracts.Users.GetUsersPagedResponse GetUsersPaged(global::VirtuManager.Auth.Contracts.Users.GetUsersPagedRequest request, grpc::CallOptions options)
      {
        return CallInvoker.BlockingUnaryCall(__Method_GetUsersPaged, null, options, request);
      }
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      public virtual grpc::AsyncUnaryCall<global::VirtuManager.Auth.Contracts.Users.GetUsersPagedResponse> GetUsersPagedAsync(global::VirtuManager.Auth.Contracts.Users.GetUsersPagedRequest request, grpc::Metadata headers = null, global::System.DateTime? deadline = null, global::System.Threading.CancellationToken cancellationToken = default(global::System.Threading.CancellationToken))
      {
        return GetUsersPagedAsync(request, new grpc::CallOptions(headers, deadline, cancellationToken));
      }
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      public virtual grpc::AsyncUnaryCall<global::VirtuManager.Auth.Contracts.Users.GetUsersPagedResponse> GetUsersPagedAsync(global::VirtuManager.Auth.Contracts.Users.GetUsersPagedRequest request, grpc::CallOptions options)
      {
        return CallInvoker.AsyncUnaryCall(__Method_GetUsersPaged, null, options, request);
      }
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      public virtual global::VirtuManager.Auth.Contracts.Users.CreateUserResponse CreateUser(global::VirtuManager.Auth.Contracts.Users.CreateUserRequest request, grpc::Metadata headers = null, global::System.DateTime? deadline = null, global::System.Threading.CancellationToken cancellationToken = default(global::System.Threading.CancellationToken))
      {
        return CreateUser(request, new grpc::CallOptions(headers, deadline, cancellationToken));
      }
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      public virtual global::VirtuManager.Auth.Contracts.Users.CreateUserResponse CreateUser(global::VirtuManager.Auth.Contracts.Users.CreateUserRequest request, grpc::CallOptions options)
      {
        return CallInvoker.BlockingUnaryCall(__Method_CreateUser, null, options, request);
      }
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      public virtual grpc::AsyncUnaryCall<global::VirtuManager.Auth.Contracts.Users.CreateUserResponse> CreateUserAsync(global::VirtuManager.Auth.Contracts.Users.CreateUserRequest request, grpc::Metadata headers = null, global::System.DateTime? deadline = null, global::System.Threading.CancellationToken cancellationToken = default(global::System.Threading.CancellationToken))
      {
        return CreateUserAsync(request, new grpc::CallOptions(headers, deadline, cancellationToken));
      }
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      public virtual grpc::AsyncUnaryCall<global::VirtuManager.Auth.Contracts.Users.CreateUserResponse> CreateUserAsync(global::VirtuManager.Auth.Contracts.Users.CreateUserRequest request, grpc::CallOptions options)
      {
        return CallInvoker.AsyncUnaryCall(__Method_CreateUser, null, options, request);
      }
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      public virtual global::VirtuManager.Auth.Contracts.Users.User UpdateUser(global::VirtuManager.Auth.Contracts.Users.UpdateUserRequest request, grpc::Metadata headers = null, global::System.DateTime? deadline = null, global::System.Threading.CancellationToken cancellationToken = default(global::System.Threading.CancellationToken))
      {
        return UpdateUser(request, new grpc::CallOptions(headers, deadline, cancellationToken));
      }
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      public virtual global::VirtuManager.Auth.Contracts.Users.User UpdateUser(global::VirtuManager.Auth.Contracts.Users.UpdateUserRequest request, grpc::CallOptions options)
      {
        return CallInvoker.BlockingUnaryCall(__Method_UpdateUser, null, options, request);
      }
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      public virtual grpc::AsyncUnaryCall<global::VirtuManager.Auth.Contracts.Users.User> UpdateUserAsync(global::VirtuManager.Auth.Contracts.Users.UpdateUserRequest request, grpc::Metadata headers = null, global::System.DateTime? deadline = null, global::System.Threading.CancellationToken cancellationToken = default(global::System.Threading.CancellationToken))
      {
        return UpdateUserAsync(request, new grpc::CallOptions(headers, deadline, cancellationToken));
      }
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      public virtual grpc::AsyncUnaryCall<global::VirtuManager.Auth.Contracts.Users.User> UpdateUserAsync(global::VirtuManager.Auth.Contracts.Users.UpdateUserRequest request, grpc::CallOptions options)
      {
        return CallInvoker.AsyncUnaryCall(__Method_UpdateUser, null, options, request);
      }
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      public virtual global::Google.Protobuf.WellKnownTypes.Empty DeleteUser(global::VirtuManager.Auth.Contracts.Users.DeleteUserRequest request, grpc::Metadata headers = null, global::System.DateTime? deadline = null, global::System.Threading.CancellationToken cancellationToken = default(global::System.Threading.CancellationToken))
      {
        return DeleteUser(request, new grpc::CallOptions(headers, deadline, cancellationToken));
      }
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      public virtual global::Google.Protobuf.WellKnownTypes.Empty DeleteUser(global::VirtuManager.Auth.Contracts.Users.DeleteUserRequest request, grpc::CallOptions options)
      {
        return CallInvoker.BlockingUnaryCall(__Method_DeleteUser, null, options, request);
      }
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      public virtual grpc::AsyncUnaryCall<global::Google.Protobuf.WellKnownTypes.Empty> DeleteUserAsync(global::VirtuManager.Auth.Contracts.Users.DeleteUserRequest request, grpc::Metadata headers = null, global::System.DateTime? deadline = null, global::System.Threading.CancellationToken cancellationToken = default(global::System.Threading.CancellationToken))
      {
        return DeleteUserAsync(request, new grpc::CallOptions(headers, deadline, cancellationToken));
      }
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      public virtual grpc::AsyncUnaryCall<global::Google.Protobuf.WellKnownTypes.Empty> DeleteUserAsync(global::VirtuManager.Auth.Contracts.Users.DeleteUserRequest request, grpc::CallOptions options)
      {
        return CallInvoker.AsyncUnaryCall(__Method_DeleteUser, null, options, request);
      }
      /// <summary>Creates a new instance of client from given <c>ClientBaseConfiguration</c>.</summary>
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      protected override UserServiceClient NewInstance(ClientBaseConfiguration configuration)
      {
        return new UserServiceClient(configuration);
      }
    }

    /// <summary>Creates service definition that can be registered with a server</summary>
    /// <param name="serviceImpl">An object implementing the server-side handling logic.</param>
    [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
    public static grpc::ServerServiceDefinition BindService(UserServiceBase serviceImpl)
    {
      return grpc::ServerServiceDefinition.CreateBuilder()
          .AddMethod(__Method_GetUserById, serviceImpl.GetUserById)
          .AddMethod(__Method_GetUserByLogin, serviceImpl.GetUserByLogin)
          .AddMethod(__Method_GetUsers, serviceImpl.GetUsers)
          .AddMethod(__Method_GetUsersPaged, serviceImpl.GetUsersPaged)
          .AddMethod(__Method_CreateUser, serviceImpl.CreateUser)
          .AddMethod(__Method_UpdateUser, serviceImpl.UpdateUser)
          .AddMethod(__Method_DeleteUser, serviceImpl.DeleteUser).Build();
    }

    /// <summary>Register service method with a service binder with or without implementation. Useful when customizing the service binding logic.
    /// Note: this method is part of an experimental API that can change or be removed without any prior notice.</summary>
    /// <param name="serviceBinder">Service methods will be bound by calling <c>AddMethod</c> on this object.</param>
    /// <param name="serviceImpl">An object implementing the server-side handling logic.</param>
    [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
    public static void BindService(grpc::ServiceBinderBase serviceBinder, UserServiceBase serviceImpl)
    {
      serviceBinder.AddMethod(__Method_GetUserById, serviceImpl == null ? null : new grpc::UnaryServerMethod<global::VirtuManager.Auth.Contracts.Users.GetUserByIdRequest, global::VirtuManager.Auth.Contracts.Users.User>(serviceImpl.GetUserById));
      serviceBinder.AddMethod(__Method_GetUserByLogin, serviceImpl == null ? null : new grpc::UnaryServerMethod<global::VirtuManager.Auth.Contracts.Users.GetUserByLoginRequest, global::VirtuManager.Auth.Contracts.Users.User>(serviceImpl.GetUserByLogin));
      serviceBinder.AddMethod(__Method_GetUsers, serviceImpl == null ? null : new grpc::UnaryServerMethod<global::VirtuManager.Auth.Contracts.Users.GetUsersRequest, global::VirtuManager.Auth.Contracts.Users.GetUsersResponse>(serviceImpl.GetUsers));
      serviceBinder.AddMethod(__Method_GetUsersPaged, serviceImpl == null ? null : new grpc::UnaryServerMethod<global::VirtuManager.Auth.Contracts.Users.GetUsersPagedRequest, global::VirtuManager.Auth.Contracts.Users.GetUsersPagedResponse>(serviceImpl.GetUsersPaged));
      serviceBinder.AddMethod(__Method_CreateUser, serviceImpl == null ? null : new grpc::UnaryServerMethod<global::VirtuManager.Auth.Contracts.Users.CreateUserRequest, global::VirtuManager.Auth.Contracts.Users.CreateUserResponse>(serviceImpl.CreateUser));
      serviceBinder.AddMethod(__Method_UpdateUser, serviceImpl == null ? null : new grpc::UnaryServerMethod<global::VirtuManager.Auth.Contracts.Users.UpdateUserRequest, global::VirtuManager.Auth.Contracts.Users.User>(serviceImpl.UpdateUser));
      serviceBinder.AddMethod(__Method_DeleteUser, serviceImpl == null ? null : new grpc::UnaryServerMethod<global::VirtuManager.Auth.Contracts.Users.DeleteUserRequest, global::Google.Protobuf.WellKnownTypes.Empty>(serviceImpl.DeleteUser));
    }

  }
}
#endregion
