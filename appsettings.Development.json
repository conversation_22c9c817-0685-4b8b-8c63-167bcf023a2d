{"Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning"}}, "ConnectionStrings": {"DefaultConnection": "server=localhost;uid=virtumanager_user;pwd=*****;database=virtumanager_db"}, "Auth": {"Issuer": "Issuer", "Audience": "Audience", "Key": "bc052bbeeac7f7fa804b71979d30252d698d7d7b9b41bdbb510e9ab42d8362f9"}, "MongoDb": {"ConnectionString": "********************************************************************************************************************************", "DatabaseName": "virtumanager_logs", "CollectionName": "logs"}, "Kafka": {"VirtualMachineTask": {"BootstrapServers": "***********:9092", "Topic": "virtual-machine-task-created-topic", "GroupId": "virtual-machine-task-created"}, "BridgeTask": {"BootstrapServers": "***********:9092", "Topic": "bridge-task-created-topic", "GroupId": "bridge-task-created"}}, "InfluxDB": {"Token": "jXWNLyyZT49PqLBlUj7YwsZl4PCOn8X3Wr2iUqd6lYWGGqw5uCO-LaKyohaupNZzSTV-7kJJPOQ1RNliMgbV3A==", "Host": "http://localhost:8086/", "Username": "admin", "Password": "adminadmin1", "Organization": "VirtuManager", "Bucket": "VirtuManager"}}