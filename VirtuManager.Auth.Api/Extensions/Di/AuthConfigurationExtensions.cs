using Microsoft.IdentityModel.Tokens;
using virtu_manager_auth.Auth;

namespace virtu_manager_auth.Extensions;

public static class AuthConfigurationExtensions
{
    public static TokenValidationParameters GetTokenValidationParameters(this AuthConfiguration authConfiguration)
    {
        return new TokenValidationParameters
        {
            ValidateIssuer = true,
            ValidIssuer = authConfiguration.Issuer,
            ValidateAudience = true,
            ValidAudience = authConfiguration.Audience,
            ValidateLifetime = true,
            IssuerSigningKey = authConfiguration.GetSymmetricSecurityKey(),
            ValidateIssuerSigningKey = true,
            ClockSkew = TimeSpan.Zero
        };
    }
}