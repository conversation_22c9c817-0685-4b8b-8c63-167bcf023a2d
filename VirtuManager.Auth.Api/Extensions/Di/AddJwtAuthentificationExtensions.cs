using Microsoft.AspNetCore.Authentication.JwtBearer;
using virtu_manager_auth.Auth;

namespace virtu_manager_auth.Extensions;

public static class AddJwtAuthentificationExtensions
{
    public static IServiceCollection AddJwtAuthentication(this IServiceCollection services,
        IConfiguration configuration)
    {
        services.AddSingleton(new AuthOptions
        {
            Issuer = configuration["Auth:Issuer"],
            Audience = configuration["Auth:Audience"],
            Key = configuration["Auth:Key"],
            AccessTokenLifetime =
                configuration.GetSection("Auth").GetSection("TokenExpiration").GetValue<int>("Access"),
            RefreshTokenLifetime =
                configuration.GetSection("Auth").GetSection("TokenExpiration").GetValue<int>("Refresh")
        });
     
        services.AddSingleton<AuthConfiguration>();

        services
            .AddAuthentication(JwtBearerDefaults.AuthenticationScheme)
            .AddJwtBearer(options =>
            {
                using var serviceProvider = services.BuildServiceProvider();

                var authConfig = serviceProvider.GetRequiredService<AuthConfiguration>();
                options.TokenValidationParameters = authConfig.GetTokenValidationParameters();
            });
        
        return services;
    }
}