using Elastic.Ingest.Elasticsearch;
using Elastic.Ingest.Elasticsearch.DataStreams;
using Elastic.Serilog.Sinks;
using Elastic.Transport;
using Serilog;
using Serilog.Events;

namespace virtu_manager_auth.Extensions;

public static class SerilogExtensions
{
    public static IHostBuilder UseElasticsearchSerilog(this IHostBuilder hostBuilder)
    {
        return hostBuilder.UseSerilog((context, _, configuration) =>
        {
            var elasticConfig = context.Configuration.GetSection("Elastic");
            var elasticUri = elasticConfig["Uri"] ?? "http://localhost:9200";
            var elasticUsername = elasticConfig["Username"];
            var elasticPassword = elasticConfig["Password"];

            var dataStreamNamespace = elasticConfig.GetSection("DataStream")["Namespace"] ?? "logs";
            var dataStreamDataset = elasticConfig.GetSection("DataStream")["Dataset"] ?? "teamflow-task";
            var dataStreamType = elasticConfig.GetSection("DataStream")["Type"] ?? "api";

            configuration
                .MinimumLevel.Information()
                .MinimumLevel.Override("Microsoft", LogEventLevel.Information)
                .MinimumLevel.Override("System", LogEventLevel.Warning)
                .Enrich.FromLogContext()
                .WriteTo.Console()
                .WriteTo.Elasticsearch([new Uri(elasticUri)], options =>
                {
                    options.DataStream = new DataStreamName(dataStreamNamespace, dataStreamDataset, dataStreamType);
                    options.BootstrapMethod = BootstrapMethod.Failure;
                }, transport =>
                {
                    if (!string.IsNullOrEmpty(elasticUsername) && !string.IsNullOrEmpty(elasticPassword))
                    {
                        transport.Authentication(new BasicAuthentication(elasticUsername, elasticPassword));
                    }
                });
        });
    }
}