using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Storage;
using VirtuManager.Auth.Domain.Repositories;
using VirtuManager.Auth.Infrastructure.Database;

namespace virtu_manager_auth.Repositories;

public abstract class AbstractRepository<T> : IRepository<T>
{
    protected readonly ApplicationDbContext ApplicationDbContext;

    protected AbstractRepository(ApplicationDbContext applicationDbContext)
    {
        ArgumentNullException.ThrowIfNull(applicationDbContext);

        ApplicationDbContext = applicationDbContext;
    }

    public abstract Task Add(T entity);

    public abstract Task<List<T>> GetAll();

    public abstract Task Remove(T entity);

    public abstract Task Update(T entity);

    public async Task<IDbContextTransaction> BeginTransactionAsync()
        => await ApplicationDbContext.Database.BeginTransactionAsync();
    
    protected virtual async Task<(IEnumerable<T> items, long totalCount)> GetPaged(int pageNumber, int pageSize, IQueryable<T> query)
    {
        long totalCount = await query.CountAsync();
		
        var items = await query
            .Skip((pageNumber - 1) * pageSize)
            .Take(pageSize)
            .ToListAsync();
	
        return (items, totalCount);
    }
}