using System.IdentityModel.Tokens.Jwt;
using System.Security.Claims;
using Microsoft.IdentityModel.Tokens;
using virtu_manager_auth.Auth;
using virtu_manager_auth.Models;
using VirtuManager.Auth.Api.Auth.Interfaces;
using VirtuManager.Auth.Api.Extensions.Di;
using VirtuManager.Auth.Domain.Enums;

namespace VirtuManager.Auth.Api.Auth;

public class JwtService : IJwtService
{
    private readonly AuthConfiguration _configuration;
    private readonly JwtSecurityTokenHandler _tokenHandler;

    public JwtService(AuthConfiguration configuration)
    {
        ArgumentNullException.ThrowIfNull(configuration);
        _configuration = configuration;

        _tokenHandler = new JwtSecurityTokenHandler();
    }

    public string GenerateAccessToken(TokensPayload payload)
    {
        var expires = DateTime.UtcNow.AddMinutes(_configuration.AccessTokenLifetime);
        return GenerateJwtToken(payload, JwtClaimValue.Access.ToString(), expires);
    }

    public string GenerateRefreshToken(TokensPayload payload)
    {
        var expires = DateTime.UtcNow.AddDays(_configuration.RefreshTokenLifetime);
        return GenerateJwtToken(payload, JwtClaimValue.Refresh.ToString(), expires);
    }

    public ClaimsPrincipal? ValidateToken(string token)
    {
        try
        {
            var principal = _tokenHandler
                .ValidateToken(token, _configuration
                    .GetTokenValidationParameters(), out _);
            return principal;
        }
        catch
        {
            return null;
        }
    }

    private string GenerateJwtToken(TokensPayload payload, string tokenTypeClaimValue, DateTime expires)
    {
        var claims = new List<Claim>
        {
            new(nameof(JwtClaimTypes.Id), payload.Id.ToString()),
            new(ClaimTypes.Name, payload.Login),
            new(ClaimTypes.Role, payload.Role.ToString()),
            new(nameof(JwtClaimTypes.TokenType), tokenTypeClaimValue)
        };

        var jwt = new JwtSecurityToken(
            issuer: _configuration.Issuer,
            audience: _configuration.Audience,
            claims: claims,
            expires: expires,
            signingCredentials: new SigningCredentials(_configuration.GetSymmetricSecurityKey(),
                SecurityAlgorithms.HmacSha256)
        );

        return _tokenHandler.WriteToken(jwt);
    }
}