using Grpc.Core;
using Microsoft.AspNetCore.Authorization;
using virtu_manager_auth.Mapping;
using VirtuManager.Auth;
using VirtuManager.Auth.Api.Services.Interfaces;

namespace virtu_manager_auth.Services.Grpc;

[Authorize]
public class GetUserByGuidService : GrpcUserService.GrpcUserServiceBase
{
    private readonly IGetUsersService _getUsersService;

    public GetUserByGuidService(IGetUsersService getUsersService)
    {
        _getUsersService = getUsersService ?? throw new ArgumentNullException(nameof(getUsersService));
    }

    public override async Task<User> GetUserByGuid(GetUserByGuidRequest request, ServerCallContext context)
    {
        if (!Guid.TryParse(request.Guid, out var guid)) 
            throw new RpcException(new Status(StatusCode.InvalidArgument, "Invalid Guid"));

        var user = await _getUsersService.GetById(guid);
        
        return user.ToGrpcModel();
    }
}