using virtu_manager_auth.Api.Presets.Paged;
using virtu_manager_auth.Api.ViewModels;

namespace VirtuManager.Auth.Api.Services.Interfaces;

public interface IGetUsersService
{
    Task<IEnumerable<UserViewModel>> GetAll(CancellationToken cancellationToken = default);
    
    Task<UserViewModel> GetById(Guid id, CancellationToken cancellationToken = default);
    
    Task<UserViewModel> GetByLogin(string login, CancellationToken cancellationToken = default);
    
    Task<PaginationResponse<UserViewModel>> GetPaged(int pageNumber, int pageSize, CancellationToken cancellationToken = default);
}