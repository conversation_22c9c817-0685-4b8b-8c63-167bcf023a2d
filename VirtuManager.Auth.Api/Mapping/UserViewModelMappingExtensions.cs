using VirtuManager.Auth.Application.ViewModels;

namespace virtu_manager_auth.Mapping;

public static class UserViewModelMappingExtensions
{
    public static virtu_manager_auth_service.User ToGrpcModel(this UserViewModel userViewModel)
    {
        return new virtu_manager_auth_service.User
        {
            Guid = userViewModel.Id.ToString(),
            Login = userViewModel.Login,
            Email = userViewModel.Email,
            Role = (virtu_manager_auth_service.UserRole)userViewModel.Role,
        };
    }
}