using VirtuManager.Auth;
using VirtuManager.Auth.Application.ViewModels;

namespace virtu_manager_auth.Mapping;

public static class UserViewModelMappingExtensions
{
    public static User ToGrpcModel(this UserViewModel userViewModel)
    {
        return new User
        {
            Guid = userViewModel.Id.ToString(),
            Login = userViewModel.Login,
            Email = userViewModel.Email,
            Role = (UserRole)userViewModel.Role,
        };
    }
}