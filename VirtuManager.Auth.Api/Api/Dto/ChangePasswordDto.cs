using System.ComponentModel.DataAnnotations;

namespace VirtuManager.Auth.Api.Api.Dto;

public record ChangePasswordDto
{
    [Required]
    public string CurrentPassword { get; init; } = string.Empty;
    
    [Required]
    [MinLength(8)]
    public string NewPassword { get; init; } = string.Empty;
    
    [Required]
    [MinLength(8)]
    public string ConfirmNewPassword { get; init; } = string.Empty;
}
