using System.ComponentModel.DataAnnotations;
using VirtuManager.Auth.Domain.Enums;

namespace VirtuManager.Auth.Api.Api.Dto;

public record CreateUserDto
{
    [Required]
    public string Login { get; init; } = string.Empty;

    [Required]
    public string Email { get; init; } = string.Empty;

    [Required]
    [MinLength(8)]
    public string Password { get; init; } = string.Empty;

    public UserRole? Role { get; init; }
}
