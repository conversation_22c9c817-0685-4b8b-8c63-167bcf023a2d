<Project Sdk="Microsoft.NET.Sdk.Web">

    <PropertyGroup>
        <TargetFramework>net9.0</TargetFramework>
        <Nullable>enable</Nullable>
        <ImplicitUsings>enable</ImplicitUsings>
        <DockerDefaultTargetOS>Linux</DockerDefaultTargetOS>
    </PropertyGroup>

    <ItemGroup>
      <Content Include="..\.dockerignore">
        <Link>.dockerignore</Link>
      </Content>
    </ItemGroup>

    <ItemGroup>
      <PackageReference Include="Ocelot" Version="24.0.0" />
      <PackageReference Include="Ocelot.Provider.Polly" Version="24.0.0" />
      <PackageReference Include="Polly" Version="8.5.2" />
    </ItemGroup>

</Project>
